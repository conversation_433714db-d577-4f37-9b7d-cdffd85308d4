package com.siact.energy.cal.server.controller.ruleCol.flow;

import cn.hutool.json.JSONArray;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.server.service.flow.IApiRtsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-11
 * @Description: 对外暴露接口
 * @Version: 1.0
 */
@RestController
@RequestMapping("rts")
@ApiSort(140)
@Api(tags = "api接口调用")
public class ApiRtsController {

    @Resource
    IApiRtsService apiRtsService;

    @ApiOperation("请求数据服务")
    @RequestMapping(value = "getApiResult/{url}/**", method = RequestMethod.POST)
    public R<JSONArray> apiPost(@PathVariable("url") String url, @RequestBody(required = false) String body) throws ExecutionException, InterruptedException {
        return apiRtsService.apiPost(url, body);
    }

}
