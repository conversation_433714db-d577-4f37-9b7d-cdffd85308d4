package com.siact.energy.cal.server.common.datasource.db.impl.tdengine;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DBATypeEnum;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.datasource.db.impl.DynamicDataSourceManager;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import com.siact.energy.cal.server.entity.energycal.DiffCal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service("tdengineOperator")
@Slf4j
public class TDengineOperator extends AbstractDbOperator {
    @Autowired
    private DynamicDataSourceManager dataSourceManager;
    @Autowired
    public TDengineOperator() {
        super(null);
    }

    @Override
    public Connection getConnection(DataSourceVo dataSourceVo) {
        try {
            // 1. 向管理器索要对应数据源的连接池
            DataSource dataSource = dataSourceManager.getDataSource(dataSourceVo);
            // 2. 从连接池中获取一个连接
            return dataSource.getConnection();
        } catch (SQLException e) {
            log.error("从动态数据源 [{}] 的连接池获取连接失败", dataSourceVo.getProjectCode(), e);
            throw new BizException("获取数据库连接失败");
        }
    }

    @Override
    public void executeBasicSql(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo) {
        long startTime = System.currentTimeMillis();

        // 分析SQL复杂度
        QueryComplexity complexity = analyzeSqlComplexity(sql);
        int queryTimeout = calculateQueryTimeout(complexity);
        int fetchSize = calculateOptimalFetchSize(complexity);

        log.debug("TDengine查询开始: 复杂度={}, 超时={}s, FetchSize={}",
                complexity.getLevel(), queryTimeout, fetchSize);

        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement()) {

            // 🔥 设置查询参数
            stmt.setQueryTimeout(queryTimeout);
            stmt.setFetchSize(fetchSize);

            // 🔥 暂时不添加查询提示，保持原始SQL
            String optimizedSql = sql;

            try (ResultSet rs = stmt.executeQuery(optimizedSql)) {
                int rowCount = 0;
                while (rs.next()) {
                    rowCount++;

                    // 每处理1000行记录一次进度
                    if (rowCount % 1000 == 0) {
                        log.debug("已处理 {} 行数据", rowCount);
                    }

                    // 获取时间戳并格式化
                    String ts = DateUtils.parse(rs.getString("ts"));
                    String property = rs.getString("devproperty");
                    String itemvalue = rs.getString("itemvalue");

                    if(StrUtil.isNotBlank(itemvalue)) {
                        try {
                            BigDecimal value = new BigDecimal(itemvalue);
                            resultMap.computeIfAbsent(property, k -> new ConcurrentHashMap<>())
                                    .put(ts, value);
                        } catch (NumberFormatException e) {
                            log.warn("数值转换失败: itemvalue={}, property={}, ts={}", itemvalue, property, ts);
                        }
                    }
                }

                long duration = System.currentTimeMillis() - startTime;
                log.debug("TDengine查询完成: 耗时={}ms, 处理行数={}, 结果指标数={}",
                        duration, rowCount, resultMap.size());

                // 记录慢查询
                if (duration > 5000) {
                    log.warn("TDengine慢查询: 耗时={}ms, 复杂度={}, SQL长度={}",
                            duration, complexity.getLevel(), sql.length());
                }
            }

        } catch (SQLException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("TDengine查询失败: 耗时={}ms, SQLState={}, ErrorCode={}, Message={}",
                    duration, e.getSQLState(), e.getErrorCode(), e.getMessage());

            // 🔥 根据错误类型提供更好的错误信息
            if (e.getMessage().contains("timeout") || e.getMessage().contains("超时")) {
                throw new BizException("TDengine查询超时，请减少查询复杂度或增加超时时间");
            } else if (e.getMessage().contains("connection") || e.getMessage().contains("连接")) {
                throw new BizException("TDengine连接异常，请检查网络连接");
            } else if (e.getMessage().contains("memory") || e.getMessage().contains("内存")) {
                throw new BizException("TDengine查询内存不足，请减少查询数据量");
            } else {
                throw new BizException("TDengine查询失败: " + e.getMessage());
            }
        }
    }

    /**
     * 分析SQL复杂度
     */
    private QueryComplexity analyzeSqlComplexity(String sql) {
        int metricCount = countMetricsInSql(sql);
        boolean hasInterval = sql.contains("INTERVAL(");
        boolean hasFill = sql.contains("FILL(");
        boolean hasUnion = sql.contains("UNION");
        long estimatedRows = estimateResultRows(sql);

        ComplexityLevel level;
        if (metricCount > 100 || estimatedRows > 50000 || (hasInterval && hasFill)) {
            level = ComplexityLevel.HIGH;
        } else if (metricCount > 50 || estimatedRows > 10000 || hasUnion) {
            level = ComplexityLevel.MEDIUM;
        } else {
            level = ComplexityLevel.LOW;
        }

        return new QueryComplexity(level, metricCount, hasInterval, hasFill, hasUnion, estimatedRows);
    }

    /**
     * 计算查询超时时间
     */
    private int calculateQueryTimeout(QueryComplexity complexity) {
        switch (complexity.getLevel()) {
            case HIGH:
                return 300; // 5分钟
            case MEDIUM:
                return 120; // 2分钟
            case LOW:
            default:
                return 60;  // 1分钟
        }
    }

    /**
     * 计算最优FetchSize
     */
    private int calculateOptimalFetchSize(QueryComplexity complexity) {
        switch (complexity.getLevel()) {
            case HIGH:
                return 500;  // 复杂查询使用较小的FetchSize
            case MEDIUM:
                return 1000;
            case LOW:
            default:
                return 2000; // 简单查询使用较大的FetchSize
        }
    }

    /**
     * 为TDengine优化SQL
     */
    private String optimizeSqlForTDengine(String sql, QueryComplexity complexity) {
        if (complexity.getLevel() == ComplexityLevel.HIGH) {
            // 对于复杂查询，可以添加TDengine特定的优化提示
            // 例如：添加索引提示、并行度提示等
            return "/*+ PARALLEL(4) */ " + sql;
        }
        return sql;
    }

    /**
     * 从SQL中统计指标数量
     */
    private int countMetricsInSql(String sql) {
        if (sql.contains("IN (")) {
            String inClause = sql.substring(sql.indexOf("IN (") + 4, sql.indexOf(")", sql.indexOf("IN (")));
            return inClause.split(",").length;
        }
        return 1;
    }

    /**
     * 估算结果行数
     */
    private long estimateResultRows(String sql) {
        int metricCount = countMetricsInSql(sql);
        if (sql.contains("INTERVAL(")) {
            // 等间隔查询，估算时间点数量
            return metricCount * 100L; // 简化估算
        } else {
            // 区间查询，每个指标最多2行
            return metricCount * 2L;
        }
    }

    /**
     * 查询复杂度枚举
     */
    private enum ComplexityLevel {
        LOW, MEDIUM, HIGH
    }

    /**
     * 查询复杂度信息
     */
    private static class QueryComplexity {
        private final ComplexityLevel level;
        private final int metricCount;
        private final boolean hasInterval;
        private final boolean hasFill;
        private final boolean hasUnion;
        private final long estimatedRows;

        public QueryComplexity(ComplexityLevel level, int metricCount, boolean hasInterval,
                             boolean hasFill, boolean hasUnion, long estimatedRows) {
            this.level = level;
            this.metricCount = metricCount;
            this.hasInterval = hasInterval;
            this.hasFill = hasFill;
            this.hasUnion = hasUnion;
            this.estimatedRows = estimatedRows;
        }

        public ComplexityLevel getLevel() { return level; }
        public int getMetricCount() { return metricCount; }
        public boolean hasInterval() { return hasInterval; }
        public boolean hasFill() { return hasFill; }
        public boolean hasUnion() { return hasUnion; }
        public long getEstimatedRows() { return estimatedRows; }
    }

    @Override
    public void executeAggQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping) {
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                String ts = DateUtils.parse(rs.getString("ts"));
                String devproperty = rs.getString("devproperty");

                // 🔥【关键优化】使用 getObject() 防止 NULL 变成 0.0
                Object itemvalueObj = rs.getObject("itemvalue");

                if (itemvalueObj != null) {
                    String targetProp = propMapping.get(devproperty);
                    if (targetProp != null) {
                        try {
                            BigDecimal value = new BigDecimal(itemvalueObj.toString());
                            resultMap.computeIfAbsent(targetProp, k -> new ConcurrentHashMap<>())
                                    .put(ts, value);
                        } catch (NumberFormatException e) {
                            log.warn("聚合结果值 [{}] 无法转换为BigDecimal", itemvalueObj);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("执行聚合查询失败: SQL=[{}], Error={}", sql, e.getMessage(), e);
        }
    }



    @Override
    public Map<String, Long> executeCountQuery(String sql, DataSourceVo dataSourceVo) {
        Map<String, Long> counts = new HashMap<>();

        // 防御性编程：如果传入的SQL为空，直接返回空Map
        if (StrUtil.isBlank(sql)) {
            return counts;
        }

        // 使用 try-with-resources 确保资源自动关闭
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            // 遍历查询结果
            while (rs.next()) {
                try {
                    // 从结果集中获取指标编码和对应的计数值
                    String devproperty = rs.getString("devproperty");
                    long count = rs.getLong("count_val"); // 对应SQL中的别名 "count_val"

                    if (devproperty != null) {
                        counts.put(devproperty, count);
                    }
                } catch (SQLException e) {
                    // 如果单行解析失败，记录日志并继续处理下一行
                    log.warn("解析计数查询结果集单行失败: {}", e.getMessage());
                }
            }
        } catch (SQLException e) {
            log.error("执行计数查询SQL失败: [{}], 错误: {}", sql, e.getMessage(), e);
            // 如果整个查询失败，返回一个空的Map。
            // 这会导致上层逻辑（findIncompleteIndicators）认为所有指标的点数都为0，
            // 从而将它们都标记为需要重算。这是一种安全的降级策略。
        }

        log.debug("计数查询完成，共返回 {} 个指标的计数值。", counts.size());
        return counts;
    }
    @Override
    public void executeDiffQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo, Map<String, String> propMapping) {
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            List<DiffCal> list = new ArrayList<>();
            while (rs.next()) {
                String ts = DateUtils.parse(rs.getString("ts"));
                String itemvalue = rs.getString("itemvalue");
                String devProp = rs.getString("devproperty");
                String targetProp = propMapping.getOrDefault(devProp, null);
                if (StrUtil.isNotBlank(targetProp)) {
                    DiffCal diffCal = new DiffCal();
                    diffCal.setTs(ts);
                    diffCal.setTargetProp(targetProp);
                    diffCal.setItemvalue(itemvalue);
                    list.add(diffCal);
                }
            }
            //排序
            list.sort(Comparator.comparing(DiffCal::getTs));
            //分组并计算差值
            calculateDiffByDevproperty(resultMap, list);

        } catch (SQLException e) {
            log.error("Execute query error: {}", e.getMessage(), e);
            throw new BizException("Query execution failed");
        }
    }

    /*@Override
    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> dataCodes, DataSourceVo dataSourceVo) {

        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement()) {
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append(" insert into ");
            for (String devprop : dataCodes) {
                Map<String, BigDecimal> tsValueMap = resultMap.getOrDefault(devprop, null);
                if (!ObjectUtils.isEmpty(tsValueMap)) {
                    for (Map.Entry<String, BigDecimal> entryValue : tsValueMap.entrySet()) {
                        try {
                            String ts = entryValue.getKey();
                            BigDecimal itemvalue = entryValue.getValue();
                            sqlBuffer.append(devprop)
                                    .append(" using " + dataSourceVo.getTableName() + " tags ('" + null + "','" + null + "','" + devprop + "','" + null + "','" + null + "','" + null + "') (ts,itemvalue) values ('" + ts + "'," + itemvalue + ") ");
                            if (sqlBuffer.length() > 63 * 1024) {
                                String sql = sqlBuffer.toString();
                                if (sql.contains("using")) {
                                    log.debug(sql);
                                    stmt.addBatch(sql);
                                }
                                sqlBuffer = new StringBuffer();
                                sqlBuffer.append("insert into ");
                            }
                        } catch (SQLException e) {
                            log.error("数据入库时拼接sql异常" + e.getMessage());
                            throw new BizException("数据入库时拼接sql异常");
                        }
                    }
                }

            }
            try {
                String sql = sqlBuffer.toString();

                if (sql.contains("using")) {
                    log.debug(sql);
                    stmt.addBatch(sql);
                }
                int[] ints = stmt.executeBatch();

                if (ints.length != 0) {
                    log.info(dataSourceVo.getProjectCode() + "===项目数据批量插入成功");
                }
            } catch (SQLException e) {
                log.error("数据批量插入失败" + "========" + e.getMessage());
                throw new BizException("数据批量插入失败");

            } finally {
                DBConnection.close(conn);
            }

        } catch (SQLException e) {
            log.error("Execute query error: {}", e.getMessage(), e);
            throw new BizException("Query execution failed");
        }
    }
*/

    @Override
    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                           List<String> dataCodes,
                           DataSourceVo dataSourceVo) {

        // 🔥 1.【核心修改】使用 try-with-resources 自动管理 Connection 和 Statement
        // 无论后续逻辑是否成功，conn 和 stmt 都会在 try 块结束时被自动关闭。
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement()) {

            // 使用 StringBuilder 替代 StringBuffer，在单线程环境下性能更好
            StringBuilder sqlBuffer = new StringBuilder("INSERT INTO ");
            final int MAX_SQL_LENGTH = 60 * 1024; // 设置一个略小于64KB的阈值
            int batchCount = 0;

            for (String devprop : dataCodes) {
                Map<String, BigDecimal> tsValueMap = resultMap.get(devprop);
                if (tsValueMap == null || tsValueMap.isEmpty()) {
                    continue;
                }

                for (Map.Entry<String, BigDecimal> entryValue : tsValueMap.entrySet()) {
                    String ts = entryValue.getKey();
                    BigDecimal itemvalue = entryValue.getValue();

                    if (itemvalue == null) {
                        continue; // 跳过空值
                    }

                    // 🔥 2.【逻辑保留】沿用您原来的SQL拼接逻辑
                    // 注意：这里仍然存在SQL注入的风险，但我们遵照您的要求保留它。
                    // 强烈建议对 devprop, ts, itemvalue 进行严格的输入验证和清洗，防止恶意字符。
                    String singleInsert = String.format("%s USING %s TAGS (null, null, '%s', null, null, null) (ts, itemvalue) VALUES ('%s', %s) ",
                            devprop,
                            dataSourceVo.getTableName(),
                            devprop, // 假设devprop是安全的，没有单引号等特殊字符
                            ts,      // 假设ts是安全的，格式为 'yyyy-MM-dd HH:mm:ss'
                            itemvalue.toPlainString()); // 使用toPlainString避免科学计数法

                    // 🔥 3.【优化】改进批处理逻辑
                    // 如果拼接后的SQL长度将要超限，则先执行当前的批次
                    if (sqlBuffer.length() + singleInsert.length() > MAX_SQL_LENGTH && batchCount > 0) {
                        stmt.addBatch(sqlBuffer.toString());
                        stmt.executeBatch();

                        // 重置 buffer 和计数器
                        sqlBuffer.setLength(0);
                        sqlBuffer.append("INSERT INTO ");
                        batchCount = 0;
                    }

                    sqlBuffer.append(singleInsert);
                    batchCount++;
                }
            }

            // 🔥 4.【收尾】处理最后一批SQL语句
            // 检查sqlBuffer是否包含有效的INSERT语句
            if (batchCount > 0) {
                stmt.addBatch(sqlBuffer.toString());
                stmt.executeBatch();
            }

            log.info("项目 [{}] 数据批量插入成功。", dataSourceVo.getProjectCode());

        } catch (SQLException e) {
            log.error("数据批量插入时发生数据库异常: SQLState={}, ErrorCode={}, Message={}",
                    e.getSQLState(), e.getErrorCode(), e.getMessage(), e);
//            throw new BizException("数据批量插入时发生数据库异常", e);
        } catch (Exception e) {
            log.error("数据批量插入时发生未知异常: {}", e.getMessage(), e);
//            throw new BizException("数据批量插入失败", e);
        }
    }
    @Override
    public boolean testConnection(DataSourceVo testDTO) {
        try (Connection conn = getConnection(testDTO);
             Statement stmt = conn.createStatement()) {
            // TDengine 特有的服务器状态查询
            ResultSet rs = stmt.executeQuery("SHOW DATABASES");
            return rs != null && rs.next();
        } catch (SQLException e) {
            log.error("TDengine 连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }
    private void calculateDiffByDevproperty(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<DiffCal> list) {
        Map<String, List<DiffCal>> groupedData = list.stream()
                .collect(Collectors.groupingBy(DiffCal::getTargetProp));

        for (Map.Entry<String, List<DiffCal>> entry : groupedData.entrySet()) {
            List<DiffCal> dataPoints = entry.getValue();
            for (int i = 0; i < dataPoints.size() - 1; i++) {
                DiffCal current = dataPoints.get(i);
                DiffCal next = dataPoints.get(i + 1);
                BigDecimal diff = null;
                if (StrUtil.isNotBlank(current.getItemvalue()) && StrUtil.isNotBlank(next.getItemvalue())) {
                    BigDecimal nextValue = new BigDecimal(next.getItemvalue());
                    BigDecimal currValue = new BigDecimal(current.getItemvalue());

                    diff = nextValue.subtract(currValue);
                }

                if (ObjectUtil.isNotEmpty(diff)) {
                    resultMap.computeIfAbsent(current.getTargetProp(), k -> new ConcurrentHashMap<>())
                            .put(current.getTs(), diff);
                }

            }

        }
    }


}