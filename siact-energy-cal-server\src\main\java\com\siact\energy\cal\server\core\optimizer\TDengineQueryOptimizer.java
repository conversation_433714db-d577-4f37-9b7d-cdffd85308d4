package com.siact.energy.cal.server.core.optimizer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 🔥 TDengine查询优化器
 * 
 * 专门针对TDengine超时问题的优化策略：
 * 1. 智能分片：减少单次查询的数据量
 * 2. 时间分段：将长时间跨度拆分为小段
 * 3. 指标分批：减少IN子句中的指标数量
 * 4. 查询降级：复杂查询自动降级为简单查询
 */
@Slf4j
@Component
public class TDengineQueryOptimizer {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 优化阈值配置
    private static final int MAX_INDICATORS_PER_QUERY = 8;  // 单次查询最大指标数（从17降到8）
    private static final int MAX_DAYS_PER_QUERY = 7;        // 单次查询最大天数（从31降到7）
    private static final int MAX_CONCURRENT_QUERIES = 3;    // 最大并发查询数（从8降到3）
    private static final long QUERY_TIMEOUT_MS = 30000;     // 查询超时时间30秒
    
    /**
     * 🔥 优化聚合查询 - 主入口
     */
    public List<OptimizedQuery> optimizeAggregateQuery(List<String> indicators, 
                                                      String startTime, 
                                                      String endTime,
                                                      String aggregateFunction) {
        
        log.info("开始优化聚合查询: 指标数={}, 时间范围={} to {}, 聚合函数={}", 
                indicators.size(), startTime, endTime, aggregateFunction);
        
        List<OptimizedQuery> optimizedQueries = new ArrayList<>();
        
        // 1. 分析查询复杂度
        QueryComplexity complexity = analyzeQueryComplexity(indicators, startTime, endTime);
        log.info("查询复杂度分析: {}", complexity);
        
        // 2. 根据复杂度选择优化策略
        switch (complexity.level) {
            case LOW:
                // 低复杂度：直接查询
                optimizedQueries.add(createDirectQuery(indicators, startTime, endTime, aggregateFunction));
                break;
                
            case MEDIUM:
                // 中等复杂度：指标分批
                optimizedQueries.addAll(createIndicatorBatchQueries(indicators, startTime, endTime, aggregateFunction));
                break;
                
            case HIGH:
                // 高复杂度：指标分批 + 时间分段
                optimizedQueries.addAll(createFullOptimizedQueries(indicators, startTime, endTime, aggregateFunction));
                break;
                
            case EXTREME:
                // 极高复杂度：降级为简单查询
                optimizedQueries.addAll(createDegradedQueries(indicators, startTime, endTime, aggregateFunction));
                break;
        }
        
        log.info("查询优化完成: 原始1个查询 -> 优化后{}个查询", optimizedQueries.size());
        return optimizedQueries;
    }
    
    /**
     * 分析查询复杂度
     */
    private QueryComplexity analyzeQueryComplexity(List<String> indicators, String startTime, String endTime) {
        
        // 计算时间跨度（天）
        LocalDateTime start = LocalDateTime.parse(startTime, DATE_TIME_FORMATTER);
        LocalDateTime end = LocalDateTime.parse(endTime, DATE_TIME_FORMATTER);
        long daySpan = ChronoUnit.DAYS.between(start, end) + 1;
        
        // 计算复杂度分数
        int complexityScore = 0;
        
        // 指标数量权重
        complexityScore += indicators.size() * 2;
        
        // 时间跨度权重
        complexityScore += daySpan;
        
        // 数据量估算权重（指标数 × 天数）
        long estimatedDataPoints = indicators.size() * daySpan * 24; // 假设每小时一个数据点
        complexityScore += estimatedDataPoints / 1000; // 每1000个数据点加1分
        
        // 确定复杂度等级
        ComplexityLevel level;
        if (complexityScore <= 50) {
            level = ComplexityLevel.LOW;
        } else if (complexityScore <= 150) {
            level = ComplexityLevel.MEDIUM;
        } else if (complexityScore <= 500) {
            level = ComplexityLevel.HIGH;
        } else {
            level = ComplexityLevel.EXTREME;
        }
        
        return new QueryComplexity(level, complexityScore, indicators.size(), (int) daySpan, estimatedDataPoints);
    }
    
    /**
     * 创建直接查询（低复杂度）
     */
    private OptimizedQuery createDirectQuery(List<String> indicators, String startTime, String endTime, String aggregateFunction) {
        return new OptimizedQuery(
                QueryType.DIRECT,
                indicators,
                startTime,
                endTime,
                aggregateFunction,
                1,
                "直接查询"
        );
    }
    
    /**
     * 创建指标分批查询（中等复杂度）
     */
    private List<OptimizedQuery> createIndicatorBatchQueries(List<String> indicators, String startTime, String endTime, String aggregateFunction) {
        List<OptimizedQuery> queries = new ArrayList<>();
        
        // 按指标数量分批
        for (int i = 0; i < indicators.size(); i += MAX_INDICATORS_PER_QUERY) {
            int endIndex = Math.min(i + MAX_INDICATORS_PER_QUERY, indicators.size());
            List<String> batchIndicators = indicators.subList(i, endIndex);
            
            OptimizedQuery query = new OptimizedQuery(
                    QueryType.INDICATOR_BATCH,
                    batchIndicators,
                    startTime,
                    endTime,
                    aggregateFunction,
                    queries.size() + 1,
                    String.format("指标分批查询 %d/%d (%d个指标)", 
                            queries.size() + 1, 
                            (indicators.size() + MAX_INDICATORS_PER_QUERY - 1) / MAX_INDICATORS_PER_QUERY,
                            batchIndicators.size())
            );
            queries.add(query);
        }
        
        return queries;
    }
    
    /**
     * 创建完全优化查询（高复杂度）
     */
    private List<OptimizedQuery> createFullOptimizedQueries(List<String> indicators, String startTime, String endTime, String aggregateFunction) {
        List<OptimizedQuery> queries = new ArrayList<>();
        
        // 先按时间分段
        List<TimeSegment> timeSegments = createTimeSegments(startTime, endTime, MAX_DAYS_PER_QUERY);
        
        // 再按指标分批
        for (TimeSegment timeSegment : timeSegments) {
            for (int i = 0; i < indicators.size(); i += MAX_INDICATORS_PER_QUERY) {
                int endIndex = Math.min(i + MAX_INDICATORS_PER_QUERY, indicators.size());
                List<String> batchIndicators = indicators.subList(i, endIndex);
                
                OptimizedQuery query = new OptimizedQuery(
                        QueryType.FULL_OPTIMIZED,
                        batchIndicators,
                        timeSegment.startTime,
                        timeSegment.endTime,
                        aggregateFunction,
                        queries.size() + 1,
                        String.format("完全优化查询 %d (%d个指标, %s)", 
                                queries.size() + 1,
                                batchIndicators.size(),
                                timeSegment.description)
                );
                queries.add(query);
            }
        }
        
        return queries;
    }
    
    /**
     * 创建降级查询（极高复杂度）
     */
    private List<OptimizedQuery> createDegradedQueries(List<String> indicators, String startTime, String endTime, String aggregateFunction) {
        List<OptimizedQuery> queries = new ArrayList<>();
        
        log.warn("查询复杂度过高，启用降级策略：单指标查询");
        
        // 降级为单指标查询，时间分段
        List<TimeSegment> timeSegments = createTimeSegments(startTime, endTime, MAX_DAYS_PER_QUERY);
        
        for (String indicator : indicators) {
            for (TimeSegment timeSegment : timeSegments) {
                OptimizedQuery query = new OptimizedQuery(
                        QueryType.DEGRADED,
                        Collections.singletonList(indicator),
                        timeSegment.startTime,
                        timeSegment.endTime,
                        aggregateFunction,
                        queries.size() + 1,
                        String.format("降级查询 %d (单指标: %s, %s)",
                                queries.size() + 1,
                                indicator,
                                timeSegment.description)
                );
                queries.add(query);
            }
        }
        
        return queries;
    }
    
    /**
     * 创建时间分段
     */
    private List<TimeSegment> createTimeSegments(String startTime, String endTime, int maxDaysPerSegment) {
        List<TimeSegment> segments = new ArrayList<>();
        
        LocalDateTime start = LocalDateTime.parse(startTime, DATE_TIME_FORMATTER);
        LocalDateTime end = LocalDateTime.parse(endTime, DATE_TIME_FORMATTER);
        
        LocalDateTime currentStart = start;
        int segmentIndex = 1;
        
        while (currentStart.isBefore(end)) {
            LocalDateTime currentEnd = currentStart.plusDays(maxDaysPerSegment).minusSeconds(1);
            if (currentEnd.isAfter(end)) {
                currentEnd = end;
            }
            
            TimeSegment segment = new TimeSegment(
                    currentStart.format(DATE_TIME_FORMATTER),
                    currentEnd.format(DATE_TIME_FORMATTER),
                    String.format("时间段%d", segmentIndex++)
            );
            segments.add(segment);
            
            currentStart = currentEnd.plusSeconds(1);
        }
        
        return segments;
    }
    
    /**
     * 获取推荐的并发查询数
     */
    public int getRecommendedConcurrency(int totalQueries) {
        // 根据查询总数动态调整并发度
        if (totalQueries <= 3) {
            return totalQueries; // 少量查询可以全并发
        } else if (totalQueries <= 10) {
            return 3; // 中等数量查询限制并发
        } else {
            return 2; // 大量查询进一步限制并发
        }
    }
    
    // 数据类定义
    public static class OptimizedQuery {
        public final QueryType type;
        public final List<String> indicators;
        public final String startTime;
        public final String endTime;
        public final String aggregateFunction;
        public final int queryIndex;
        public final String description;
        
        public OptimizedQuery(QueryType type, List<String> indicators, String startTime, String endTime, 
                            String aggregateFunction, int queryIndex, String description) {
            this.type = type;
            this.indicators = indicators;
            this.startTime = startTime;
            this.endTime = endTime;
            this.aggregateFunction = aggregateFunction;
            this.queryIndex = queryIndex;
            this.description = description;
        }
    }
    
    public static class QueryComplexity {
        public final ComplexityLevel level;
        public final int score;
        public final int indicatorCount;
        public final int daySpan;
        public final long estimatedDataPoints;
        
        public QueryComplexity(ComplexityLevel level, int score, int indicatorCount, int daySpan, long estimatedDataPoints) {
            this.level = level;
            this.score = score;
            this.indicatorCount = indicatorCount;
            this.daySpan = daySpan;
            this.estimatedDataPoints = estimatedDataPoints;
        }
        
        @Override
        public String toString() {
            return String.format("复杂度=%s, 分数=%d, 指标数=%d, 天数=%d, 预估数据点=%d", 
                    level, score, indicatorCount, daySpan, estimatedDataPoints);
        }
    }
    
    public static class TimeSegment {
        public final String startTime;
        public final String endTime;
        public final String description;
        
        public TimeSegment(String startTime, String endTime, String description) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.description = description;
        }
    }
    
    public enum QueryType {
        DIRECT,           // 直接查询
        INDICATOR_BATCH,  // 指标分批查询
        FULL_OPTIMIZED,   // 完全优化查询
        DEGRADED          // 降级查询
    }
    
    public enum ComplexityLevel {
        LOW,      // 低复杂度
        MEDIUM,   // 中等复杂度
        HIGH,     // 高复杂度
        EXTREME   // 极高复杂度
    }
}
