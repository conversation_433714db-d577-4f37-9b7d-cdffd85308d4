package com.siact.energy.cal.server.common.datasource.db.impl.influxdb;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.siact.energy.cal.common.core.domain.RStatus;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DBATypeEnum;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Connection;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Package com.siact.energy.cal.server.common.datasource.db.impl.influxdb
 * @description: InfluxDB实现类
 * <AUTHOR>
 * @create 2024/11/29 16:09
 */
@Service("influxDBOperator")
@Slf4j
public class InfluxDBOperator extends AbstractDbOperator {
    private static final String DEFAULT_RETENTION_POLICY = "autogen";
    private static final ZoneId BEIJING_ZONE = ZoneId.of("Asia/Shanghai");
    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(BEIJING_ZONE);
    protected InfluxDBOperator(RedisUtil redisUtil) {
        super(redisUtil);
    }

    @Override
    public Connection getConnection(DataSourceVo dataSourceVo) {
        try {
            Integer dbType = dataSourceVo.getDbType();
            DBATypeEnum dbaTypeEnum = DBATypeEnum.getByIndex(dbType);
            // InfluxDB使用其专用客户端
            String url = String.format(dbaTypeEnum.getUrl(), dataSourceVo.getDatabaseIp(), dataSourceVo.getDatabasePort());
            // 创建InfluxDB客户端
            InfluxDBClient client = InfluxDBClientFactory.createV1(
                    url,
                    dataSourceVo.getUserName(),
                    dataSourceVo.getPassword().toCharArray(),
                    dataSourceVo.getDatabaseName(),
                    DEFAULT_RETENTION_POLICY
            );

            // 返回包装的Connection
            return new InfluxDBConnection(client);
        } catch (Exception e) {
            log.error("{}：{}", RStatus.CONNECT_ERROR.getMessage(), e.getMessage(), e);
            throw new BizException(RStatus.CONNECT_ERROR);
        }
    }
    /**
     * 执行基础查询
     */
    @Override
    public void executeBasicSql(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String flux,
            DataSourceVo dataSourceVo) {

        try (InfluxDBConnection conn = (InfluxDBConnection) getConnection(dataSourceVo)) {
            InfluxDBClient client = conn.getInfluxDBClient();
            QueryApi queryApi = client.getQueryApi();

            List<FluxTable> tables = queryApi.query(flux);

            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    String devproperty = record.getValueByKey("devproperty").toString();
                    // 转换时间为北京时间格式
                    String timestamp = formatInstant(record.getTime());
                    Double value = (Double) record.getValueByKey("_value");

                    ConcurrentHashMap<String, BigDecimal> timeValueMap =
                            resultMap.computeIfAbsent(devproperty, k -> new ConcurrentHashMap<>());
                    timeValueMap.put(timestamp, BigDecimal.valueOf(value));
                }
            }
        } catch (Exception e) {
            log.error("执行基础查询失败: {}", e.getMessage(), e);
            throw new BizException("执行查询失败: " + e.getMessage());
        }
    }

    /**
     * 执行聚合查询
     */
    @Override
    public void executeAggQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String flux,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping) {

        try (InfluxDBConnection conn = (InfluxDBConnection) getConnection(dataSourceVo)) {
            InfluxDBClient client = conn.getInfluxDBClient();
            QueryApi queryApi = client.getQueryApi();

            List<FluxTable> tables = queryApi.query(flux);

            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    String devproperty = record.getValueByKey("devproperty").toString();
                    String mappedProp = propMapping.get(devproperty);
                    // 转换时间为北京时间格式
                    String timestamp = formatInstant(record.getTime());
                    Double value = (Double) record.getValueByKey("_value");

                    ConcurrentHashMap<String, BigDecimal> timeValueMap =
                            resultMap.computeIfAbsent(mappedProp, k -> new ConcurrentHashMap<>());
                    timeValueMap.put(timestamp, BigDecimal.valueOf(value));
                }
            }
        } catch (Exception e) {
            log.error("执行聚合查询失败: {}", e.getMessage(), e);
            throw new BizException("执行聚合查询失败: " + e.getMessage());
        }
    }

    /**
     * 执行差值查询
     */
    @Override
    public void executeDiffQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String flux,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping) {

        try (InfluxDBConnection conn = (InfluxDBConnection) getConnection(dataSourceVo)) {
            InfluxDBClient client = conn.getInfluxDBClient();
            QueryApi queryApi = client.getQueryApi();

            List<FluxTable> tables = queryApi.query(flux);

            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    String devproperty = record.getValueByKey("devproperty").toString();
                    String mappedProp = propMapping.get(devproperty);
                    // 转换时间为北京时间格式
                    String timestamp = formatInstant(record.getTime());
                    Double value = (Double) record.getValueByKey("_value");

                    ConcurrentHashMap<String, BigDecimal> timeValueMap =
                            resultMap.computeIfAbsent(mappedProp, k -> new ConcurrentHashMap<>());
                    timeValueMap.put(timestamp, BigDecimal.valueOf(value));
                }
            }
        } catch (Exception e) {
            log.error("执行差值查询失败: {}", e.getMessage(), e);
            throw new BizException("执行差值查询失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Long> executeCountQuery(String sql, DataSourceVo dataSourceVo) {
        return null;
    }

    /**
     * 数据写入
     */
    @Override
    public void insertData(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            List<String> dataCodes,
            DataSourceVo dataSourceVo) {

        try (InfluxDBConnection conn = (InfluxDBConnection) getConnection(dataSourceVo)) {
            InfluxDBClient client = conn.getInfluxDBClient();
            WriteApiBlocking writeApi = client.getWriteApiBlocking();

            List<Point> points = new ArrayList<>();

            for (String dataCode : dataCodes) {
                ConcurrentHashMap<String, BigDecimal> timeValueMap = resultMap.get(dataCode);
                if (timeValueMap != null) {
                    for (Map.Entry<String, BigDecimal> entry : timeValueMap.entrySet()) {
                        // 将北京时间转换为UTC时间存储
                        Instant instant = parseDateTime(entry.getKey());
                        Point point = Point.measurement(dataSourceVo.getTableName())
                                .addTag("devproperty", dataCode)
                                .addField("itemvalue", entry.getValue().doubleValue())
                                .time(instant, WritePrecision.NS);
                        points.add(point);
                    }
                }
            }

            writeApi.writePoints(dataSourceVo.getDb(), "default", points);

        } catch (Exception e) {
            log.error("数据写入失败: {}", e.getMessage(), e);
            throw new BizException("数据写入失败: " + e.getMessage());
        }
    }

    /**
     * 格式化Instant为北京时间字符串
     */
    private String formatInstant(Instant instant) {
        return DATE_FORMATTER.format(instant);
    }

    /**
     * 解析北京时间字符串为Instant
     */
    private Instant parseDateTime(String dateTimeStr) {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return localDateTime.atZone(BEIJING_ZONE).toInstant();
        } catch (Exception e) {
            throw new BizException("时间格式转换失败: " + dateTimeStr);
        }
    }

    /**
     * 测试连接
     */
    @Override
    public boolean testConnection(DataSourceVo testDTO) {
        try (InfluxDBConnection conn = (InfluxDBConnection) getConnection(testDTO)) {
            InfluxDBClient influxDBClient = conn.getInfluxDBClient();
            // 执行一个简单的查询来测试连接
            String flux = "from(bucket:\"" + testDTO.getDb() + "\") |> range(start: -1m) |> limit(n:1)";
            influxDBClient.getQueryApi().query(flux);
            return true;
        } catch (Exception e) {
            log.error("连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
