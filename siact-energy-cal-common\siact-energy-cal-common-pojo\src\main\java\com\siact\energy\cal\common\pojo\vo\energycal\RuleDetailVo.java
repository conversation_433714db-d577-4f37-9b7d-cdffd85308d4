package com.siact.energy.cal.common.pojo.vo.energycal;/**
 * @Package com.siact.energycal.entity
 * @description:
 * <AUTHOR>
 * @create 2024/7/13 15:11
 */

import lombok.Data;

import java.util.Date;

/**
 * @ClassName RuleDetailVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/13 15:11
 * @Version 1.0
 **/
@Data
public class RuleDetailVo {

    /**
     * id
     */
    private Long id;
    /**
     * 指标名称
     */
    private String ruleName;
    /**
     * 指标描述
     */
    private String ruleDes;
    /**
     * 指标类型（0-实例级规则，1-模型级规则）
     */
    private Integer ruleType;
    /**
     * 计算类型（1-基础指标，2-聚合指标,3-衍生指标）
     */
    private Integer calType;
    /**
     * 节点（模型）编码
     */
    private String devCode;
    /**
     * 节点（模型）名称
     */
    private String devName;
    /**
     * 属性编码，模型编码
     */
    private String devProperty;
    /**
     * 属性（模型）名称
     */
    private String propName;
    /**
     * 公式表达式
     */
    private String ruleFormula;
    /**
     * 公式表达式
     */
    private String ruleFormulaShow;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 所属规则集id
     */
    private Long ruleColId;
    /**
     * 是否激活（0-激活,1-未激活）
     */
    private Integer activeState;
    /**
     * 创建者id
     */
//    private Long creator;
    /**
     * 创建时间
     */
//    private Date createTime;
    /**
     * 更新者
     */
//    private Long updater;
    /**
     * 更新时间
     */
//    private Date updateTime;
    /**
     * 删除标志(0-正常，1-已删除)
     */
    private Integer deleted;


}
