package com.siact.energy.cal.server.core.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;

@Service
@Slf4j
public class SystemMonitorService {
    
    private final OperatingSystemMXBean osBean;
    private final MemoryMXBean memoryBean;
    
    public SystemMonitorService() {
        this.osBean = ManagementFactory.getOperatingSystemMXBean();
        this.memoryBean = ManagementFactory.getMemoryMXBean();
    }
    
    /**
     * 获取CPU使用率
     */
    public double getCpuUsage() {
        try {
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = 
                    (com.sun.management.OperatingSystemMXBean) osBean;
                return sunOsBean.getProcessCpuLoad() * 100;
            }
            return osBean.getSystemLoadAverage();
        } catch (Exception e) {
            log.warn("获取CPU使用率失败", e);
            return 0.0;
        }
    }
    
    /**
     * 获取内存使用率
     */
    public double getMemoryUsage() {
        try {
            long used = memoryBean.getHeapMemoryUsage().getUsed();
            long max = memoryBean.getHeapMemoryUsage().getMax();
            return max > 0 ? (double) used / max * 100 : 0.0;
        } catch (Exception e) {
            log.warn("获取内存使用率失败", e);
            return 0.0;
        }
    }
    
    /**
     * 获取可用处理器数量
     */
    public int getAvailableProcessors() {
        return Runtime.getRuntime().availableProcessors();
    }
    
    /**
     * 判断系统是否过载
     */
    public boolean isSystemOverloaded() {
        double cpuUsage = getCpuUsage();
        double memoryUsage = getMemoryUsage();
        
        return cpuUsage > 80 || memoryUsage > 85;
    }
}