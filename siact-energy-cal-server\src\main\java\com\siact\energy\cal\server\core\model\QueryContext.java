package com.siact.energy.cal.server.core.model;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Data
@Builder
public class QueryContext {
    private TimeQueryDTO queryDTO;
    private DataSourceVo dataSourceVo;
    private List<String> indicators;
    private double systemCpuUsage;
    private double systemMemoryUsage;
    private int activeConnections;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 获取时间跨度（天数）
     */
    public long getTimeSpanDays() {
        LocalDateTime start = LocalDateTime.parse(queryDTO.getStartTime(), FORMATTER);
        LocalDateTime end = LocalDateTime.parse(queryDTO.getEndTime(), FORMATTER);
        return ChronoUnit.DAYS.between(start, end);
    }
    
    /**
     * 获取指标数量
     */
    public int getMetricCount() {
        return indicators != null ? indicators.size() : 0;
    }
    
    /**
     * 估算预期数据点数
     */
    public long getExpectedDataPoints() {
        if (!queryDTO.isEquallySpacedQuery()) {
            return getMetricCount(); // 区间查询每个指标只有一个点
        }
        
        long timeSpanMinutes = getTimeSpanDays() * 24 * 60;
        int intervalMinutes = convertToMinutes(queryDTO.getInterval(), queryDTO.getTsUnit());
        long pointsPerMetric = timeSpanMinutes / intervalMinutes;
        
        return pointsPerMetric * getMetricCount();
    }
    
    /**
     * 获取采样间隔（分钟）
     */
    public int getInterval() {
        return convertToMinutes(queryDTO.getInterval(), queryDTO.getTsUnit());
    }
    
    private int convertToMinutes(Integer interval, String unit) {
        if (interval == null || unit == null) return 1;
        
        switch (unit.toLowerCase()) {
            case "s": return interval / 60;
            case "m": return interval;
            case "h": return interval * 60;
            case "d": return interval * 24 * 60;
            default: return interval;
        }
    }
    
    /**
     * 判断系统负载是否过高
     */
    public boolean isSystemOverloaded() {
        return systemCpuUsage > 80 || systemMemoryUsage > 85 || activeConnections > 80;
    }
}