package com.siact.energy.cal.common.pojo.dto.energycal;

import cn.hutool.core.util.StrUtil;
import com.siact.energy.cal.common.pojo.enums.TimeQueryType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeQueryDTO {
    private String startTime;
    private String endTime;
    private List<String> dataCodes;
    
    // 采样参数，可选
    private Integer interval;
    private String tsUnit;
    
    // 查询类型
    TimeQueryType queryType;
    
    public boolean isEquallySpacedQuery() {
        return interval != null && StrUtil.isNotBlank(tsUnit);
    }



}