package com.siact.energy.cal.server.controller.dataSource;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.domain.ResponseCodeConstant;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceUpdateDTO;
import com.siact.energy.cal.common.pojo.enums.DBATypeEnum;
import com.siact.energy.cal.common.pojo.enums.DataSourceStatusEnum;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 数据源表(DataSource)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:09
 */
@Api(tags = {"数据源表"})
@ApiSort(20)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/data/source")
public class DataSourceController {

    private final DataSourceService dataSourceService;

    public static final String TAOS_JDBC_FORMAT = "jdbc:TAOS-RS://%s:%s/%s";

    /**
     * 数据源状态列表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "数据源状态列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 9)
    @GetMapping("/status/list")
    public R<List<SelectOptionVO>> listState() {
        return R.OK(DataSourceStatusEnum.list());
    }


    /**
     * 数据源状态列表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "数据源类型列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 10)
    @GetMapping("/dbType/list")
    public R<List<SelectOptionVO>> listDbType() {
        return R.OK(DBATypeEnum.list());
    }
    /**
     * 分页列表
     *
     * @param page               分页对象
     * @param dataSourceQueryDTO 查询实体
     * @return 查询结果
     */
    @ApiOperation(value = "分页列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 10, ignoreParameters = {"total", "pages", "records", "orders", "id"})
    @GetMapping("/listPage")
    public R<PageBean<DataSourceVO>> listPage(PageBean<DataSourceVO> page, DataSourceQueryDTO dataSourceQueryDTO) {
        return R.OK(dataSourceService.listPage(page, dataSourceQueryDTO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    @ApiOperation(value = "通过主键查询单条数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 20)
    @GetMapping("{id}")
    public R<DataSourceVO> selectOne(@PathVariable Serializable id) {
        return R.OK(dataSourceService.getVoById(id));
    }

    /**
     * 新增数据
     *
     * @param dataSourceInsertDTO 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSourceInsertDTO", value = "实体对象", dataType = "数据源表新增DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 30, ignoreParameters = {"id"})
    @PostMapping
    public R<Boolean> insert(@RequestBody @Validated DataSourceInsertDTO dataSourceInsertDTO) {
        fillData(dataSourceInsertDTO);
        return R.OK(dataSourceService.save(dataSourceInsertDTO));
    }

    /**
     * 填充数据
     *
     * @param dto 待填充对象
     */
    private void fillData(DataSourceInsertDTO dto) {
        if(Objects.nonNull(dto) && StringUtils.isBlank(dto.getJdbcUrl())) {
            dto.setJdbcUrl(String.format(TAOS_JDBC_FORMAT, dto.getDatabaseIp(), dto.getDatabasePort(), dto.getDb()));
        }
    }

    /**
     * 修改数据
     *
     * @param dataSourceUpdateDTO 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSourceUpdateDTO", value = "实体对象", dataType = "数据源表更新DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 40)
    @PutMapping
    public R<Boolean> update(@RequestBody @Validated(UpdateValidGroup.class) DataSourceUpdateDTO dataSourceUpdateDTO) {
        return R.OK(dataSourceService.updateVoById(dataSourceUpdateDTO));
    }

    /**
     * 删除数据
     *
     * @param ids 主键集合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 50)
    @DeleteMapping
    public R<Boolean> delete(@RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        return R.OK(dataSourceService.removeByIds(ids));
    }

    /**
     * 数据源测试
     *
     * @param dataSourceTestDTO 实体对象
     * @return 测试结果
     */
    @ApiOperation(value = "数据源测试")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSourceTestDTO", value = "实体对象", dataType = "数据源测试DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 60)
    @PostMapping("/dbTest")
    public R<Object> dbTest(@RequestBody @Validated DataSourceTestDTO dataSourceTestDTO) {
        if(Objects.nonNull(dataSourceTestDTO) && StringUtils.isBlank(dataSourceTestDTO.getJdbcUrl())) {
            dataSourceTestDTO.setJdbcUrl(String.format(TAOS_JDBC_FORMAT, dataSourceTestDTO.getDatabaseIp(),
                    dataSourceTestDTO.getDatabasePort(), dataSourceTestDTO.getDb()));
        }
        dataSourceService.dbTest(dataSourceTestDTO);
        return R.OK();
    }

    /**
     * 数据源测试(使用ID)
     *
     * @param id 主键
     * @return 测试结果
     */
    @ApiOperation(value = "数据源测试(使用ID)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @GetMapping("/dbTestById")
    public R<Object> dbTestById(@RequestParam Serializable id) {
        dataSourceService.dbTestById(id);
        return R.OK();
    }

}

