package com.siact.energy.cal.server.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 公式优化相关配置
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Configuration
@EnableAsync
public class FormulaOptimizationConfig {

    /**
     * 公式处理专用线程池
     */
    @Bean("formulaProcessorExecutor")
    public Executor formulaProcessorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：CPU核心数
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数：CPU核心数 * 2
        executor.setMaxPoolSize(corePoolSize * 2);
        
        // 队列容量：适中的队列大小，避免内存溢出
        executor.setQueueCapacity(1000);
        
        // 线程名前缀
        executor.setThreadNamePrefix("formula-processor-");
        
        // 空闲线程存活时间
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：调用者运行策略，避免任务丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("公式处理线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                corePoolSize, corePoolSize * 2, 1000);
        
        return executor;
    }

    /**
     * 数据库查询专用线程池
     */
    @Bean("databaseQueryExecutor")
    public Executor databaseQueryExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 数据库连接池通常有限，使用较小的线程数
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("db-query-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("数据库查询线程池初始化完成 - 核心线程数: 10, 最大线程数: 20, 队列容量: 500");
        
        return executor;
    }
}
