package com.siact.energy.cal.server.service.flow.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.dao.flow.ApiConfigMapper;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;
import com.siact.energy.cal.server.entity.flow.RuleDetailEntity;
import com.siact.energy.cal.server.service.flow.IApiRtsService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static java.util.stream.Collectors.toList;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-12
 * @Description:
 * @Version: 1.0
 */
@Service
@Slf4j
public class ApiRtsServiceImpl extends ServiceImpl<ApiConfigMapper, ApiConfigEntity> implements IApiRtsService {

    @Resource
    ApiConfigMapper apiConfigMapper;
    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    HikariDataSource dataSource;

    @Resource
    RuleDetailFlowServiceImpl ruleDetailFlowService;
    @Override
    public R<JSONArray> apiPost(String url, String body) throws ExecutionException, InterruptedException {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String requestURI = request.getRequestURI();
        String path = requestURI.substring(requestURI.indexOf("getApiResult") + 12);
        ApiConfigEntity configEntity = new ApiConfigEntity();
        configEntity.setApiUrl(path);
        LambdaQueryWrapper<ApiConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiConfigEntity::getApiUrl, path);
        ApiConfigEntity apiConfigEntity = apiConfigMapper.selectOne(wrapper);
        if (apiConfigEntity == null){
            return R.ERROR("接口不存在,请检查接口是否正确");
        }
        Long flowId = apiConfigEntity.getFlowId();

        List<RuleDetailEntity> list = ruleDetailFlowService.list();
        List<Long> collect = list.stream().map(RuleDetailEntity::getId).collect(toList());

        List<String> idList = new ArrayList<>();
        collect.forEach(item ->{
            idList.add(String.valueOf(item));
        });
        ResultContext resultContext = new ResultContext();
        resultContext.setFormulaList(idList);
        resultContext.setDataSourceId("1796356265204617217");
        resultContext.setApiId(apiConfigEntity.getId());

        CompletableFuture<LiteflowResponse> completableFuture = CompletableFuture.supplyAsync(() -> flowExecutor.execute2Resp(String.valueOf(flowId), "arg", resultContext));
        LiteflowResponse response = completableFuture.get();
        if (response.isSuccess()){
            wrapper.clear();
            wrapper.eq(ApiConfigEntity::getId, flowId);
            ApiConfigEntity apiConfig = this.getById(apiConfigEntity.getId());
            String tableId = apiConfig.getTableId();
            if (StrUtil.isNotBlank(apiConfig.getTableId())){
                return getResultData(url, tableId);
            }
            return R.ERROR("计算失败:"+url);
        }
        return R.ERROR("组件流运行失败");

    }

    private R<JSONArray> getResultData(String url, String tableId) {
        JSONArray result = new JSONArray();
        //查询结果表数据
        try {
            Connection connection = dataSource.getConnection();
            String sql = "select * from "+ tableId + ";";
            ResultSet resultSet = DBTools.executeQuerySql(connection, sql);
            assert resultSet != null;
            ResultSetMetaData metaData = resultSet.getMetaData();
            List<String> columns = new ArrayList<>();
            for (int i=1; i<=metaData.getColumnCount(); i++){
                columns.add(metaData.getColumnName(i));
            }
            while (resultSet.next()){
                JSONObject jsonObject = new JSONObject();
                for (String column : columns){
                    jsonObject.put(column, resultSet.getObject(column));
                }
                result.add(jsonObject);
            }
        } catch (SQLException e) {
            log.error("获取数据失败:{},{}", e.getMessage(), tableId, e);
            return R.ERROR("计算失败:" + url);
        }
        return R.OK(result);
    }
}
