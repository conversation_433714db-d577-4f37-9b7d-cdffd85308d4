package com.siact.energy.cal.server.service.dataSource.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.auth.vo.ProjectVO;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceUpdateDTO;
import com.siact.energy.cal.common.pojo.enums.DataSourceStatusEnum;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.datasource.db.factory.TimeSeriesDbFactory;
import com.siact.energy.cal.server.common.datasource.factory.TDengineDriver;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import com.siact.energy.cal.server.convertor.dataSource.DataSourceConvertor;
import com.siact.energy.cal.server.dao.dataSource.DataSourceDao;
import com.siact.energy.cal.server.entity.dataProject.DataProject;
import com.siact.energy.cal.server.entity.dataSource.DataSource;
import com.siact.energy.cal.server.service.BaseService;
import com.siact.energy.cal.server.service.dataProject.DataProjectService;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 数据源表(DataSource)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:11
 */
@Slf4j
@Service("dataSourceService")
public class DataSourceServiceImpl extends ServiceImpl<DataSourceDao, DataSource> implements DataSourceService {

    @Autowired
    private DataProjectService dataProjectService;

    @Autowired
    private DataSourceDao dataSourceDao;

    @Autowired
    private TDengineDriver tDengineDriver;

    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TimeSeriesDbFactory dbFactory;

    @Autowired
    private BaseService baseService;

    @Override
    public PageBean<DataSourceVO> listPage(PageBean<DataSourceVO> page, DataSourceQueryDTO dataSourceQueryDTO) {
        // 转换器
        DataSourceConvertor convertor = DataSourceConvertor.INSTANCE;

        escapeSpecialCharacters(dataSourceQueryDTO);
        // VO转实体
        DataSource dataSource = convertor.queryDTO2Entity(dataSourceQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<DataSource> queryWrapper = new LambdaQueryWrapper<>(dataSource);

        // 添加权限过滤条件
        queryWrapper = addAuthFilter(queryWrapper);
        if (queryWrapper == null) {
            return new PageBean<>();
        }
        // 处理projectName查询条件
        if (StringUtils.isNotBlank(dataSourceQueryDTO.getProjectName())) {
            // 方案1：通过关联查询处理projectName条件
            List<Long> projectIds = dataProjectService.list(new LambdaQueryWrapper<DataProject>()
                            .like(DataProject::getProjectName, dataSourceQueryDTO.getProjectName()))
                    .stream()
                    .map(DataProject::getId)
                    .collect(Collectors.toList());

            if (!projectIds.isEmpty()) {
                queryWrapper.in(DataSource::getProjectId, projectIds);
            } else {
                // 如果没有匹配的项目，返回空结果
                return new PageBean<>();
            }
        }
        // 查询实体数据
        Page<DataSource> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);
        // 实体分页转VO分页
        PageBean<DataSourceVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);
        // 填充项目信息
        fillProjectInfo(voPageBean.getRecords());
        return voPageBean;
    }

    @Override
    public DataSourceVO getVoById(Serializable id) {
        DataSource dataSource = getById(id);
        return DataSourceConvertor.INSTANCE.entity2Vo(dataSource);
    }

    @Override
    public Boolean save(DataSourceInsertDTO dataSourceInsertDTO) {
        DataSource dataSource = DataSourceConvertor.INSTANCE.insertDTO2Entity(dataSourceInsertDTO);
        boolean save = save(dataSource);
        CompletableFuture.runAsync(() -> dbTestById(dataSource.getId()), executor);
        return save;
    }

    @Override
    public Boolean updateVoById(DataSourceUpdateDTO dataSourceUpdateDTO) {
        boolean update = updateById(DataSourceConvertor.INSTANCE.updateDTO2Entity(dataSourceUpdateDTO));
        CompletableFuture.runAsync(() -> dbTestById(dataSourceUpdateDTO.getId()), executor);
        return update;
    }

    @Override
    public void dbTest(DataSourceTestDTO dataSourceTestDTO) {
     /*   try {
            DataSourceDialect dialect = tDengineDriver.factory(DataSourceConvertor.INSTANCE.testDTODBConfigDTO(dataSourceTestDTO));
            dialect.close();
        } catch (Exception e) {
            log.error("校验数据库连接失败，参数：{}", dataSourceTestDTO, e);
            throw new BizException(e.getMessage());
        }*/
        try {
            // 获取对应的数据库操作器
            AbstractDbOperator dbOperator = dbFactory.getDbOperator(dataSourceTestDTO.getDbType());

            DataSourceVo dataSourceVo = new DataSourceVo();
            BeanUtils.copyProperties(dataSourceTestDTO, dataSourceVo);
            // 执行连接测试
            boolean success = dbOperator.testConnection(dataSourceVo);
            if (!success) {
                throw new BizException("数据源连接测试失败");
            }
        } catch (Exception e) {
            log.error("数据源测试失败: {}", e.getMessage(), e);
            throw new BizException("数据源测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据源配置
     */
    @Override
    @Cacheable(value = "dataSourceCache", key = "#projectCode", unless = "#result == null")
    public DataSourceVo getDataSourceConfig(String projectCode) {
        log.debug("Data source cache miss for project: {}", projectCode);

        // 从数据库获取数据源配置
        return dataSourceDao.getDataSourceByProjectCode(projectCode);
    }

    @Override
    public void dbTestById(Serializable id) {
        DataSource dataSource = getById(id);
        if (Objects.nonNull(dataSource)) {
            try {
                dbTest(DataSourceConvertor.INSTANCE.entity2TestDTO(dataSource));
                updateDataSourceStatusById(id, DataSourceStatusEnum.NORMAL);
            } catch (Exception e) {
                updateDataSourceStatusById(id, DataSourceStatusEnum.INTERRUPT);
                throw new RuntimeException(e);
            }
        } else {
            throw new BizException("数据源不存在");
        }
    }

    @Override
    public boolean updateDataSourceStatusById(Serializable id, DataSourceStatusEnum dataSourceStatusEnum) {

        LambdaUpdateWrapper<DataSource> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataSource::getId, id)
                .set(DataSource::getStatus, dataSourceStatusEnum.getValue());

        return update(updateWrapper);
    }

    @Override
    public void dataSourceCheck() {
        List<DataSource> dataSourceList = list();
        dataSourceList.forEach(d -> {
            try {
                dbTestById(d.getId());
            } catch (Exception e) {
                log.warn("数据源校验失败, ", e);
            }
        });
    }

    @Override
    public void saveDataSource() {
        List<DataSourceVo> dataSourceVos = dataSourceDao.saveDataSource();

        for (DataSourceVo dataSourceVo : dataSourceVos) {
            if (StringUtils.isNotBlank(dataSourceVo.getProjectCode())) {
//                GlobalUtil.dbConfigMap.put(dataSourceVo.getProjectCode(),dataSourceVo);
                /*try {
                    Connection connection = DruidTdDataUtil.getConnection(dataSourceVo.getJdbcUrl(), dataSourceVo.getUserName(), dataSourceVo.getPassword());
                    GlobalUtil.dbConnectionMap.put(dataSourceVo.getProjectCode(),connection);
                } catch (SQLException e) {
                    log.error("{}数据源初始化失败",dataSourceVo.getDatabaseName(),e);
                    throw new RuntimeException(e);
                }*/

                redisUtil.set(dataSourceVo.getProjectCode(), dataSourceVo);
            }

        }

    }

    @Override
    public List<DataSourceVo> getAllDataSource() {
        return dataSourceDao.saveDataSource();
    }

    /**
     * 获取经过权限过滤的数据源列表
     *
     * @return 过滤后的DataSourceVO列表
     */
    private List<DataSourceVO> getFilteredDataSources() {
        // 获取所有数据源配置
        List<DataSourceVo> dataSourceVos = dataSourceDao.saveDataSource();

        // 直接对DataSourceVo进行权限过滤
        List<DataSourceVo> filteredVos = baseService.filterAuthorizedProjects(
                dataSourceVos,
                DataSourceVo::getProjectCode  // 使用projectCode进行过滤
        );

        // 将过滤后的DataSourceVo转换为DataSourceVO，并关联项目ID
        return filteredVos.stream()
                .map(vo -> {
                    // 根据projectCode查询对应的项目信息
                    DataProject project = dataProjectService.getOne(
                            new LambdaQueryWrapper<DataProject>()
                                    .eq(DataProject::getProjectCode, vo.getProjectCode())
                    );

                    DataSourceVO dataSourceVO = new DataSourceVO();
                    BeanUtils.copyProperties(vo, dataSourceVO);

                    // 设置项目相关信息
                    if (project != null) {
                        dataSourceVO.setProjectId(project.getId());
                        dataSourceVO.setProjectName(project.getProjectName());
                    }

                    return dataSourceVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取权限过滤的查询条件
     *
     * @param queryWrapper 原始查询条件
     * @return 添加了权限过滤的查询条件
     */
    private LambdaQueryWrapper<DataSource> addAuthFilter(LambdaQueryWrapper<DataSource> queryWrapper) {
        if (!baseService.isEnabled()) {
            return queryWrapper;
        }

        // 直接获取用户有权限的项目编码
        List<String> authorizedProjectCodes = baseService.getUserProjects().stream()
                .map(ProjectVO::getDataCode)
                .collect(Collectors.toList());

        if (!authorizedProjectCodes.isEmpty()) {
            // 根据项目编码查询项目ID
            List<DataProject> projects = dataProjectService.list(new LambdaQueryWrapper<DataProject>()
                    .in(DataProject::getProjectCode, authorizedProjectCodes));

            List<Long> authorizedProjectIds = projects.stream()
                    .map(DataProject::getId)
                    .collect(Collectors.toList());

            if (!authorizedProjectIds.isEmpty()) {
                queryWrapper.in(DataSource::getProjectId, authorizedProjectIds);
            }
        } else {
            throw new BizException("用户没有任何项目权限");
        }

        return queryWrapper;
    }

    @Override
    public List<DataSourceVO> listAll() {
        return getFilteredDataSources();
    }

    /**
     * 填充项目信息
     *
     * @param records 待填充数据
     */
    private void fillProjectInfo(List<DataSourceVO> records) {

        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Map<Long, String> projectIdNameMap = dataProjectService.listByIds(records.stream().map(DataSourceVO::getProjectId).collect(Collectors.toList())).stream()
                .collect(Collectors.toMap(DataProject::getId, DataProject::getProjectName, (o, n) -> o));

        records.forEach(vo -> vo.setProjectName(projectIdNameMap.get(vo.getProjectId())));
    }

    /**
     * 转义查询条件中的特殊字符
     *
     * @param queryDTO 查询条件DTO
     */
    private void escapeSpecialCharacters(DataSourceQueryDTO queryDTO) {
        if (queryDTO == null) {
            return;
        }

        // 对可能包含特殊字符的字符串字段进行转义
        if (StringUtils.isNotBlank(queryDTO.getProjectName())) {
            queryDTO.setProjectName(escapeLikeQueryParam(queryDTO.getProjectName()));
        }
        if (StringUtils.isNotBlank(queryDTO.getDatabaseName())) {
            queryDTO.setDatabaseName(escapeLikeQueryParam(queryDTO.getDatabaseName()));
        }
        if (StringUtils.isNotBlank(queryDTO.getDb())) {
            queryDTO.setDb(escapeLikeQueryParam(queryDTO.getDb()));
        }
        //dbType和status是Integer类型，不需要转义
    }

    /**
     * 转义LIKE查询中的特殊字符
     *
     * @param param 查询参数
     * @return 转义后的参数
     */
    private String escapeLikeQueryParam(String param) {
        if (StringUtils.isBlank(param)) {
            return param;
        }
        // 转义特殊字符 % _ \ 等
        return param.replaceAll("[%_\\\\]", "\\\\$0");
    }
}

