package com.siact.energy.cal.server.common.utils;

import com.siact.energy.cal.server.core.pojo.TimeWindow;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 计算器通用工具类
 * <p>
 * 封装了在各个计算器（基础、聚合、衍生）中可被复用的公共方法，
 * 例如数据结构转置、时间点提取等。
 *
 * <AUTHOR>
 * @since 2025-09-02
 */
public final class CalculatorUtil {

    /**
     * 私有构造函数，防止实例化工具类。
     */
    private CalculatorUtil() {}

    /**
     * 从行式存储的原始数据中，提取出所有唯一、有序的时间点列表。
     * <p>
     * 1. 如果是等时间间隔查询 (timeWindows.size() > 1)，则优先使用 TimeWindow 中的对齐时间点 (alignedTime)。
     * 2. 如果是时间区间/截面查询 (timeWindows.size() <= 1)，则从实际数据 (rawDataMap) 中提取所有存在的时间戳。
     *
     * @param rawDataMap  从数据库查询到的原始数据，格式为 Map<指标, Map<时间, 值>>。
     * @param timeWindows 当前计算任务的时间窗口列表。
     * @return 一个经过排序的、不重复的时间点字符串列表 (e.g., ["2025-09-01 10:00:00", "2025-09-01 11:00:00"])。
     */
    public static List<String> extractTimePoints(
            Map<String, ? extends Map<String, BigDecimal>> rawDataMap,
            List<TimeWindow> timeWindows) {

        Set<String> timePointSet = new HashSet<>();

        if (CollectionUtils.isEmpty(timeWindows) || timeWindows.size() <= 1) {
            // 时间区间或时间截面模式：从实际数据中收集所有时间戳
            if (rawDataMap != null) {
                rawDataMap.values().forEach(timeValueMap -> {
                    if (timeValueMap != null) {
                        timePointSet.addAll(timeValueMap.keySet());
                    }
                });
            }
        } else {
            // 等时间间隔模式：使用时间窗口的对齐时间，确保时间轴的完整性
            timeWindows.stream()
                    .map(TimeWindow::getAlignedTime)
                    .filter(Objects::nonNull)
                    .forEach(timePointSet::add);
        }

        List<String> timePoints = new ArrayList<>(timePointSet);
        timePoints.sort(String::compareTo);
        return timePoints;
    }

    /**
     * 将行式数据结构 (Map of Maps) 转置为列式数据结构 (Map of Arrays)。
     * <p>
     * 它将数据从便于按时间点查询的格式，
     * 转换为便于按指标（列）连续访问的格式，以最大化CPU缓存效率。
     *
     * @param rowBasedData 原始的行式数据，格式为 Map<指标, Map<时间, 值>>。
     * @param timePoints   一个有序的时间点列表，作为列式数据的时间轴基准。
     * @param timeIndexMap 一个从时间点字符串到其在 timePoints 列表中索引的映射，用于高效查找。
     * @return 转置后的列式数据，格式为 Map<指标, 值数组[]>。数组的长度与 timePoints 相同。
     */
    public static Map<String, BigDecimal[]> transposeToColumnar(
            Map<String, ? extends Map<String, BigDecimal>> rowBasedData,
            List<String> timePoints,
            Map<String, Integer> timeIndexMap) {

        Map<String, BigDecimal[]> columnarData = new HashMap<>();
        int timePointSize = timePoints.size();

        if (rowBasedData == null) {
            return columnarData;
        }

        rowBasedData.forEach((indicator, timeValueMap) -> {
            BigDecimal[] values = new BigDecimal[timePointSize];
            if (timeValueMap != null) {
                timeValueMap.forEach((timestamp, value) -> {
                    Integer index = timeIndexMap.get(timestamp);
                    if (index != null) {
                        values[index] = value;
                    }
                });
            }
            columnarData.put(indicator, values);
        });
        return columnarData;
    }

    /**
     * 将列式数据结构 (Map of Arrays) 中新计算出的指标列，反转并合并回行式数据结构。
     * <p>
     * 这是列式计算的收尾步骤，用于将计算结果更新回全局的结果集。
     *
     * @param indicatorsToMerge 需要合并的指标列表。
     * @param columnData        包含所有计算结果的列式数据。
     * @param timePoints        有序的时间点列表，用于重建时间戳。
     * @param targetRowMap      目标行式数据Map，新计算的结果将被写入或合并到这里。
     */
    public static void mergeColumnarToRowBased(
            List<String> indicatorsToMerge,
            Map<String, BigDecimal[]> columnData,
            List<String> timePoints,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> targetRowMap) {

        if (CollectionUtils.isEmpty(indicatorsToMerge) || columnData == null || targetRowMap == null) {
            return;
        }

        for (String indicator : indicatorsToMerge) {
            BigDecimal[] resultColumn = columnData.get(indicator);
            if (resultColumn == null) continue;

            ConcurrentHashMap<String, BigDecimal> timeValueMap = targetRowMap.computeIfAbsent(indicator, k -> new ConcurrentHashMap<>());
            for (int i = 0; i < timePoints.size(); i++) {
                if (resultColumn[i] != null) {
                    timeValueMap.put(timePoints.get(i), resultColumn[i]);
                }
            }
        }
    }

    /**
     * 将列式数据中的**增量计算结果**，转置为行式数据结构。
     * <p>
     * 与 `mergeColumnarToRowBased` 不同，此方法只转换那些被明确标记为“新计算”的数据点，
     * 通常用于异步存储，只将增量数据写入数据库。
     *
     * @param columnarData        包含所有新旧数据的列式Map。
     * @param newDataPointsMap    描述了哪些指标的哪些时间点是新计算的。
     * @param timeIndexMap        时间点到索引的映射。
     * @return 一个只包含新计算出的数据点的行式Map。
     */
    public static ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> transposeIncrementallyToRowBased(
            Map<String, BigDecimal[]> columnarData,
            Map<String, Set<String>> newDataPointsMap,
            Map<String, Integer> timeIndexMap) {

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> newlyCalculatedResult = new ConcurrentHashMap<>();
        
        if (newDataPointsMap == null) return newlyCalculatedResult;

        newDataPointsMap.forEach((indicator, newTimes) -> {
            BigDecimal[] values = columnarData.get(indicator);
            if (values == null) return;

            ConcurrentHashMap<String, BigDecimal> timeValueMap = new ConcurrentHashMap<>();
            for (String timePoint : newTimes) {
                Integer index = timeIndexMap.get(timePoint);
                if (index != null && values[index] != null) {
                    timeValueMap.put(timePoint, values[index]);
                }
            }
            if (!timeValueMap.isEmpty()) {
                newlyCalculatedResult.put(indicator, timeValueMap);
            }
        });
        return newlyCalculatedResult;
    }
    
    /**
     * 根据一个时间点列表，快速创建一个从时间点到其索引的映射。
     * <p>
     * 这个预处理步骤可以极大地提升在循环中根据时间戳查找数组索引的效率，
     * 将 O(N) 的查找变为 O(1)。
     *
     * @param timePoints 有序或无序的时间点列表。
     * @return 一个 Map，Key是时间点字符串，Value是其在列表中的索引 (Integer)。
     */
    public static Map<String, Integer> createTimeIndexMap(List<String> timePoints) {
        if (CollectionUtils.isEmpty(timePoints)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>((int)(timePoints.size() / 0.75f) + 1); // 优化初始容量
        for (int i = 0; i < timePoints.size(); i++) {
            map.put(timePoints.get(i), i);
        }
        return map;
    }
}