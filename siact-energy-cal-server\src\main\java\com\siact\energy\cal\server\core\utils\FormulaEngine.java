package com.siact.energy.cal.server.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.nfunk.jep.JEP;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 优化的公式计算引擎
 * 解决现有JEP计算的性能瓶颈：
 * 1. 线程本地JEP实例复用
 * 2. 公式解析结果缓存
 * 3. 变量提取优化
 * 4. 批量计算支持
 */
@Component
@Slf4j
public class FormulaEngine {

    // 线程本地JEP实例，避免重复创建
    private final ThreadLocal<JEP> jepThreadLocal = ThreadLocal.withInitial(() -> {
        JEP jep = new JEP();
        jep.addStandardConstants();
        jep.addStandardFunctions();
        jep.setAllowUndeclared(true);
        jep.setImplicitMul(true);
        return jep;
    });

    // 解析后的公式缓存
    private final ConcurrentHashMap<String, ParsedFormula> formulaCache = new ConcurrentHashMap<>();

    // 变量提取的正则表达式缓存
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("@\\[([^\\]]+)\\]");

    /**
     * 计算公式值
     */
    public BigDecimal calculateFormula(String formula, Map<String, BigDecimal> variables) {
        if (formula == null || formula.trim().isEmpty()) {
            return null;
        }

        try {
            ParsedFormula parsedFormula = formulaCache.computeIfAbsent(formula, this::parseFormula);
            if (parsedFormula == null) {
                return null;
            }

            return parsedFormula.evaluate(variables);

        } catch (Exception e) {
            log.error("公式计算失败: formula={}, variables={}", formula, variables, e);
            return null;
        }
    }

    /**
     * 批量计算公式值
     */
    public Map<String, BigDecimal> calculateFormulaBatch(
            Map<String, String> formulas, 
            Map<String, BigDecimal> variables) {
        
        Map<String, BigDecimal> results = new HashMap<>();
        
        for (Map.Entry<String, String> entry : formulas.entrySet()) {
            String indicator = entry.getKey();
            String formula = entry.getValue();
            
            BigDecimal result = calculateFormula(formula, variables);
            if (result != null) {
                results.put(indicator, result);
                // 将计算结果加入变量中，供后续公式使用
                variables.put(indicator, result);
            }
        }
        
        return results;
    }

    /**
     * 获取公式中的变量列表
     */
    public List<String> getFormulaVariables(String formula) {
        if (formula == null || formula.trim().isEmpty()) {
            return Collections.emptyList();
        }

        ParsedFormula parsedFormula = formulaCache.computeIfAbsent(formula, this::parseFormula);
        return parsedFormula != null ? parsedFormula.getVariables() : Collections.emptyList();
    }

    /**
     * 解析公式
     */
    private ParsedFormula parseFormula(String originalFormula) {
        try {
            // 清理公式格式
            String cleanFormula = originalFormula.replaceAll("@|\\[|\\]", "");
            
            // 提取变量
            List<String> variables = extractVariables(originalFormula);
            
            // 验证公式语法
            JEP jep = jepThreadLocal.get();
            jep.parseExpression(cleanFormula);
            
            if (jep.hasError()) {
                log.warn("公式语法错误: {}, error: {}", originalFormula, jep.getErrorInfo());
                return null;
            }

            return new ParsedFormula(originalFormula, cleanFormula, variables);

        } catch (Exception e) {
            log.error("解析公式失败: {}", originalFormula, e);
            return null;
        }
    }

    /**
     * 提取公式中的变量
     */
    private List<String> extractVariables(String formula) {
        List<String> variables = new ArrayList<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(formula);
        
        while (matcher.find()) {
            String variable = matcher.group(1);
            if (!variables.contains(variable)) {
                variables.add(variable);
            }
        }
        
        return variables;
    }

    /**
     * 清理线程本地资源
     */
    public void cleanup() {
        jepThreadLocal.remove();
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("formulaCacheSize", formulaCache.size());
        stats.put("threadLocalInstances", Thread.activeCount()); // 近似值
        return stats;
    }

    /**
     * 清理公式缓存
     */
    public void clearCache() {
        formulaCache.clear();
        log.info("公式缓存已清理");
    }

    /**
     * 解析后的公式对象
     */
    private class ParsedFormula {
        private final String originalFormula;
        private final String cleanFormula;
        private final List<String> variables;

        public ParsedFormula(String originalFormula, String cleanFormula, List<String> variables) {
            this.originalFormula = originalFormula;
            this.cleanFormula = cleanFormula;
            this.variables = variables;
        }

        public List<String> getVariables() {
            return variables;
        }

        public BigDecimal evaluate(Map<String, BigDecimal> variableValues) {
            JEP jep = jepThreadLocal.get();

            try {
                // 清除之前的变量
                for (String var : variables) {
                    BigDecimal value = variableValues.get(var);
                    if (value != null) {
                        jep.addVariable(var, value.doubleValue());
                    } else {
                        // 变量值为null时，设为0（根据业务需求调整）
                        jep.addVariable(var, 0.0);
                    }
                }

                // 解析并计算
                jep.parseExpression(cleanFormula);
                if (jep.hasError()) {
                    log.warn("公式计算错误: {}, error: {}", originalFormula, jep.getErrorInfo());
                    return null;
                }

                Object result = jep.getValue();
                if (result == null) {
                    return null;
                }

                if (result instanceof Double) {
                    Double d = (Double) result;
                    if (d.isNaN() || d.isInfinite()) {
                        return null;
                    }
                    return BigDecimal.valueOf(d).setScale(6, RoundingMode.HALF_UP);
                }

                return new BigDecimal(result.toString()).setScale(6, RoundingMode.HALF_UP);

            } catch (Exception e) {
                log.error("公式求值失败: formula={}, variables={}", originalFormula, variableValues, e);
                return null;
            }
        }
    }
}
