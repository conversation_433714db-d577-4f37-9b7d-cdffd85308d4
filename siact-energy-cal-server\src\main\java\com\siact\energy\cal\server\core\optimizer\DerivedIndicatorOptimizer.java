package com.siact.energy.cal.server.core.optimizer;

import com.siact.energy.cal.common.pojo.dto.energycal.FormulaClass;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.FormulaCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 🔥 衍生指标智能优化器
 *
 * 核心功能：
 * 1. 分析指标复杂度和依赖关系
 * 2. 制定智能分批计算策略
 * 3. 优化计算顺序和并行度
 */
@Component
@Slf4j
public class DerivedIndicatorOptimizer {
    
    @Autowired
    private FormulaCacheService formulaCacheService;
    
    /**
     * 🎯 分析衍生指标计算复杂度和依赖关系
     */
    public DerivedCalculationPlan analyzeDerivedIndicators(List<String> derivedIndicators) {
        log.info("🔥 开始分析衍生指标计算复杂度: 指标数量={}", derivedIndicators.size());
        
        // 1. 批量获取公式
        Map<String, String> formulaMap = formulaCacheService.batchGetFormulas(
                derivedIndicators, ConstantBase.RULECOLID_COMMON);
        
        // 2. 构建公式对象列表
        List<FormulaClass> formulaList = derivedIndicators.stream()
                .filter(indicator -> StringUtils.isNotBlank(formulaMap.get(indicator)))
                .map(indicator -> new FormulaClass(indicator, formulaMap.get(indicator)))
                .collect(Collectors.toList());
        
        // 3. 拓扑排序获取计算顺序
        List<String> calculationOrder = FormulaUtils.calculateOrder(formulaList);
        
        // 4. 分析指标类型和复杂度
        IndicatorAnalysisResult analysisResult = analyzeIndicatorTypes(calculationOrder, formulaMap);
        
        // 5. 制定分批计算策略
        List<CalculationBatch> batches = createCalculationBatches(analysisResult, calculationOrder);
        
        // 6. 构建计算计划
        DerivedCalculationPlan plan = new DerivedCalculationPlan(
                calculationOrder, analysisResult, batches, formulaMap);
        
        log.info("衍生指标分析完成: 普通指标={}, 同比环比指标={}, 计算批次={}", 
                analysisResult.normalIndicators.size(), 
                analysisResult.yoyMomIndicators.size(), 
                batches.size());
        
        return plan;
    }
    
    /**
     * 分析指标类型和复杂度
     */
    private IndicatorAnalysisResult analyzeIndicatorTypes(List<String> calculationOrder, 
                                                        Map<String, String> formulaMap) {
        
        Map<String, IndicatorComplexity> complexityMap = new HashMap<>();
        List<String> normalIndicators = new ArrayList<>();
        Map<String, String> yoyMomIndicators = new HashMap<>();
        Map<String, Set<String>> dependencyMap = new HashMap<>();
        
        for (String indicator : calculationOrder) {
            String formula = formulaMap.get(indicator);
            if (StringUtils.isBlank(formula)) continue;
            
            // 分析指标类型
            if (formula.contains(ConstantBase.YOY) || formula.contains(ConstantBase.MOM)) {
                // 同比环比指标
                String functionType = formula.contains(ConstantBase.YOY) ? "YOY" : "MOM";
                yoyMomIndicators.put(indicator, functionType);
                complexityMap.put(indicator, IndicatorComplexity.HIGH); // 同比环比复杂度高
            } else {
                // 普通指标
                normalIndicators.add(indicator);
                
                // 分析复杂度
                IndicatorComplexity complexity = analyzeFormulaComplexity(formula);
                complexityMap.put(indicator, complexity);
                
                // 分析依赖关系
                List<String> dependencies = FormulaUtils.getVarList(formula);
                dependencyMap.put(indicator, new HashSet<>(dependencies));
            }
        }
        
        return new IndicatorAnalysisResult(normalIndicators, yoyMomIndicators, 
                complexityMap, dependencyMap);
    }
    
    /**
     * 分析公式复杂度
     */
    private IndicatorComplexity analyzeFormulaComplexity(String formula) {
        if (StringUtils.isBlank(formula)) return IndicatorComplexity.LOW;
        
        // 复杂度评估因子
        int complexityScore = 0;
        
        // 1. 操作符数量
        long operatorCount = formula.chars()
                .filter(ch -> "+-*/()".indexOf(ch) >= 0)
                .count();
        complexityScore += operatorCount;
        
        // 2. 函数调用数量
        long functionCount = formula.chars()
                .filter(ch -> ch == '(')
                .count();
        complexityScore += functionCount * 2;
        
        // 3. 变量数量
        List<String> variables = FormulaUtils.getVarList(formula);
        complexityScore += variables.size();
        
        // 4. 特殊函数
        if (formula.contains("sqrt") || formula.contains("pow") || formula.contains("log")) {
            complexityScore += 5;
        }
        
        // 5. 条件表达式
        if (formula.contains("if") || formula.contains("?") || formula.contains(":")) {
            complexityScore += 3;
        }
        
        // 复杂度分级
        if (complexityScore <= 5) return IndicatorComplexity.LOW;
        if (complexityScore <= 15) return IndicatorComplexity.MEDIUM;
        return IndicatorComplexity.HIGH;
    }
    
    /**
     * 创建计算批次
     */
    private List<CalculationBatch> createCalculationBatches(IndicatorAnalysisResult analysisResult, 
                                                          List<String> calculationOrder) {
        
        List<CalculationBatch> batches = new ArrayList<>();
        
        // 1. 按依赖层级分批
        Map<String, Integer> dependencyLevels = calculateDependencyLevels(
                analysisResult.normalIndicators, analysisResult.dependencyMap);
        
        // 2. 按层级分组
        Map<Integer, List<String>> levelGroups = dependencyLevels.entrySet().stream()
                .collect(Collectors.groupingBy(
                        Map.Entry::getValue,
                        Collectors.mapping(Map.Entry::getKey, Collectors.toList())
                ));
        
        // 3. 为每个层级创建批次
        for (int level = 0; level <= levelGroups.keySet().stream().mapToInt(Integer::intValue).max().orElse(0); level++) {
            List<String> levelIndicators = levelGroups.getOrDefault(level, new ArrayList<>());
            if (!levelIndicators.isEmpty()) {
                
                // 按复杂度进一步细分批次
                List<CalculationBatch> levelBatches = createBatchesByComplexity(
                        levelIndicators, analysisResult.complexityMap, level);
                batches.addAll(levelBatches);
            }
        }
        
        // 4. 添加同比环比批次（最后执行）
        if (!analysisResult.yoyMomIndicators.isEmpty()) {
            CalculationBatch yoyMomBatch = new CalculationBatch(
                    new ArrayList<>(analysisResult.yoyMomIndicators.keySet()),
                    BatchType.YOY_MOM,
                    IndicatorComplexity.HIGH,
                    batches.size(),
                    "同比环比指标批次"
            );
            batches.add(yoyMomBatch);
        }
        
        return batches;
    }
    
    /**
     * 计算依赖层级
     */
    private Map<String, Integer> calculateDependencyLevels(List<String> indicators, 
                                                         Map<String, Set<String>> dependencyMap) {
        
        Map<String, Integer> levels = new HashMap<>();
        
        // 使用拓扑排序计算层级
        for (String indicator : indicators) {
            if (!levels.containsKey(indicator)) {
                calculateLevel(indicator, dependencyMap, levels, new HashSet<>());
            }
        }
        
        return levels;
    }
    
    /**
     * 递归计算单个指标的依赖层级
     */
    private int calculateLevel(String indicator, Map<String, Set<String>> dependencyMap, 
                             Map<String, Integer> levels, Set<String> visiting) {
        
        if (levels.containsKey(indicator)) {
            return levels.get(indicator);
        }
        
        if (visiting.contains(indicator)) {
            // 检测到循环依赖，设为0级
            log.warn("检测到循环依赖: {}", indicator);
            levels.put(indicator, 0);
            return 0;
        }
        
        visiting.add(indicator);
        
        Set<String> dependencies = dependencyMap.getOrDefault(indicator, new HashSet<>());
        int maxDependencyLevel = -1;
        
        for (String dependency : dependencies) {
            int depLevel = calculateLevel(dependency, dependencyMap, levels, visiting);
            maxDependencyLevel = Math.max(maxDependencyLevel, depLevel);
        }
        
        int level = maxDependencyLevel + 1;
        levels.put(indicator, level);
        visiting.remove(indicator);
        
        return level;
    }
    
    /**
     * 按复杂度创建批次
     */
    private List<CalculationBatch> createBatchesByComplexity(List<String> indicators, 
                                                           Map<String, IndicatorComplexity> complexityMap, 
                                                           int level) {
        
        List<CalculationBatch> batches = new ArrayList<>();
        
        // 按复杂度分组
        Map<IndicatorComplexity, List<String>> complexityGroups = indicators.stream()
                .collect(Collectors.groupingBy(
                        indicator -> complexityMap.getOrDefault(indicator, IndicatorComplexity.LOW)
                ));
        
        // 为每个复杂度级别创建批次
        int batchIndex = 0;
        for (IndicatorComplexity complexity : Arrays.asList(
                IndicatorComplexity.LOW, IndicatorComplexity.MEDIUM, IndicatorComplexity.HIGH)) {
            
            List<String> complexityIndicators = complexityGroups.getOrDefault(complexity, new ArrayList<>());
            if (!complexityIndicators.isEmpty()) {
                
                // 大批次进一步拆分
                List<List<String>> subBatches = splitLargeBatch(complexityIndicators, complexity);
                
                for (List<String> subBatch : subBatches) {
                    CalculationBatch batch = new CalculationBatch(
                            subBatch,
                            BatchType.NORMAL,
                            complexity,
                            level * 100 + batchIndex++,
                            String.format("L%d-%s批次(%d个指标)", level, complexity, subBatch.size())
                    );
                    batches.add(batch);
                }
            }
        }
        
        return batches;
    }
    
    /**
     * 拆分大批次
     */
    private List<List<String>> splitLargeBatch(List<String> indicators, IndicatorComplexity complexity) {
        // 根据复杂度确定批次大小
        int batchSize;
        switch (complexity) {
            case LOW: batchSize = 100; break;
            case MEDIUM: batchSize = 50; break;
            case HIGH: batchSize = 20; break;
            default: batchSize = 50;
        }
        
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < indicators.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, indicators.size());
            batches.add(new ArrayList<>(indicators.subList(i, endIndex)));
        }
        
        return batches;
    }
    
    // 数据类定义
    public static class DerivedCalculationPlan {
        public final List<String> calculationOrder;
        public final IndicatorAnalysisResult analysisResult;
        public final List<CalculationBatch> batches;
        public final Map<String, String> formulaMap;
        
        public DerivedCalculationPlan(List<String> calculationOrder, 
                                    IndicatorAnalysisResult analysisResult,
                                    List<CalculationBatch> batches,
                                    Map<String, String> formulaMap) {
            this.calculationOrder = calculationOrder;
            this.analysisResult = analysisResult;
            this.batches = batches;
            this.formulaMap = formulaMap;
        }
    }
    
    public static class IndicatorAnalysisResult {
        public final List<String> normalIndicators;
        public final Map<String, String> yoyMomIndicators;
        public final Map<String, IndicatorComplexity> complexityMap;
        public final Map<String, Set<String>> dependencyMap;
        
        public IndicatorAnalysisResult(List<String> normalIndicators,
                                     Map<String, String> yoyMomIndicators,
                                     Map<String, IndicatorComplexity> complexityMap,
                                     Map<String, Set<String>> dependencyMap) {
            this.normalIndicators = normalIndicators;
            this.yoyMomIndicators = yoyMomIndicators;
            this.complexityMap = complexityMap;
            this.dependencyMap = dependencyMap;
        }
    }
    
    public static class CalculationBatch {
        public final List<String> indicators;
        public final BatchType type;
        public final IndicatorComplexity complexity;
        public final int order;
        public final String description;
        
        public CalculationBatch(List<String> indicators, BatchType type, 
                              IndicatorComplexity complexity, int order, String description) {
            this.indicators = indicators;
            this.type = type;
            this.complexity = complexity;
            this.order = order;
            this.description = description;
        }
    }
    
    public enum IndicatorComplexity {
        LOW, MEDIUM, HIGH
    }
    
    public enum BatchType {
        NORMAL, YOY_MOM
    }
}
