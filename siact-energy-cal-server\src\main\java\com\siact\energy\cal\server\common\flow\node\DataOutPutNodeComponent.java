package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.type.ProductTypeEnum;
import com.siact.energy.cal.common.util.StringUtil;
import com.siact.energy.cal.core.model.ColumnDescription;
import com.siact.energy.cal.core.service.IMetaDataByDatasourceService;
import com.siact.energy.cal.core.service.impl.MetaDataByDataSourceServiceImpl;
import com.siact.energy.cal.data.entity.TargetDataSourceProperties;
import com.siact.energy.cal.data.util.DataSourceUtils;
import com.siact.energy.cal.dbwriter.DatabaseWriterFactory;
import com.siact.energy.cal.dbwriter.IDatabaseWriter;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.DataSourceOutPutDto;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.entity.flow.DataDatabaseEntity;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.ApiRtsServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 数据输出算子
 * @Version: 1.0
 */
@Component("DataOutPutNode")
@Slf4j
public class DataOutPutNodeComponent extends NodeComponent {

    @Resource
    HikariDataSource dataSource;

    @Resource
    FlowViewServiceImpl flowViewService;

    @Resource
    ApiRtsServiceImpl apiRtsService;

    @Resource
    IFlowRelationService flowRelationService;
    @Override
    public void process(){
        //获取上下文
        CalculateContext calculateContext = this.getContextBean("calculateContext");
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        log.info("数据库输出组件开始执行");
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置查询失败");
        }
        //获取数据源id
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        DataSourceOutPutDto dataSourceDto = BeanUtil.toBean(collect, DataSourceOutPutDto.class);
        //获取数据库类型
        Integer databaseType = dataSourceDto.getDatabaseType();
        ProductTypeEnum database = ProductTypeEnum.getByIndex(databaseType);

        //获取targetDataSource
        TargetDataSourceProperties targetDataSourceProperties = new TargetDataSourceProperties();
        DataDatabaseEntity dataDatabaseEntity = new DataDatabaseEntity();
        dataDatabaseEntity.setDatabaseType(databaseType);
        dataDatabaseEntity.setDatabaseIp(dataSourceDto.getDatabaseIp());
        dataDatabaseEntity.setDatabasePort(dataSourceDto.getDatabasePort());
        dataDatabaseEntity.setDatabaseName(dataSourceDto.getDatabaseName());
        setJdbcUrlByEntity(dataDatabaseEntity);
        String jdbcUrl = dataDatabaseEntity.getJdbcUrl();
        targetDataSourceProperties.setUrl(jdbcUrl);
        targetDataSourceProperties.setDriverClassName(database.getDriveClassName());
        targetDataSourceProperties.setUsername(dataSourceDto.getUserName());
        targetDataSourceProperties.setPassword(dataSourceDto.getPassword());
        HikariDataSource targetDataSource = DataSourceUtils.createTargetDataSource(targetDataSourceProperties);
        //生成字段名列表
        List<ColumnDescription> columnsList = getColumnsList(database);
        //主键列表
        List<String> primaryKeys = new ArrayList<>();
        MetaDataByDataSourceServiceImpl metaDataByDataSourceService = new MetaDataByDataSourceServiceImpl(targetDataSource, database);
        List<String> sqlCreateTable = metaDataByDataSourceService.getDDLCreateTableSQL(
                database,
                columnsList,
                primaryKeys,
                dataSourceDto.getDatabaseName(),
                dataSourceDto.getTableName(),
                null,
                false
        );
        JdbcTemplate targetJdbcTemplate = new JdbcTemplate(targetDataSource);
        for (String sql : sqlCreateTable) {
            targetJdbcTemplate.execute(sql);
            log.info("Execute SQL: \n{}", sql);
        }
        IDatabaseWriter writer = DatabaseWriterFactory.createDatabaseWriter(
                targetDataSource, targetDataSourceProperties.getWriterEngineInsert());
        List<String> fieldNames = columnsList.stream().map(ColumnDescription::getFieldName).collect(Collectors.toList());
        //获取resultMap
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = calculateContext.getResultMap();
        List<Object[]> recordValues = new ArrayList<>();
        for (Map.Entry<String, ConcurrentHashMap<String, BigDecimal>> propTsValue : resultMap.entrySet()) {
            String propCode = propTsValue.getKey();
            ConcurrentHashMap<String, BigDecimal> tsValue = propTsValue.getValue();
            for (Map.Entry<String, BigDecimal> tsValueMap : tsValue.entrySet()) {
                String ts = tsValueMap.getKey();
                BigDecimal itemvalue = tsValueMap.getValue();
                Object[] record = new Object[fieldNames.size()];
                record[0] = ts;
                record[1] = propCode;
                record[2] = itemvalue;
                recordValues.add(record);
            }
        }
        writer.prepareWrite(dataDatabaseEntity.getDatabaseName(),dataSourceDto.getTableName(), fieldNames);
        writer.write(fieldNames, recordValues);
        log.info("数据库输出组件执行完成");
    }

    private List<ColumnDescription> getColumnsList(ProductTypeEnum database) {
        ArrayList<ColumnDescription> columnDescriptions = new ArrayList<>();
        String[] array = {"ts","devproperty","propValue"};
        for (String column : array) {
            ColumnDescription columnDescription = new ColumnDescription();
            columnDescription.setFieldName(column);
            if("TDENGINE".equals(database.getDesc())){
                if("devproperty".equals(column)){
                   columnDescription.setFieldTypeName("NCHAR");
                   columnDescription.setDisplaySize(100);
                }else if("ts".equals(column)){
                    columnDescription.setFieldTypeName("TIMESTAMP");
                }else if("propValue".equals(column)){
                    columnDescription.setFieldTypeName("DOUBLE");
                    columnDescription.setLabelName("TAG");
                }
            }

            columnDescriptions.add(columnDescription);
        }
        return columnDescriptions;
    }

    private void setJdbcUrlByEntity(DataDatabaseEntity entity) {
        //获取DatabaseType
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getByIndex(entity.getDatabaseType());
        entity.setJdbcUrl(StringUtil.isBlank(entity.getJdbcUrl()) ? productTypeEnum.getUrl()
                .replace("{host}", entity.getDatabaseIp())
                .replace("{port}", entity.getDatabasePort())
                .replace("{database}", entity.getDatabaseName()) : entity.getJdbcUrl());
    }
}
