package com.siact.energy.cal.common.pojo.dto.ruleCol;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 指标集表(RuleCol) 查询DTO
 *
 * <AUTHOR>
 * @since 2024-05-20 11:30:22
 */
@ApiModel("指标集表查询DTO")
@Data
public class RuleColQueryDTO {

    /**
     * 指标集名称
     */
    @ApiModelProperty(value = "指标集名称", position = 1)
    private String ruleColName;

    /**
     * 是否激活（0-激活,1-未激活）
     */
    @ApiModelProperty(value = "是否激活（0-激活,1-未激活）", position = 2)
    private Integer activeState;

}

