package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.StartNodeDataCodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.StartNodeIndicatorColDto;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import com.siact.energy.cal.common.pojo.vo.ruleCol.RuleColVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.GlobalUtil;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.common.flow.init.TaoSnit;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.siact.energy.cal.server.service.ruleCol.RuleColService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailInstanceService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-08-19
 * @Description: 开始组件-指标集
 * @Version: 1.0
 */
@Component("startIndicatorColNode")
@Slf4j
public class StartIndicatorColComponent extends NodeComponent {

    @Resource
    EnergyCalService energyCalService;
    @Resource
    HikariDataSource hikariDataSource;
    @Resource
    RuleColService ruleColService;
    @Resource
    RuleDetailService ruleDetailService;
    @Resource
    IFlowRelationService flowRelationService;
    @Resource
    FlowViewServiceImpl flowViewService;
    @Resource
    StartDataCodesComponent startDataCodesComponent;
    @Resource
    RuleDetailInstanceService ruleDetailInstanceService;

    @Override
    public void process() {
        CalculateContext calculateContext = this.getContextBean("calculateContext");
        //
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = calculateContext.getTaskId();
        log.info("指标集开始组件开始执行，tag:{}", nodeId);
        //获取上下文中的参数
        Map<String, String> paramList = calculateContext.getParamList();
        StartNodeIndicatorColDto startNodeIndicatorColDto = BeanUtil.toBean(paramList, StartNodeIndicatorColDto.class);
        //获取组件参数中的参数
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null) {
            throw new BizException("组件配置信息不存在");
        }
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        StartNodeIndicatorColDto bean = BeanUtil.toBean(collect, StartNodeIndicatorColDto.class);
        StartNodeIndicatorColDto merge = merge(startNodeIndicatorColDto, bean);
        calculateContext.setStartTime(merge.getStartTime());
        calculateContext.setEndTime(merge.getEndTime());
        calculateContext.setInterval(merge.getInterval());
        calculateContext.setTsUnit(EnergyCalService.convertTsUnit(merge.getTsUnit()));
        Long indicatorsColId = merge.getRuleColId();
        RuleColVO ruleColVo = ruleColService.getVoById(indicatorsColId);
        List<AttrObject> attrList = ruleColVo.getAttrList();
        List<String> dataCodeList = new ArrayList<>();
        for (AttrObject attrObject : attrList) {
            String attrCode = attrObject.getAttrCode();
            dataCodeList.add(attrCode);
        }
        //递归获取计算指标所需的所有属性变量
        List<String> formulaVarList = energyCalService.getFormulaVarList(dataCodeList);
        //将devpropertyList按照项目分组,分组后的组成一个map,key是项目编码，value是devpropertyList
        Map<String, List<String>> groupedMap = energyCalService.groupDevpropertyByprojectCode(formulaVarList);
        //创建map,存放此次计算中涉及到的变量分类projectCode -> <calTypePropMap>
        Map<String, Map<String, List<String>>> projectCalTypePropMap = new HashMap<>();
        groupedMap.entrySet().forEach(entry -> {
            String projectCode = entry.getKey();
            List<String> devpropList = entry.getValue();
            //将采集属性/基础指标属性与聚合指标和衍生指标区分开，前者从数据库中查询，后者基于前者进行计算
//            Map<Integer, List<String>> calTypeConfigMap = GlobalUtil.calculateFormulaMap.getOrDefault(projectCode, new HashMap<>());
            Map<String, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ConstantBase.RULECOLID_COMMON);
            //此次计算涉及到的变量和其对应的指标类型
            Map<String, List<String>> calTypePropMap = energyCalService.groupDevpropertyByCalType(devpropList, calTypeConfigMap);
            projectCalTypePropMap.put(projectCode, calTypePropMap);
        });

        //创建临时表
        String tableId = UUIDUtils.uuidStdTableName();
        //创建需要查询数据的临时表
        startDataCodesComponent.createTale(tableId, formulaVarList);
        flowViewService.insertFlowTable(Integer.parseInt(flowId), tableId, nodeId, taskId);
        calculateContext.setProjectCalTypeMap(projectCalTypePropMap);
        calculateContext.setResultTable(tableId);
        log.info("指标集开始组件执行完毕");
    }


    /*
     * <AUTHOR>
     * @Description //合并上下文和组件参数中的类，上下文优先级高
     * @Date 9:05 2024/9/6
     * @Param
     * @return
     **/

    private StartNodeIndicatorColDto merge(StartNodeIndicatorColDto contextDto, StartNodeIndicatorColDto componentDto) {
        StartNodeIndicatorColDto result = new StartNodeIndicatorColDto();

        result.setRuleColId(Optional.ofNullable(contextDto.getRuleColId()).orElse(componentDto.getRuleColId()));
        result.setStartTime(Optional.ofNullable(contextDto.getStartTime()).orElse(componentDto.getStartTime()));
        result.setEndTime(Optional.ofNullable(contextDto.getEndTime()).orElse(componentDto.getEndTime()));
        result.setInterval(Optional.ofNullable(contextDto.getInterval()).orElse(componentDto.getInterval()));
        result.setTsUnit(Optional.ofNullable(contextDto.getTsUnit()).orElse(componentDto.getTsUnit()));

        return result;
    }

}
