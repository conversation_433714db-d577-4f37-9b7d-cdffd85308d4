package com.siact.energy.cal.server.core.calculator;

import com.siact.energy.cal.common.pojo.dto.energycal.FormulaClass;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.server.common.utils.CalculatorUtil;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.FormulaCacheService;
import com.siact.energy.cal.server.core.executor.HighPerformanceDerivedExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * 🔥 精简版衍生指标计算器
 * 
 * 核心功能：
 * 1. 智能计算模式选择
 * 2. 普通指标计算（公式计算）
 * 3. 同比环比指标计算（简化版）
 * 4. 列式计算优化
 */
@Slf4j
@Service
public class SimplifiedDerivedIndicatorCalculator {

    @Autowired
    private FormulaCacheService formulaCacheService;
    
    @Autowired
    private HighPerformanceDerivedExecutor highPerformanceExecutor;

    // 时间格式化器
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 专用线程池
    private final ForkJoinPool derivedCalculationPool = new ForkJoinPool(
            Math.min(Runtime.getRuntime().availableProcessors() * 2, 32));

    /**
     * 🔥 计算衍生指标 - 主入口方法
     */
    public CompletableFuture<Void> calculateDerivedIndicators(
            List<String> derivedIndicators,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            log.info("开始衍生指标计算，指标数量: {}", derivedIndicators.size());

            try {
                // 智能选择计算策略
                if (shouldUseHighPerformanceMode(derivedIndicators, timeWindows, queryDTO)) {
                    log.info("启用高性能计算模式");
                    highPerformanceExecutor.executeHighPerformanceCalculation(
                            derivedIndicators, timeWindows, queryDTO, globalResults).join();
                } else {
                    log.info("使用传统计算模式");
                    calculateTraditional(globalResults, derivedIndicators, queryDTO);
                }
                
                long duration = System.currentTimeMillis() - startTime;
                log.info("衍生指标计算完成，耗时: {}ms", duration);

            } catch (Exception e) {
                log.error("衍生指标计算失败", e);
            }
        }, derivedCalculationPool);
    }

    /**
     * 判断是否使用高性能模式
     */
    private boolean shouldUseHighPerformanceMode(List<String> derivedIndicators,
                                               List<TimeWindow> timeWindows,
                                               TimeQueryDTO queryDTO) {

        // 高性能模式触发条件
        boolean hasLargeIndicatorCount = derivedIndicators.size() > 50;
        boolean hasLongTimeSpan = timeWindows.size() > 100;
        boolean hasComplexQuery = queryDTO.isEquallySpacedQuery() &&
                                 queryDTO.getInterval() != null &&
                                 queryDTO.getInterval() <= 5;

        // 系统资源充足时优先使用高性能模式
        boolean hasGoodSystemResources = Runtime.getRuntime().availableProcessors() >= 4 &&
                                       Runtime.getRuntime().freeMemory() > 500 * 1024 * 1024;

        return (hasLargeIndicatorCount || hasLongTimeSpan || hasComplexQuery) && hasGoodSystemResources;
    }

    /**
     * 传统计算模式
     */
    private void calculateTraditional(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            List<String> propList,
            TimeQueryDTO timeQueryDTO) {

        // 1. 提取时间戳
        List<String> timePointList = extractTimePointsFromResultMap(resultMap);
        if (timePointList.isEmpty()) {
            log.warn("没有找到有效时间戳，衍生指标计算跳过");
            return;
        }

        // 2. 批量获取公式并排序
        Map<String, String> formulaMap = formulaCacheService.batchGetFormulas(propList, ConstantBase.RULECOLID_COMMON);

        List<FormulaClass> formulaList = propList.stream()
                .filter(targetProp -> StringUtils.isNotBlank(formulaMap.get(targetProp)))
                .map(targetProp -> new FormulaClass(targetProp, formulaMap.get(targetProp)))
                .collect(Collectors.toList());

        List<String> orderFormulaDevProp = FormulaUtils.calculateOrder(formulaList);

        // 3. 分类处理指标
        List<String> normalIndicators = new ArrayList<>();
        List<String> yoyMomIndicators = new ArrayList<>();

        for (String propCode : orderFormulaDevProp) {
            if (propList.contains(propCode)) {
                String formula = formulaMap.get(propCode);
                if (StringUtils.isNotBlank(formula)) {
                    if (formula.contains(ConstantBase.YOY) || formula.contains(ConstantBase.MOM)) {
                        yoyMomIndicators.add(propCode);
                    } else {
                        normalIndicators.add(propCode);
                    }
                }
            }
        }

        // 4. 处理普通指标
        if (!normalIndicators.isEmpty()) {
            processNormalIndicators(normalIndicators, timePointList, timeQueryDTO, resultMap, formulaMap);
        }

        // 5. 处理同比环比指标（简化版）
        if (!yoyMomIndicators.isEmpty()) {
            processYoYMoMIndicators(yoyMomIndicators, timePointList, timeQueryDTO, resultMap, formulaMap);
        }

        log.debug("衍生指标计算完成: 普通指标={}, 同比环比指标={}",
                normalIndicators.size(), yoyMomIndicators.size());
    }

    /**
     * 处理普通指标（公式计算）
     */
    private void processNormalIndicators(List<String> normalIndicators,
                                       List<String> timePointList,
                                       TimeQueryDTO timeQueryDTO,
                                       ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                       Map<String, String> formulaMap) {

        // 计算DURATION值
        Long duration = calculateDurationInSeconds(timeQueryDTO);

        // 转换为列式数据进行高效计算
        Map<String, Integer> timeIndexMap = CalculatorUtil.createTimeIndexMap(timePointList);
        Map<String, BigDecimal[]> columnData = CalculatorUtil.transposeToColumnar(
                resultMap, timePointList, timeIndexMap);

        // 逐个计算普通指标
        for (String indicator : normalIndicators) {
            String originalFormula = formulaMap.get(indicator);
            if (StringUtils.isBlank(originalFormula)) continue;

            try {
                // 处理DURATION替换
                String formula = originalFormula;
                if (duration != null && formula.contains(ConstantBase.DURATION)) {
                    formula = formula.replace(ConstantBase.DURATION, duration.toString());
                }

                // 获取依赖变量
                List<String> variables = FormulaUtils.getVarList(formula);

                // 检查依赖是否满足
                Map<String, BigDecimal[]> dependencyColumns = new HashMap<>();
                boolean dependenciesMet = true;

                for (String variable : variables) {
                    BigDecimal[] depColumn = columnData.get(variable);
                    if (depColumn == null) {
                        log.warn("指标 {} 依赖 {} 不存在，跳过计算", indicator, variable);
                        dependenciesMet = false;
                        break;
                    }
                    dependencyColumns.put(variable, depColumn);
                }

                if (!dependenciesMet) continue;

                // 列式计算
                BigDecimal[] resultColumn = performVectorizedCalculation(
                        formula, variables, dependencyColumns, timePointList.size());

                // 存储结果到列数据中
                columnData.put(indicator, resultColumn);

            } catch (Exception e) {
                log.error("指标 {} 计算失败", indicator, e);
            }
        }

        // 将计算结果合并回全局结果
        CalculatorUtil.mergeColumnarToRowBased(normalIndicators, columnData, timePointList, resultMap);
    }

    /**
     * 向量化计算（列式计算的核心）
     */
    private BigDecimal[] performVectorizedCalculation(String formula,
                                                    List<String> variables,
                                                    Map<String, BigDecimal[]> dependencyColumns,
                                                    int timePointCount) {

        BigDecimal[] resultColumn = new BigDecimal[timePointCount];
        Map<String, BigDecimal> valueMap = new HashMap<>(variables.size());

        // 向量化计算：一次性处理所有时间点
        for (int i = 0; i < timePointCount; i++) {
            valueMap.clear();
            boolean hasAllValues = true;

            // 收集当前时间点的所有依赖值
            for (String variable : variables) {
                BigDecimal value = dependencyColumns.get(variable)[i];
                if (value == null) {
                    hasAllValues = false;
                    break;
                }
                valueMap.put(variable, value);
            }

            // 如果所有依赖值都存在，则计算结果
            if (hasAllValues) {
                try {
                    resultColumn[i] = FormulaUtils.calcFormula(formula, valueMap);
                } catch (Exception e) {
                    log.debug("时间点 {} 计算失败: {}", i, e.getMessage());
                }
            }
        }

        return resultColumn;
    }

    /**
     * 处理同比环比指标（简化版）
     */
    private void processYoYMoMIndicators(List<String> yoyMomIndicators,
                                       List<String> timePointList,
                                       TimeQueryDTO timeQueryDTO,
                                       ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                       Map<String, String> formulaMap) {

        log.info("开始处理同比环比指标: {}", yoyMomIndicators.size());
        
        // 简化版：暂时跳过同比环比计算，避免复杂的基期数据查询
        // 在实际应用中，可以根据需要实现简化的同比环比逻辑
        log.warn("同比环比指标计算已简化，如需完整功能请使用高性能模式");
    }

    /**
     * 从结果中提取时间点
     */
    private List<String> extractTimePointsFromResultMap(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap) {

        Set<String> timePointSet = new HashSet<>();

        resultMap.forEach((indicator, timeValueMap) -> {
            if (!indicator.contains("YOY") && !indicator.contains("MOM")) {
                timePointSet.addAll(timeValueMap.keySet());
            }
        });

        List<String> timePoints = new ArrayList<>(timePointSet);
        timePoints.sort(String::compareTo);

        return timePoints;
    }

    /**
     * 计算DURATION值
     */
    private Long calculateDurationInSeconds(TimeQueryDTO queryDTO) {
        try {
            if (!queryDTO.isEquallySpacedQuery()) {
                // 区间查询：返回总时间跨度
                if (StringUtils.isNoneBlank(queryDTO.getStartTime(), queryDTO.getEndTime())) {
                    LocalDateTime startTime = LocalDateTime.parse(queryDTO.getStartTime(), DATE_TIME_FORMATTER);
                    LocalDateTime endTime = LocalDateTime.parse(queryDTO.getEndTime(), DATE_TIME_FORMATTER);
                    return ChronoUnit.SECONDS.between(startTime, endTime);
                }
            } else {
                // 等间隔查询：返回单个间隔的秒数
                Integer interval = queryDTO.getInterval();
                String tsUnit = queryDTO.getTsUnit();

                if (interval != null && StringUtils.isNotBlank(tsUnit)) {
                    switch (tsUnit.toLowerCase()) {
                        case "m": return interval * 60L;
                        case "h": return interval * 3600L;
                        case "d": return interval * 86400L;
                        case "n": return interval * 86400L * 30; // 月
                        case "y": return interval * 86400L * 365; // 年
                    }
                }
            }
        } catch (Exception e) {
            log.error("计算DURATION失败", e);
        }
        return null;
    }

    /**
     * 销毁方法
     */
    public void destroy() {
        if (derivedCalculationPool != null && !derivedCalculationPool.isShutdown()) {
            derivedCalculationPool.shutdown();
        }
    }
}
