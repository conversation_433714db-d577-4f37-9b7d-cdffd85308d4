package com.siact.energy.cal.common.pojo.dto.dataSource;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * 数据源表(DataSource) 查询DTO
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:31
 */
@ApiModel("数据源表查询DTO")
@Data
public class DataSourceQueryDTO {

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", position = 1)
    private String projectName;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称", position = 2)
    private String databaseName;

    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型", position = 3)
    private Integer dbType;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称", position = 4)
    private String db;

    /**
     * 状态（0-正常/1-中断），描述数据库的连接状态
     */
    @ApiModelProperty(value = "状态（0-正常/1-中断），描述数据库的连接状态", position = 5)
    private Integer status;

}

