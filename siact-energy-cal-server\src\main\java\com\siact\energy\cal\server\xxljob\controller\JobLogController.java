package com.siact.energy.cal.server.xxljob.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.flow.JobInfoDto;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobGroupDao;
import com.siact.energy.cal.server.xxljob.biz.ExecutorBiz;
import com.siact.energy.cal.server.xxljob.biz.model.KillParam;
import com.siact.energy.cal.server.xxljob.biz.model.LogParam;
import com.siact.energy.cal.server.xxljob.biz.model.LogResult;
import com.siact.energy.cal.server.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.server.xxljob.core.complete.XxlJobCompleter;
import com.siact.energy.cal.server.xxljob.core.exception.XxlJobException;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobGroup;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobInfo;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobInfoVO;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobLog;
import com.siact.energy.cal.server.xxljob.core.scheduler.XxlJobScheduler;
import com.siact.energy.cal.server.xxljob.core.util.I18nUtil;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobInfoDao;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobLogDao;
import com.siact.energy.cal.server.xxljob.util.DateUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.HtmlUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-01
 * @Description: 调度日志管理
 * @Version: 1.0
 */
@Controller
@RequestMapping("/joblog")
@ApiSort(120)
@Api(tags = {"调度日志管理"})
public class JobLogController {
	private static Logger logger = LoggerFactory.getLogger(JobLogController.class);

	@Resource
	private XxlJobGroupDao xxlJobGroupDao;
	@Resource
	public XxlJobInfoDao xxlJobInfoDao;
	@Resource
	public XxlJobLogDao xxlJobLogDao;

//	@RequestMapping
//	@ResponseBody
	public JSONObject index(HttpServletRequest request, Model model, @RequestParam(required = false, defaultValue = "0") Integer jobId) {

		JSONObject jsonObject = new JSONObject();
		// 执行器列表
		List<XxlJobGroup> jobGroupList_all =  xxlJobGroupDao.findAll();

		// filter group
		List<XxlJobGroup> jobGroupList = JobInfoController.filterJobGroupByRole(request, jobGroupList_all);
		if (jobGroupList==null || jobGroupList.size()==0) {
			throw new XxlJobException(I18nUtil.getString("jobgroup_empty"));
		}

		model.addAttribute("JobGroupList", jobGroupList);

		// 任务
		if (jobId > 0) {
			XxlJobInfo jobInfo = xxlJobInfoDao.loadById(jobId);
			if (jobInfo == null) {
				throw new RuntimeException(I18nUtil.getString("jobinfo_field_id") + I18nUtil.getString("system_unvalid"));
			}

			model.addAttribute("jobInfo", jobInfo);

			// valid permission
			JobInfoController.validPermission(request, jobInfo.getJobGroup());
		}
		jsonObject.put("JobGroupList", jobGroupList);

		//return "joblog/joblog.index";
		return jsonObject;
	}

//	@RequestMapping(value = "/getJobsByGroup", method = RequestMethod.GET)
//	@ApiOperation("获取任务列表")
//	@ResponseBody
//	public ReturnT<List<XxlJobInfo>> getJobsByGroup(@RequestParam(value = "执行器id,", defaultValue = "7")int jobGroup){
//		List<XxlJobInfo> list = xxlJobInfoDao.getJobsByGroup(jobGroup);
//		return new ReturnT<List<XxlJobInfo>>(list);
//	}
	
	@RequestMapping(value = "/pageList", method = RequestMethod.GET)
	@ApiOperation("获取日志列表")
	@ApiOperationSupport(order = 10)
	@ApiImplicitParams({
			@ApiImplicitParam(name = "start", value = "起始页码", dataType = "int"),
			@ApiImplicitParam(name = "length", value = "每页长度", dataType = "int"),
			@ApiImplicitParam(name = "jobGroup", value = "执行器id，默认值为7", dataType = "int"),
			@ApiImplicitParam(name = "jobId", value = "任务id", dataType = "int"),
			@ApiImplicitParam(name = "logStatus", value = "状态，默认值为-1:全部,1:成功,2:失败,3:进行中", dataType = "int"),
			@ApiImplicitParam(name = "filterTime", value = "时间过滤，参考xxljob 的日志日期搜索条件", dataType = "String")
	})
	@ResponseBody
	public R<Map<String, Object>> pageList(HttpServletRequest request,
										   @RequestParam(required = false, defaultValue = "0") int start,
										   @RequestParam(required = false, defaultValue = "10") int length,
										   @RequestParam(defaultValue = "7")int jobGroup,
										   @RequestParam(defaultValue = "0")int jobId,
										   @RequestParam(defaultValue = "-1")int logStatus,
										   String filterTime) {

		// valid permission
		//JobInfoController.validPermission(request, jobGroup);	// 仅管理员支持查询全部；普通用户仅支持查询有权限的 jobGroup

		// parse param
		Date triggerTimeStart = null;
		Date triggerTimeEnd = null;
		if (filterTime!=null && filterTime.trim().length()>0) {
			String[] temp = filterTime.split(" - ");
			if (temp.length == 2) {
				triggerTimeStart = DateUtil.parseDateTime(temp[0]);
				triggerTimeEnd = DateUtil.parseDateTime(temp[1]);
			}
		}
//		start = start == 1 ? 0 : start*length;
		
		// page query
		List<XxlJobLog> list = xxlJobLogDao.pageList(start, length, jobGroup, jobId, triggerTimeStart, triggerTimeEnd, logStatus);
		for (XxlJobLog xxlJobLog : list) {
			XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(xxlJobLog.getJobId());
			xxlJobLog.setJobDesc(xxlJobInfo.getJobDesc());
		}
		int list_count = xxlJobLogDao.pageListCount(start, length, jobGroup, jobId, triggerTimeStart, triggerTimeEnd, logStatus);
		
		// package result
		Map<String, Object> maps = new HashMap<String, Object>();
	    maps.put("recordsTotal", list_count);		// 总记录数
	    maps.put("recordsFiltered", list_count);	// 过滤后的总记录数
	    maps.put("data", list);  					// 分页列表
		return  R.OK(maps);
	}

//	@RequestMapping(value = "/logDetailPage", method = RequestMethod.GET)
	public String logDetailPage(int id, Model model){

		// base check
		ReturnT<String> logStatue = ReturnT.SUCCESS;
		XxlJobLog jobLog = xxlJobLogDao.load(id);
		if (jobLog == null) {
            throw new RuntimeException(I18nUtil.getString("joblog_logid_unvalid"));
		}

        model.addAttribute("triggerCode", jobLog.getTriggerCode());
        model.addAttribute("handleCode", jobLog.getHandleCode());
        model.addAttribute("logId", jobLog.getId());
		return "joblog/joblog.detail";
	}

	@RequestMapping(value = "/logDetailCat", method = RequestMethod.GET)
	@ApiOperation("获取日志内容")
	@ApiOperationSupport(order = 20)
	@ApiImplicitParams({
			@ApiImplicitParam(name = "logId", value = "日志id,默认值为1", dataType = "long", required = true),
			@ApiImplicitParam(name = "fromLineNum", value = "开始行数", dataType = "int", required = true)
	})
	@ResponseBody
	public ReturnT<LogResult> logDetailCat(long logId, int fromLineNum){
		try {
			// valid
			XxlJobLog jobLog = xxlJobLogDao.load(logId);	// todo, need to improve performance
			if (jobLog == null) {
				return new ReturnT<LogResult>(ReturnT.FAIL_CODE, I18nUtil.getString("joblog_logid_unvalid"));
			}

			// log cat
			ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(jobLog.getExecutorAddress());
			ReturnT<LogResult> logResult = executorBiz.log(new LogParam(jobLog.getTriggerTime().getTime(), logId, fromLineNum));

			// is end
            if (logResult.getContent()!=null && logResult.getContent().getFromLineNum() > logResult.getContent().getToLineNum()) {
                if (jobLog.getHandleCode() > 0) {
                    logResult.getContent().setEnd(true);
                }
            }

			// fix xss
			if (logResult.getContent()!=null && StringUtils.hasText(logResult.getContent().getLogContent())) {
				String newLogContent = logResult.getContent().getLogContent();
				newLogContent = HtmlUtils.htmlEscape(newLogContent, "UTF-8");
				logResult.getContent().setLogContent(newLogContent);
			}

			return logResult;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return new ReturnT<LogResult>(ReturnT.FAIL_CODE, e.getMessage());
		}
	}

	@RequestMapping(value = "/logKill", method = RequestMethod.POST)
	@ApiOperation("停止任务")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "日志id", dataType = "int", required = true)
	})
	@ApiOperationSupport(order = 30)
	@ResponseBody
	public ReturnT<String> logKill(int id){
		// base check
		XxlJobLog log = xxlJobLogDao.load(id);
		XxlJobInfo jobInfo = xxlJobInfoDao.loadById(log.getJobId());
		if (jobInfo==null) {
			return new ReturnT<String>(500, I18nUtil.getString("jobinfo_glue_jobid_unvalid"));
		}
		if (ReturnT.SUCCESS_CODE != log.getTriggerCode()) {
			return new ReturnT<String>(500, I18nUtil.getString("joblog_kill_log_limit"));
		}

		// request of kill
		ReturnT<String> runResult = null;
		try {
			ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(log.getExecutorAddress());
			runResult = executorBiz.kill(new KillParam(jobInfo.getId()));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			runResult = new ReturnT<String>(500, e.getMessage());
		}

		if (ReturnT.SUCCESS_CODE == runResult.getCode()) {
			log.setHandleCode(ReturnT.FAIL_CODE);
			log.setHandleMsg( I18nUtil.getString("joblog_kill_log_byman")+":" + (runResult.getMsg()!=null?runResult.getMsg():""));
			log.setHandleTime(new Date());
			XxlJobCompleter.updateHandleInfoAndFinish(log);
			return new ReturnT<String>(runResult.getMsg());
		} else {
			return new ReturnT<String>(500, runResult.getMsg());
		}
	}

	@RequestMapping(value = "/clearLog", method = RequestMethod.POST)
	@ApiOperation("清理日志")
	@ApiOperationSupport(order = 50)
	@ApiImplicitParams({
			@ApiImplicitParam(name = "JobInfoDto", value = "执行器id")
	})
	@ResponseBody
	public ReturnT<String> clearLog(@RequestBody JobInfoDto jobInfoDto){

		int type = jobInfoDto.getType();
		int jobId = jobInfoDto.getJobId();
		int jobGroup = 7;
		Date clearBeforeTime = null;
		int clearBeforeNum = 0;
		if (type == 1) {
			clearBeforeTime = DateUtil.addMonths(new Date(), -1);	// 清理一个月之前日志数据
		} else if (type == 2) {
			clearBeforeTime = DateUtil.addMonths(new Date(), -3);	// 清理三个月之前日志数据
		} else if (type == 3) {
			clearBeforeTime = DateUtil.addMonths(new Date(), -6);	// 清理六个月之前日志数据
		} else if (type == 4) {
			clearBeforeTime = DateUtil.addYears(new Date(), -1);	// 清理一年之前日志数据
		} else if (type == 5) {
			clearBeforeNum = 1000;		// 清理一千条以前日志数据
		} else if (type == 6) {
			clearBeforeNum = 10000;		// 清理一万条以前日志数据
		} else if (type == 7) {
			clearBeforeNum = 30000;		// 清理三万条以前日志数据
		} else if (type == 8) {
			clearBeforeNum = 100000;	// 清理十万条以前日志数据
		} else if (type == 9) {
			clearBeforeNum = 0;			// 清理所有日志数据
		} else {
			return new ReturnT<String>(ReturnT.FAIL_CODE, I18nUtil.getString("joblog_clean_type_unvalid"));
		}

		List<Long> logIds = null;
		do {
			logIds = xxlJobLogDao.findClearLogIds(jobGroup, jobId, clearBeforeTime, clearBeforeNum, 1000);
			if (logIds!=null && logIds.size()>0) {
				xxlJobLogDao.clearLog(logIds);
			}
		} while (logIds!=null && logIds.size()>0);

		return ReturnT.SUCCESS;
	}

	@RequestMapping(value = "/job/list", method = RequestMethod.GET)
	@ApiOperation("查询所有任务")
	@ApiOperationSupport(order = 60)
	@ResponseBody
	public ReturnT<List<XxlJobInfoVO>> jobList(){
		List<XxlJobInfoVO> xxlJobInfoVOS = new ArrayList<>();
		List<XxlJobInfo> xxlJobInfoList = xxlJobInfoDao.findAll();
		for (XxlJobInfo xxlJobInfo : xxlJobInfoList) {
			XxlJobInfoVO xxlJobInfoVO = new XxlJobInfoVO();
			xxlJobInfoVO.setJobDesc(xxlJobInfo.getJobDesc());
			xxlJobInfoVO.setId(xxlJobInfo.getId());
			xxlJobInfoVOS.add(xxlJobInfoVO);
		}
		return new ReturnT<>(xxlJobInfoVOS);
	}

}
