package com.siact.energy.cal.server.core.utils;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 时间窗口生成器
 * 基于TimeUtils.generateTimeGroups逻辑优化的时间窗口生成
 */
@Service
@Slf4j
public class TimeWindowGenerator {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");



    /**
     * 【新增】将一个大的时间范围按指定的粒度切分为多个小的时间分片。
     * <p>
     * 这个方法是“时间分片”查询策略的核心，用于将可能导致超时的长周期查询，
     * 分解为多个可在规定时间内完成的、可并行执行的短周期查询。
     *
     * @param startTimeStr  总任务的开始时间, 格式为 "yyyy-MM-dd HH:mm:ss"
     * @param endTimeStr    总任务的结束时间, 格式为 "yyyy-MM-dd HH:mm:ss"
     * @param shardAmount   每个分片的时长数量 (通常为 1)
     * @param shardUnit     分片的时间单位 (h, d, w, M, y)
     * @return              切分好的 TimeWindow 列表
     */
    public List<TimeWindow> generateShards(String startTimeStr, String endTimeStr, int shardAmount, String shardUnit) {
        if (startTimeStr == null || endTimeStr == null || shardUnit == null || shardAmount <= 0) {
            // 参数无效，返回一个包含完整时间范围的单一分片
            return Collections.singletonList(new TimeWindow(startTimeStr, endTimeStr, startTimeStr));
        }

        List<TimeWindow> shards = new ArrayList<>();
        try {
            LocalDateTime start = LocalDateTime.parse(startTimeStr, DATE_TIME_FORMATTER);
            LocalDateTime end = LocalDateTime.parse(endTimeStr, DATE_TIME_FORMATTER);

            ChronoUnit chronoUnit = convertToChronoUnit(shardUnit);
            if (chronoUnit == null) {
                // 如果单位无效，则不分片
                shards.add(new TimeWindow(startTimeStr, endTimeStr, startTimeStr));
                return shards;
            }

            LocalDateTime currentStart = start;
            while (currentStart.isBefore(end)) {
                LocalDateTime currentEnd = currentStart.plus(shardAmount, chronoUnit);
                if (currentEnd.isAfter(end)) {
                    currentEnd = end;
                }

                // 创建分片，alignedTime 默认为分片的开始时间
                shards.add(new TimeWindow(
                        currentStart.format(DATE_TIME_FORMATTER),
                        currentEnd.format(DATE_TIME_FORMATTER),
                        currentStart.format(DATE_TIME_FORMATTER)
                ));
                currentStart = currentEnd;
            }

            // 处理起止时间相同或非常接近，导致 while 循环未执行的边界情况
            if (shards.isEmpty() && !start.isAfter(end)) {
                shards.add(new TimeWindow(startTimeStr, endTimeStr, startTimeStr));
            }

        } catch (Exception e) {
            log.error("时间分片生成失败，将返回整个时间范围作为一个分片。原因: {}", e.getMessage(), e);
            // 降级处理：如果分片逻辑出错，返回单一分片，确保计算能继续
            return Collections.singletonList(new TimeWindow(startTimeStr, endTimeStr, startTimeStr));
        }
        return shards;
    }

    /**
     * 【新增】一个静态的辅助方法，用于 QueryPlanner 计算天数差。
     * 设为 public static，以便 QueryPlanner 可以直接调用。
     */
    public static long getDaysBetween(String startStr, String endStr) {
        if (startStr == null || endStr == null) return 0;
        try {
            LocalDateTime start = LocalDateTime.parse(startStr, DATE_TIME_FORMATTER);
            LocalDateTime end = LocalDateTime.parse(endStr, DATE_TIME_FORMATTER);
            return Math.max(0, ChronoUnit.DAYS.between(start, end)); // 确保返回非负天数
        } catch (Exception e) {
            log.warn("解析时间范围以计算天数差时失败: start={}, end={}", startStr, endStr);
            return 0; // 解析失败，安全返回0
        }
    }

    /**
     * 【新增】私有辅助方法，将我们的单位字符转换为 java.time.temporal.ChronoUnit
     */
    private ChronoUnit convertToChronoUnit(String unit) {
        if (unit == null) return null;
        switch (unit.toLowerCase()) {
            case "h": return ChronoUnit.HOURS;
            case "d": return ChronoUnit.DAYS;
            case "w": return ChronoUnit.WEEKS;
            case "m": // 明确 'm' 在分片场景下代表月
            case "n":
                return ChronoUnit.MONTHS;
            case "y": return ChronoUnit.YEARS;
            default:
                log.warn("不支持的分片时间单位: [{}]", unit);
                return null;
        }
    }
    /**
     * 生成时间窗口
     */
    public List<TimeWindow> generateTimeWindows(TimeQueryDTO queryDTO) {
        try {
            log.debug("开始生成时间窗口: 开始时间={}, 结束时间={}, 间隔={}, 单位={}",
                queryDTO.getStartTime(), queryDTO.getEndTime(), queryDTO.getInterval(), queryDTO.getTsUnit());

            // 使用优化的时间窗口生成逻辑
            List<TimeWindow> timeWindows = generateOptimizedTimeWindows(
                queryDTO.getStartTime(),
                queryDTO.getEndTime(),
                queryDTO.getInterval(),
                queryDTO.getTsUnit()
            );

            log.info("时间窗口生成完成: 数量={}, 时间跨度={}到{}",
                timeWindows.size(),
                timeWindows.isEmpty() ? "无" : timeWindows.get(0).getStartTime(),
                timeWindows.isEmpty() ? "无" : timeWindows.get(timeWindows.size() - 1).getEndTime());

            return timeWindows;

        } catch (Exception e) {
            log.error("生成时间窗口失败", e);
            throw new RuntimeException("时间窗口生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为时间区间计算创建时间窗口
     */
    public List<TimeWindow> createIntervalTimeWindow(TimeQueryDTO queryDTO) {
        List<TimeWindow> timeWindows = new ArrayList<>();

        // 创建一个包含开始时间和结束时间的时间窗口
        TimeWindow intervalWindow = new TimeWindow(
                queryDTO.getStartTime(),  // startTime
                queryDTO.getEndTime(),    // endTime
                queryDTO.getStartTime()   // alignedTime - 使用开始时间作为对齐时间
        );

        timeWindows.add(intervalWindow);

        log.debug("为时间区间计算创建时间窗口: {} - {}", queryDTO.getStartTime(), queryDTO.getEndTime());

        return timeWindows;
    }

    /**
     * 优化的时间窗口生成方法
     * 基于TimeUtils.generateTimeGroups逻辑，增强时间窗口对象
     */
    private List<TimeWindow> generateOptimizedTimeWindows(String startTimeStr, String endTimeStr, int interval, String unit) {

        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, DATE_TIME_FORMATTER);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, DATE_TIME_FORMATTER);


        // 对齐开始时间到间隔边界（用于计算alignedTime）
        LocalDateTime alignedStartTime = alignToIntervalBoundary(startTime, interval, unit);
        List<TimeWindow> timeWindows = new ArrayList<>();

        // 关键修改：第一个窗口从用户指定的开始时间开始，而不是对齐后的时间
        LocalDateTime currentStartTime = startTime;

        // 计算第一个窗口的对齐时间
        LocalDateTime currentAlignedTime = alignedStartTime;

        while (currentStartTime.isBefore(endTime)) {
            LocalDateTime groupEndTime;
            LocalDateTime nextAlignedTime;

            if (currentStartTime.equals(startTime)) {
                // 第一个窗口：从用户开始时间到下一个整点
                nextAlignedTime = addInterval(currentAlignedTime, interval, unit);
                groupEndTime = nextAlignedTime.isBefore(endTime) ? nextAlignedTime : endTime;
            } else {
                // 后续窗口：按标准间隔
                groupEndTime = addInterval(currentStartTime, interval, unit);
                nextAlignedTime = addInterval(currentAlignedTime, interval, unit);

                // 如果分组结束时间超过了总结束时间，则使用总结束时间
                if (groupEndTime.isAfter(endTime)) {
                    groupEndTime = endTime;
                }
            }

            // 创建时间窗口对象
            TimeWindow timeWindow = new TimeWindow(
                currentStartTime.format(DATE_TIME_FORMATTER),    // startTime (实际窗口开始时间)
                groupEndTime.format(DATE_TIME_FORMATTER),        // endTime (实际窗口结束时间)
                currentAlignedTime.format(DATE_TIME_FORMATTER)   // alignedTime (对齐后的时间，用于结果返回)
            );

            timeWindows.add(timeWindow);
            currentStartTime = groupEndTime;
            currentAlignedTime = nextAlignedTime;
        }

        return timeWindows;
    }

    /**
     * 对齐时间到间隔边界（参照TimeUtils逻辑）
     */
    private LocalDateTime alignToIntervalBoundary(LocalDateTime time, int interval, String unit) {
        switch (unit) {
            case "m":
                // 对齐到分钟边界
                int alignedMinute = (time.getMinute() / interval) * interval;
                return time.withMinute(alignedMinute).withSecond(0).withNano(0);
            case "h":
                // 对齐到小时边界
                return time.withMinute(0).withSecond(0).withNano(0);
            case "d":
                // 对齐到天边界
                return time.withHour(0).withMinute(0).withSecond(0).withNano(0);
            case "n":
                // 对齐到月边界
                return time.withDayOfMonth(1).withHour(0).withMinute(0)
                        .withSecond(0).withNano(0);
            case "y":
                // 对齐到年边界
                return time.withMonth(1).withDayOfMonth(1).withHour(0)
                        .withMinute(0).withSecond(0).withNano(0);
            default:
                throw new IllegalArgumentException("不支持的时间单位: " + unit);
        }
    }

    /**
     * 添加时间间隔（参照TimeUtils逻辑）
     */
    private LocalDateTime addInterval(LocalDateTime time, int interval, String unit) {
        switch (unit) {
            case "m":
                return time.plusMinutes(interval);
            case "h":
                return time.plusHours(interval);
            case "d":
                return time.plusDays(interval);
            case "n":
                return time.plusMonths(interval);
            case "y":
                return time.plusYears(interval);
            default:
                throw new IllegalArgumentException("不支持的时间单位: " + unit);
        }
    }

}
