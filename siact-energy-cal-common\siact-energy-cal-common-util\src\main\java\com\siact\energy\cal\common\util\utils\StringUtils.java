package com.siact.energy.cal.common.util.utils;

import java.util.Set;
import java.util.TreeSet;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-03
 * @Description: 字符串工具
 * @Version: 1.0
 */
public class StringUtils {

    public static Set<String> getCode(String str){
        Set<String> codeList = new TreeSet<>();
        String[] split = str.split(",");
        for (String st : split) {
            if (st.contains("@[")){
                int i = st.indexOf("@[");
                String substring = st.substring(i+2, st.trim().lastIndexOf("]")).trim();

                substring = substring.replaceAll("]", "").replaceAll("\\[", "").replaceAll("\"","");
                codeList.add(substring);
            }
        }
        return codeList;
    }

    public static String getRuleFormulaStr(String str){
        str = str.replaceAll("\\[", "")
                .replaceAll("]", "")
                .replaceAll("@", "")
                .replaceAll("#", "")
                .replaceAll("\"", "")
                .replaceAll(",", "")
                .replaceAll("\r", "")
                .replaceAll("\n", "")
                .replaceAll("math.", "");
        return str;
    }

    public static void main(String[] args) {
        String str = " [\"#[abs]\",\"(\",\"@[PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPSHL2]\",\")\",\"*\",\"1\",\"0\",\"+\",\"#[sqrt]\",\"(\",\"@[PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPFZL2]\",\")\",\"/\",\"1\",\"0\",\"0\"]";

        String ruleFormulaStr = getRuleFormulaStr(str);
        System.out.println(ruleFormulaStr);

    }
}
