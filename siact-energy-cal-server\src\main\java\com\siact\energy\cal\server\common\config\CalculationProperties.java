package com.siact.energy.cal.server.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "calculation.planner")
@Data
public class CalculationProperties {

    // ========== 原有配置（针对TDengine优化） ==========
    /** 当查询时长超过此天数(且为等间隔查询)，触发时间分片 */
    private int timeShardThresholdDays = 3; // 降低到3天，更早触发时间分片
    /** 当一次查询的指标数量超过此数量时，触发指标分片 */
    private int metricShardThresholdCount = 30; // 降低到30个指标
    /** 每个指标分片最多包含多少个指标 */
    private int metricShardSize = 25; // 降低到25个指标/分片
    /** 应用层同时最多向数据库发起多少个查询子任务 */
    private int maxConcurrentQueries = 8; // 降低并发数，避免连接池耗尽

    // ========== 智能分片新增配置 ==========
    /** 是否启用智能分片策略 */
    private boolean enableIntelligentSharding = true;

    /** 复杂度阈值配置 */
    private ComplexityThresholds complexity = new ComplexityThresholds();

    /** 分片大小配置 */
    private ShardingSizes sharding = new ShardingSizes();

    /** 并发控制配置 */
    private ConcurrencyControl concurrency = new ConcurrencyControl();

    /** 系统监控配置 */
    private SystemMonitoring monitoring = new SystemMonitoring();

    /** 性能优化配置 */
    private PerformanceOptimization performance = new PerformanceOptimization();

    @Data
    public static class ComplexityThresholds {
        /** 高复杂度阈值 */
        private double highComplexity = 10.0;
        /** 中等复杂度阈值 */
        private double mediumComplexity = 5.0;
        /** 时间复杂度权重 */
        private double timeWeight = 0.3;
        /** 指标复杂度权重 */
        private double metricWeight = 0.4;
        /** 数据量复杂度权重 */
        private double dataVolumeWeight = 0.3;
    }

    @Data
    public static class ShardingSizes {
        /** 最小时间分片天数 */
        private int minTimeShardDays = 1;
        /** 最大时间分片天数 - 针对TDengine优化 */
        private int maxTimeShardDays = 7; // 降低到7天
        /** 最小指标分片大小 - 针对TDengine优化 */
        private int minMetricShardSize = 10; // 降低到10个
        /** 最大指标分片大小 - 针对TDengine优化 */
        private int maxMetricShardSize = 30; // 降低到30个
        /** 自适应分片的时间/指标比例阈值 */
        private double adaptiveRatio = 5.0; // 降低阈值，更容易触发分片
    }

    @Data
    public static class ConcurrencyControl {
        /** 最小并发数 - 针对TDengine优化 */
        private int minConcurrency = 2; // 降低最小并发
        /** 最大并发数 - 针对TDengine优化 */
        private int maxConcurrency = 12; // 降低最大并发，避免连接池耗尽
        /** 基础并发数 - 针对TDengine优化 */
        private int baseConcurrency = 6; // 降低基础并发
        /** 高复杂度查询并发数减半 */
        private boolean reduceForHighComplexity = true;
        /** 低复杂度查询并发数加倍 */
        private boolean increaseForLowComplexity = false; // 关闭加倍，保持保守策略
    }

    @Data
    public static class SystemMonitoring {
        /** CPU使用率阈值（超过此值认为系统过载） */
        private double cpuThreshold = 80.0;
        /** 内存使用率阈值（超过此值认为系统过载） */
        private double memoryThreshold = 85.0;
        /** 活跃连接数阈值 */
        private int activeConnectionThreshold = 50;
        /** 是否启用系统监控 */
        private boolean enableSystemMonitoring = true;
        /** 监控数据刷新间隔（秒） */
        private int monitoringInterval = 5;
    }

    @Data
    public static class PerformanceOptimization {
        /** 慢查询阈值（毫秒） - 针对TDengine调整 */
        private long slowQueryThreshold = 3000; // 降低到3秒
        /** 快查询阈值（毫秒） */
        private long fastQueryThreshold = 1000;
        /** 是否启用查询性能监控 */
        private boolean enableQueryMonitoring = true;
        /** 是否启用自适应重分片 */
        private boolean enableAdaptiveResharding = false;
        /** 智能分片检测阈值（指标数量） - 针对TDengine优化 */
        private int intelligentShardingDetectionThreshold = 30; // 降低到30个指标就启用智能分片
    }

    // ========== 便捷方法 ==========

    /**
     * 判断是否应该使用智能分片
     */
    public boolean shouldUseIntelligentSharding() {
        return enableIntelligentSharding;
    }

    /**
     * 获取当前有效的时间分片阈值
     */
    public int getEffectiveTimeShardThreshold() {
        return shouldUseIntelligentSharding() ?
                sharding.getMaxTimeShardDays() : timeShardThresholdDays;
    }

    /**
     * 获取当前有效的指标分片阈值
     */
    public int getEffectiveMetricShardThreshold() {
        return shouldUseIntelligentSharding() ?
                sharding.getMaxMetricShardSize() : metricShardThresholdCount;
    }

    /**
     * 获取当前有效的最大并发数
     */
    public int getEffectiveMaxConcurrency() {
        return shouldUseIntelligentSharding() ?
                concurrency.getMaxConcurrency() : maxConcurrentQueries;
    }
}