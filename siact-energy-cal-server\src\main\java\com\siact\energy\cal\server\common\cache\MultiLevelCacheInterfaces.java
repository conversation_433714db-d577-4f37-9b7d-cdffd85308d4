package com.siact.energy.cal.server.common.cache;

import java.util.Collection;
import java.util.Map;

/**
 * 多级缓存加载器函数式接口定义
 * <p>
 * 用于向 MultiLevelCacheTemplate 提供具体的业务加载逻辑。
 */
public interface MultiLevelCacheInterfaces {

    /**
     * 从源头（DB/API）加载单个值的函数式接口。
     *
     * @param <K> 用于从源头加载的Key类型
     * @param <V> 返回值的类型
     */
    @FunctionalInterface
    interface SourceLoader<K, V> {
        /**
         * 根据指定的key加载数据。
         * @param key 用于加载数据的键。
         * @return 加载到的数据，如果不存在则返回null。
         */
        V load(K key);
    }

    /**
     * 从源头（DB/API）批量加载多个值的函数式接口。
     *
     * @param <K> 用于从源头加载的Key类型
     * @param <V> 返回值的类型
     */
    @FunctionalInterface
    interface BatchSourceLoader<K, V> {
        /**
         * 根据一批key加载数据。
         * @param keys 需要加载数据的键集合。
         * @return 一个Map，Key是传入的键，Value是对应的数据。对于未找到的key，Map中可以不包含该条目。
         */
        Map<K, V> load(Collection<K> keys);
    }
}