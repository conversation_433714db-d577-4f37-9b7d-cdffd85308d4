package com.siact.energy.cal.server.core.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QueryCost {
    /** 时间复杂度：时间跨度 * 采样频率 */
    private long timeComplexity;
    
    /** 指标复杂度：指标数量 */
    private int metricComplexity;
    
    /** 数据量复杂度：预期数据点数 */
    private long dataVolumeComplexity;
    
    /** 总体复杂度评分 */
    private double totalScore;
    
    /**
     * 计算总体复杂度评分
     */
    public double calculateTotalScore() {
        // 基于经验公式计算复杂度评分
        this.totalScore = Math.log(timeComplexity + 1) * 0.3 
                        + Math.log(metricComplexity + 1) * 0.4
                        + Math.log(dataVolumeComplexity + 1) * 0.3;
        return this.totalScore;
    }
    
    /**
     * 判断是否为高复杂度查询
     */
    public boolean isHighComplexity() {
        return calculateTotalScore() > 10.0;
    }
    
    /**
     * 判断是否为中等复杂度查询
     */
    public boolean isMediumComplexity() {
        double score = calculateTotalScore();
        return score > 5.0 && score <= 10.0;
    }
    
    /**
     * 判断是否为低复杂度查询
     */
    public boolean isLowComplexity() {
        return calculateTotalScore() <= 5.0;
    }
}