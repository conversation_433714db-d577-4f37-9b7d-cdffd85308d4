package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.StartCronNodeDataCodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.StartNodeDataCodeDto;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.GlobalUtil;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailInstanceService;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-08-19
 * @Description: 开始定时组件-设备实例编码
 * @Version: 1.0
 */
@Component("startCronDataCodesNode")
@Slf4j
public class StartCronDataCodesComponent extends NodeComponent  {

    @Resource
    EnergyCalService energyCalService;
    @Resource
    HikariDataSource hikariDataSource;
    @Resource
    IFlowRelationService flowRelationService;
    @Resource
    FlowViewServiceImpl flowViewService;
    @Resource
    StartDataCodesComponent startDataCodesComponent;
    @Resource
    RuleDetailInstanceService ruleDetailInstanceService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Override
    public void process() {
        CalculateContext calculateContext = this.getContextBean("calculateContext");

        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = calculateContext.getTaskId();
        log.info("设备属性开始定时组件开始执行，tag:{}",nodeId);

        //获取组件参数中的参数
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置信息不存在");
        }
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        StartCronNodeDataCodeDto bean = BeanUtil.toBean(collect, StartCronNodeDataCodeDto.class);
        String[] timeRange = calculateTimeRange(System.currentTimeMillis(), bean.interval);
        calculateContext.setStartTime(timeRange[0]);
        calculateContext.setEndTime(timeRange[1]);
        calculateContext.setInterval(bean.getInterval());
        calculateContext.setTsUnit(EnergyCalService.convertTsUnit("MIN"));
        List<String> dataCodeList = bean.getIndicatorList();
        //递归获取计算指标所需的所有属性变量
        List<String> formulaVarList = energyCalService.getFormulaVarList(dataCodeList);
        //将devpropertyList按照项目分组,分组后的组成一个map,key是项目编码，value是devpropertyList
        Map<String, List<String>> groupedMap = energyCalService.groupDevpropertyByprojectCode(formulaVarList);
        //创建map,存放此次计算中涉及到的变量分类projectCode -> <calTypePropMap>
        Map<String, Map<String, List<String>>> projectCalTypePropMap = new HashMap<>();
        groupedMap.entrySet().forEach(entry -> {
            String projectCode = entry.getKey();
            List<String> devpropList = entry.getValue();
            //将采集属性/基础指标属性与聚合指标和衍生指标区分开，前者从数据库中查询，后者基于前者进行计算
//            Map<Integer, List<String>> calTypeConfigMap = GlobalUtil.calculateFormulaMap.getOrDefault(projectCode, new HashMap<>());
            Map<String, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ConstantBase.RULECOLID_COMMON);
            //此次计算涉及到的变量和其对应的指标类型
            Map<String, List<String>> calTypePropMap = energyCalService.groupDevpropertyByCalType(devpropList, calTypeConfigMap);
            projectCalTypePropMap.put(projectCode,calTypePropMap);
        });

        //创建临时表
        String tableId = UUIDUtils.uuidStdTableName();
        log.info("临时表==="+tableId);
        //创建需要查询数据的临时表
        startDataCodesComponent.createTale(tableId,formulaVarList);
        flowViewService.insertFlowTable(Integer.parseInt(flowId), tableId, nodeId, taskId);
        calculateContext.setProjectCalTypeMap(projectCalTypePropMap);
        calculateContext.setResultTable(tableId);
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();
        calculateContext.setResultMap(resultMap);
        log.info("设备属性开始定时组件执行完毕");
    }



    /**
     * 计算定时触发的时间区间。
     *
     * @param currentTimeMillis 当前时间（以毫秒为单位）。
     * @param intervalMinutes   时间间隔，分钟为单位。
     * @return 一个包含开始时间和结束时间的数组，其中 [0] 是开始时间，[1] 是结束时间。
     */
    public static String[] calculateTimeRange(long currentTimeMillis, int intervalMinutes) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        calendar.setTimeInMillis(currentTimeMillis);

        // 将时间调整到当前时间间隔的起始点
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int minutesOffset = calendar.get(Calendar.MINUTE) % intervalMinutes;
        if (minutesOffset != 0) {
            calendar.add(Calendar.MINUTE, -minutesOffset);
        }

        // 计算结束时间（当前时间）
        long endTimeMillis = calendar.getTimeInMillis();

        // 计算开始时间（结束时间减去时间间隔）
        long startTimeMillis = endTimeMillis - intervalMinutes * 60 * 1000;

        // 格式化时间
        String startTimeStr = sdf.format(startTimeMillis);
        String endTimeStr = sdf.format(endTimeMillis);

        return new String[]{startTimeStr, endTimeStr};
    }
}
