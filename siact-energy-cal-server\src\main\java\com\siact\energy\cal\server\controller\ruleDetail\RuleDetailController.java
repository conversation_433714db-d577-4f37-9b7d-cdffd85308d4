package com.siact.energy.cal.server.controller.ruleDetail;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.domain.ResponseCodeConstant;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibSelectQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.*;
import com.siact.energy.cal.common.pojo.enums.ActiveStateEnum;
import com.siact.energy.cal.common.pojo.enums.CalTypeEnum;
import com.siact.energy.cal.common.pojo.enums.RuleTypeEnum;
import com.siact.energy.cal.common.pojo.validator.EnumValidator;
import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import com.siact.energy.cal.common.pojo.vo.common.DigitalTwinTreeVO;
import com.siact.energy.cal.common.pojo.vo.common.FunLibSelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.FormulaTreeVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import com.siact.energy.cal.server.service.funLib.FunLibService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 指标详情表(RuleDetail)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Api(tags = {"指标详情表"})
@ApiSort(50)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/rule/detail")
public class RuleDetailController {

    private final RuleDetailService ruleDetailService;

    private final FunLibService funLibService;

    /**
     * 指标类型列表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "指标类型列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 8)
    @GetMapping("/ruleType/list")
    public R<List<SelectOptionVO>> listRuleType() {
        return R.OK(RuleTypeEnum.list());
    }

    /**
     * 计算类型列表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "计算类型列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 9)
    @GetMapping("/calType/list")
    public R<List<SelectOptionVO>> listCalType() {
        return R.OK(CalTypeEnum.list());
    }

    /**
     * 分页列表
     *
     * @param page               分页对象
     * @param ruleDetailQueryDTO 查询实体
     * @return 查询结果
     */
    @ApiOperation(value = "分页列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 10, ignoreParameters = {"total", "pages", "records", "orders", "id"})
    @GetMapping("/listPage")
    public R<PageBean<RuleDetailVO>> listPage(PageBean<RuleDetailVO> page, /*@Validated*/ RuleDetailQueryDTO ruleDetailQueryDTO) {
        return R.OK(ruleDetailService.listPage(page, ruleDetailQueryDTO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    @ApiOperation(value = "通过主键查询单条数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 20)
    @GetMapping("{id}")
    public R<RuleDetailVO> selectOne(@PathVariable Serializable id) {
        return R.OK(ruleDetailService.getVoById(id));
    }

    /**
     * 新增数据
     *
     * @param ruleDetailInsertDTO 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ruleDetailInsertDTO", value = "实体对象", dataType = "指标详情表新增DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 30, ignoreParameters = {"id"})
    @PostMapping
    public R<Boolean> insert(@RequestBody @Validated RuleDetailInsertDTO ruleDetailInsertDTO) {
        ruleDetailService.verifyInsertParam(ruleDetailInsertDTO);
        Boolean save = ruleDetailService.save(ruleDetailInsertDTO);
        return R.OK(save);
    }

    /**
     * 修改数据
     *
     * @param ruleDetailUpdateDTO 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ruleDetailUpdateDTO", value = "实体对象", dataType = "指标详情表更新DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 40)
    @PutMapping
    public R<Boolean> update(@RequestBody @Validated(UpdateValidGroup.class) RuleDetailUpdateDTO ruleDetailUpdateDTO) {
        ruleDetailService.verifyUpdateParam(ruleDetailUpdateDTO);
        Boolean update = ruleDetailService.updateVoById(ruleDetailUpdateDTO);
        return R.OK(update);
    }

    /**
     * 删除数据
     *
     * @param ids 主键集合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 50)
    @DeleteMapping
    public R<Boolean> delete(@RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        boolean deleted = ruleDetailService.deleteByIds(ids);
        return R.OK(deleted);
    }

    /**
     * 模型树
     *
     * @param dataCode 项目模型数字化编码
     * @param useCache 是否使用缓存
     * @return 模型树数据
     */
    @ApiOperation(value = "模型树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCode", value = "项目模型数字化编码", dataType = "String", defaultValue = "PGY02_S0000_ST00000_U00000_EQ000000000_MP0000", example = "PGY02_S0000_ST00000_U00000_EQ000000000_MP0000"),
            @ApiImplicitParam(name = "useCache", value = "是否使用缓存，刷新的时候设置为false", dataType = "boolean", defaultValue = "true", example = "true")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 60)
    @GetMapping("/modelTree")
    public R<List<DigitalTwinTreeVO>> modelTree(@RequestParam(required = false, defaultValue = "PGY02_S0000_ST00000_U00000_EQ000000000_MP0000") String dataCode,
                                                @RequestParam(value = "useCache", required = false, defaultValue = "true") boolean useCache) {
        if(!useCache) {
            ruleDetailService.clearModelTreeCache(dataCode);
        }
        return R.OK(ruleDetailService.modelTree(dataCode));
    }

    /**
     * 实例树
     *
     * @param dataCode 项目实例数字化编码
     * @return 实例树数据
     */
    @ApiOperation(value = "实例树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCode", value = "项目实例数字化编码", dataType = "String", required = true, example = "PGY02014_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000"),
            @ApiImplicitParam(name = "useCache", value = "是否使用缓存，刷新的时候设置为false", dataType = "boolean", defaultValue = "true", example = "true")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @GetMapping("/insTree")
    public R<List<DigitalTwinTreeVO>> insTree(@RequestParam @NotBlank(message = "数字化编码不能为空") String dataCode,
                                              @RequestParam(value = "useCache", required = false, defaultValue = "true") boolean useCache) {
        if(!useCache) {
            ruleDetailService.clearInsTreeCache(dataCode);
        }
        return R.OK(ruleDetailService.insTree(dataCode));
    }

    /**
     * 属性查询
     * @param dataCode 父级实例编码，可以是项目，系统，站，单元和设备
     * @return 实例树数据
     */
    @ApiOperation(value = "查询属性实例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCode", value = "父级实例数字化编码,可以是项目，系统，站，单元和设备", dataType = "String", required = true, example = "PGY02014_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000"),
            @ApiImplicitParam(name = "nodeType", value = "节点类型", dataType = "String", defaultValue = "true", example = "项目：project；系统：system；站点：station；单元：unit；管路：pipe；设备：eq；表计：meter")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @GetMapping("/getProp")
    public R<List<DigitalTwinTreeVO>> getProp(@RequestParam @NotBlank(message = "数字化编码不能为空") String dataCode,
                                              @RequestParam(value = "nodeType", required = true) String nodeType) {

        return R.OK(ruleDetailService.getProp(dataCode, nodeType));
    }



    /**
     * 函数库列表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "函数库列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 80)
    @GetMapping("/funLib/list")
    public R<List<FunLibSelectOptionVO>> listFunLib(FunLibSelectQueryDTO dto) {
        return R.OK(funLibService.selectList(dto));
    }

    /**
     * 指标表达式基本校验
     *
     * @param dto 实体对象
     * @return 校验结果
     */
    @ApiOperation(value = "指标表达式基本校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", value = "实体对象", dataType = "指标表达式校验DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 90, ignoreParameters = {"id"})
    @PostMapping("/verifyExpression")
    public R<Boolean> verifyExpression(@RequestBody @Validated RuleDetailVerifyExpressionDTO dto) {
        return R.OK(ruleDetailService.verifyExpression(dto));
    }


    /**
     * 查看公式（方便查看公式和公式层级）
     *
     * @return 校验结果
     */
    @ApiOperation(value = "查看公式")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 90, ignoreParameters = {"id"})
    @GetMapping("/viewFormula")
    public R<PageBean<RuleDetailViewDTO>> viewFormula( @RequestParam("id") Long id,
                                                   PageBean<RuleDetailViewDTO> page) {
        return R.OK(ruleDetailService.viewFormula(id,page));
    }

    /**
     * 公式层级树（查看公式引用）
     *
     * @return 校验结果
     */
    @ApiOperation(value = "查看公式层级树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 90, ignoreParameters = {"id"})
    @PostMapping("/viewFormulaTree")
    public R<FormulaTreeDTO> viewFormulaTree(@RequestBody FormulaTreeVO formulaTreeVO) {
        return R.OK(ruleDetailService.viewFormulaTree(formulaTreeVO));
    }
    /**
     * 获取所有计算指标,返回仅需要设备属性名称和编码
     *
     * @return 校验结果
     */
    @ApiOperation(value = "获取指标列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 90, ignoreParameters = {"id"})
    @GetMapping("/indicatorList")
    public R<List<SelectOptionVO>> getIndicatorList() {
        return R.OK(ruleDetailService.getIndicatorList());
    }
    /**
     * 启用/未启用
     *
     * @param activeState 启用/未启用
     * @param ids  主键集合
     * @return 返回
     */
    @ApiOperation(value = "启用/未启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "activeState", value = ActiveStateEnum.TIPS, dataType = "String", required = true),
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 100)
    @PutMapping("/toggle/{activeState}")
    public R<Boolean> toggle(
            @PathVariable("activeState") @EnumValidator(enumClass = ActiveStateEnum.class, existGetMethod = "getValue", message = ActiveStateEnum.TIPS) Integer activeState,
            @RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        Boolean toggle = ruleDetailService.toggle(activeState, ids);
        //全量更新到全局变量中
//        ruleDetailService.getAllFormulaDetail();
        return R.OK(toggle);
    }

    // TODO 高卫东 能源数仓 实例树懒加载
}

