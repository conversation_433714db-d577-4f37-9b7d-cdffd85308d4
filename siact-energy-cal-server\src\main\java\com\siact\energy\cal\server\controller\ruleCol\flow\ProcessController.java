package com.siact.energy.cal.server.controller.ruleCol.flow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ComponentFlowDto;
import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.siact.energy.cal.common.pojo.enums.DataBaseTypeEnum;
import com.siact.energy.cal.common.pojo.enums.IndicatorFuncEnum;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import com.siact.energy.cal.common.pojo.vo.flow.ComponentFlowVO;
import com.siact.energy.cal.common.pojo.vo.flow.FlowRunResultVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import com.siact.energy.cal.server.common.flow.ELParser;
import com.siact.energy.cal.server.common.flow.logicflow.ELNode;
import com.siact.energy.cal.server.common.flow.logicflow.LogicFlow;
import com.siact.energy.cal.server.common.flow.logicflow.LogicFlowParser;
import com.siact.energy.cal.server.entity.flow.ComponentFlowEntity;
import com.siact.energy.cal.server.entity.flow.SiComponentEntity;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import com.siact.energy.cal.server.service.flow.SiComponentService;
import com.siact.energy.cal.server.service.flow.impl.ProcessServiceImpl;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 执行对象
 * @Version: 1.0
 */
@Api(tags = "工作流管理")
@RestController
@ApiSort(60)
@RequestMapping("/process")
@Slf4j
public class ProcessController {

    @Resource
    ProcessServiceImpl processService;

    @Resource
    DataSourceService dataSourceService;

    @Resource
    RuleDetailService ruleDetailService;

    @Resource
    SiComponentService siComponentService;

    @ApiOperation(value = "查询所有的组件")
    @ApiOperationSupport(order = 5)
    @GetMapping("/component/list")
    public R<List<SiComponentEntity>> componentList() {
        return R.OK(siComponentService.list());
    }


    @ApiOperation(value = "保存组件流", notes = "保存组件流")
    @PostMapping("/save")
    @ApiOperationSupport(order = 10)
    public R<Integer> save(@RequestBody @Validated LogicFlow logicFlow) {
        try {
            return R.OK(processService.saveComponent(logicFlow));
        }catch (Exception e){
            log.error("保存组件流失败,{}",e.getMessage(), e);
            return R.ERROR(e.getMessage());
        }
    }

    @ApiOperation(value = "校验组件流名称是否存在", notes = "校验组件流名称是否存在")
    @PostMapping("/check")
    @ApiOperationSupport(order = 15)
    public R<Boolean> check(@RequestBody ComponentFlowDto componentFlowDto) {
        return R.OK(processService.check(componentFlowDto.getFlowName()));
    }

    @ApiOperation(value = "执行组件流", notes = "执行组件流")
    @ApiOperationSupport(order = 20)
    @PostMapping("/run/{id}")
    public R<FlowRunResultVO> run(@PathVariable String id, @RequestBody List<ParamDto> paramList) {
        FlowRunResultVO flowRunResultVO = null;
        try {
            flowRunResultVO = processService.runFlow(id, paramList);
        } catch (Exception e) {
           log.error("执行组件流失败,{}",e.getMessage(), e);
           return R.ERROR(e.getMessage());
        }
        return R.OK(flowRunResultVO);
    }
    @ApiOperation(value = "查询组件流", notes = "执行组件流")
    @ApiOperationSupport(order = 30)
    @PostMapping("/getFlow/{id}")
    public R<String> getFlow(@PathVariable String id) {
        String flowContent = processService.getFlowContent(id);
        return R.OK(flowContent);
    }

    @ApiOperation(value = "删除组件流", notes = "删除组件流")
    @ApiOperationSupport(order = 40)
    @DeleteMapping("/delete")
    public R<Boolean> deleteFlow(@RequestBody List<Long> ids) {
        return R.OK(processService.deleteFlow(ids));
    }

    @ApiOperation(value = "查询组件流列表", notes = "查询组件流列表")
    @GetMapping("/list")
    @ApiOperationSupport(order = 50, ignoreParameters = {"total", "pages", "records", "orders", "id"})
    public R<PageBean<ComponentFlowVO>> list(PageBean<ComponentFlowVO> page, ComponentFlowDto componentFlowDto) {
        Page<ComponentFlowEntity> pageQu = new Page<>(page.getCurrent(),page.getSize());
        return R.OK(processService.list(pageQu, componentFlowDto));
    }

    @ApiOperation(value = "复制组件流", notes = "复制组件流")
    @ApiOperationSupport(order = 60)
    @PostMapping("/copy")
    public R<Integer> copy(@RequestBody @Validated ComponentFlowDto componentFlowDto) {
        return R.OK(processService.copy(componentFlowDto));

    }

    @ApiOperation(value = "转化el表达式", notes = "输入ElNode结构")
    @ApiImplicitParam(name = "elNode", value = "节点", required = true)
    @ApiOperationSupport(order = 70)
    @PostMapping("/generateEL")
    public String generateEL(@RequestBody ELNode elNode) {
        String sqlTemplate = null;
        try {
            sqlTemplate = elNode.generateEl();
        } catch (BizException e) {
            log.error("转化el表达式,{}",e.getMessage(), e);
        }
        return sqlTemplate;
    }

    @ApiOperation(value = "logicFlow转化el表达式", notes = "输入logicFlow 结构")
    @ApiImplicitParam(name = "logicFlow", value = "节点", required = true)
    @ApiOperationSupport(order = 80)
    @PostMapping("/generateLogicFlowEL")
    public String generateLogicFlowEL(@RequestBody LogicFlow logicFlow) {

        String sqlTemplate = null;
        try {
            ELParser elParser = new LogicFlowParser(logicFlow);
            ELNode elNode = elParser.extractElNode();
            sqlTemplate = elNode.generateEl();
        } catch (BizException e) {
            log.error("logicFlow转化el表达式失败,{}",e.getMessage(), e);
        }
        return sqlTemplate;
    }


    /**
     * 查询所有的数据源
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询所有的数据源")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 90)
    @GetMapping("/data/list")
    public R<List<DataSourceVO>> list() {
        return R.OK(dataSourceService.listAll());
    }

    /**
     * 查询所有的公式
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询所有的规则")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "计算类型（1-基础指标，2-聚合指标）", dataType = "String", required = true)
    })
    @ApiOperationSupport(order = 100)
    @GetMapping("/rule/list/{type}")
    public R<List<RuleDetailVO>> ruleList(@PathVariable String type) {
        return R.OK(ruleDetailService.ruleList(type));
    }


    /**
     * 获取所有的mysql支持的聚合
     *
     * @return 查询结果
     */
    @ApiOperation(value = "获取所有的mysql支持的聚合方式")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 91)
    @GetMapping("/aggType/list")
    public R<List<SelectOptionVO>> listAggFunc() {
        return R.OK(IndicatorFuncEnum.list());
    }

    /**
     * 获取所有支持的输出数据库类型
     *
     * @return 查询结果
     */
    @ApiOperation(value = "获取所有支持的输出数据库类型")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 95)
    @GetMapping("/databaseType/list")
    public R<List<SelectOptionVO>> listDatabaseType() {
        return R.OK(DataBaseTypeEnum.list());
    }
}
