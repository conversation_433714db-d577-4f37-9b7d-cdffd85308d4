package com.siact.energy.cal.server.service.energycal;/**
 * @Package com.siact.energy.cal.server.service.energycal
 * @description: 数据库服务
 * <AUTHOR>
 * @create 2024/12/5 16:53
 */

import com.google.common.collect.Lists;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import com.siact.energy.cal.server.common.datasource.db.factory.TimeSeriesDbFactory;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.QueryPlanner;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.ruleDetail.impl.RuleDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName DataBaseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 16:53
 * @Version 1.0
 **/
@Service
@Slf4j
public class DataBaseService {

    // 使用正则表达式匹配计算函数，例如sum(@[A])
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(@\\[(\\w+)\\]\\)");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    // 动态分批配置
    private static final int DEFAULT_BATCH_SIZE = 50; // 降低默认分批大小
    private static final int MAX_UNION_COUNT = 20; // 每个SQL批次的最大查询数量
    private static final int QUERY_TIMEOUT_SECONDS = 60; // 增加超时时间
    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;
    @Autowired
    private RedisUtil redisUtil;
    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    @Autowired
    private RuleDetailServiceImpl ruleDetailService;
    @Autowired
    private TimeSeriesDbFactory timeSeriesDbFactory;
    @Autowired
    private TimeWindowGenerator timeWindowGenerator;
    @Autowired
    private QueryPlanner queryPlanner;

    /**
     * 查询等间隔时序数据（入口方法）
     * 优化版：减少与QueryPlanner的重复分片
     */
    public void queryDataByEquallySpacedTime(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO,
            List<String> allDataCodes) {

        try {
            // 检查是否来自智能分片（通过指标数量判断）
            boolean fromIntelligentSharding = allDataCodes.size() <= 100; // 智能分片通常不会超过100个指标

            if (fromIntelligentSharding) {
                // 来自智能分片，直接执行，避免重复分片
                log.debug("检测到智能分片子任务，直接执行查询，指标数量: {}", allDataCodes.size());
                queryDTO.setDataCodes(allDataCodes);
                singleQuery(resultMap, dataSourceVo, queryDTO);
            } else {
                // 传统调用路径，使用动态分批逻辑
                log.debug("传统查询路径，指标数量: {}", allDataCodes.size());
                int dynamicBatchSize = calculateDynamicBatchSize(queryDTO, allDataCodes.size());
                if (allDataCodes.size() > dynamicBatchSize) {
                    batchQueryOptimized(resultMap, dataSourceVo, queryDTO, allDataCodes);
                } else {
                    queryDTO.setDataCodes(allDataCodes);
                    singleQuery(resultMap, dataSourceVo, queryDTO);
                }
            }
        } catch (Exception e) {
            log.error("Error querying time series data: {}", e.getMessage(), e);
            throw new BizException("查询时序数据异常", e);
        }
    }

    /**
     * 动态计算分批大小 - 针对TDengine优化
     */
    private int calculateDynamicBatchSize(TimeQueryDTO queryDTO, int totalMetrics) {
        // 计算时间跨度（天数）
        long timeSpanDays = calculateTimeSpanDays(queryDTO.getStartTime(), queryDTO.getEndTime());

        // 🔥 针对TDengine的分批策略：更保守的分批大小
        int baseBatchSize;

        // 根据时间跨度和查询类型综合判断
        if (queryDTO.isEquallySpacedQuery()) {
            // 等间隔查询：数据量大，需要更小的分批
            if (timeSpanDays > 30) {
                baseBatchSize = 10; // 超长时间跨度，极小分批
            } else if (timeSpanDays > 7) {
                baseBatchSize = 15; // 长时间跨度，小分批
            } else if (timeSpanDays > 1) {
                baseBatchSize = 25; // 中等时间跨度，中等分批
            } else {
                baseBatchSize = 40; // 短时间跨度，可以稍大
            }
        } else {
            // 区间查询：数据量相对较小
            if (timeSpanDays > 30) {
                baseBatchSize = 20;
            } else if (timeSpanDays > 7) {
                baseBatchSize = 30;
            } else {
                baseBatchSize = 50;
            }
        }

        // 🔥 根据总指标数量进一步调整
        if (totalMetrics > 1000) {
            baseBatchSize = Math.max(baseBatchSize / 2, 5); // 超大指标集，减半分批
        } else if (totalMetrics > 500) {
            baseBatchSize = Math.max(baseBatchSize * 2 / 3, 8); // 大指标集，减少1/3
        }

        // 确保分批大小在TDengine安全范围内
        return Math.max(5, Math.min(baseBatchSize, 50)); // 最大50个指标/批
    }

    /**
     * 计算时间跨度（天数）
     */
    private long calculateTimeSpanDays(String startTime, String endTime) {
        try {
            LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return ChronoUnit.DAYS.between(start, end);
        } catch (Exception e) {
            log.warn("计算时间跨度失败: {} -> {}", startTime, endTime, e);
            return 1; // 默认1天
        }
    }

    /**
     * 【重构版】检查并返回数据点数不足的指标列表。
     */
    public List<String> findIncompleteIndicators(
            Set<String> sourceProps,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        if (sourceProps.isEmpty() || CollectionUtils.isEmpty(timeWindows)) {
            return Collections.emptyList();
        }

        // 1. 🔥 调用 QueryPlanner 来高效地获取所有指标的总点数
        Map<String, Long> actualCounts = queryPlanner.executeCountPlan(
                new ArrayList<>(sourceProps), queryDTO, dataSourceVo
        );

        // 2. 在内存中进行比较
        long expectedCount = timeWindows.size();
        List<String> incompleteIndicators = new ArrayList<>();
        for (String prop : sourceProps) {
            long actualCount = actualCounts.getOrDefault(prop, 0L);
            if (actualCount < expectedCount) {
                log.warn("数据完整性检查：指标 [{}] 期望点数 {}, 实际点数 {}，需要补算。", prop, expectedCount, actualCount);
                incompleteIndicators.add(prop);
            }
        }
        return incompleteIndicators;
    }

    /**
     * 【新增】查询单个分片的数据点数，供 QueryPlanner 调用。
     */
    public Map<String, Long> queryCountsForShard(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);

        // a. 构建用于统计点数的SQL
        String sql = sqlBuilder.buildCountByIntervalSql(new HashSet<>(queryDTO.getDataCodes()), queryDTO, dataSourceVo.getTableName());

        // b. 执行查询并获取该分片内每个指标的实际点数
        return dbOperator.executeCountQuery(sql, dataSourceVo);
    }
    /**
     * 分批查询处理
     */
    private void batchQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO,
            List<String> allDataCodes) {
        // 分批调用并发调用
        List<CompletableFuture<Void>> futureList = Lists.newArrayList();
        // 将设备列表分批，使用动态分批大小
        int batchSize = calculateDynamicBatchSize(queryDTO, allDataCodes.size());
        List<List<String>> batches = Lists.partition(allDataCodes, batchSize);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始批量查询基础指标");
        // 创建所有批次的查询任务
        batches.forEach(batch -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                TimeQueryDTO batchQueryDTO = copyQueryDTO(queryDTO);
                batchQueryDTO.setDataCodes(batch);
                singleQuery(resultMap, dataSourceVo, batchQueryDTO);
            }, executor);
            futureList.add(future);
        });

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        log.info("批量查询基础指标耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 单批次查询处理
     */
    /**
     * 单批次查询处理
     * 保持原有接口，内部调用增强版本
     */
    private void singleQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO) {

        // 如果需要详细监控，调用增强版本；否则保持原有逻辑
        boolean enableDetailedMonitoring = log.isDebugEnabled();

        if (enableDetailedMonitoring) {
            singleQueryEnhanced(resultMap, dataSourceVo, queryDTO);
        } else {
            // 原有逻辑，保持性能
            Integer dbType = dataSourceVo.getDbType();
            AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
            String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);
            AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
            dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);
        }
    }

    private TimeQueryDTO copyQueryDTO(TimeQueryDTO original) {
        TimeQueryDTO copy = new TimeQueryDTO();
        BeanUtils.copyProperties(original, copy);
        return copy;
    }

    public void processDiffQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        //先组装sql
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        //这里查询的应该是聚合指标的源指标，也就是proMapping中的values
        queryDTO.setDataCodes(propMapping.keySet().stream().collect(Collectors.toList()));
        String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);
        //查询sql
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeDiffQuery(resultMap, sql, dataSourceVo, propMapping);
    }

    public void processAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String functionName, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {

        // 🔥 检查是否为TDengine且需要优化
        if (shouldOptimizeForTDengine(dataSourceVo, propMapping, queryDTO)) {
            processOptimizedAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        } else {
            // 使用原有逻辑
            processStandardAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        }
    }

    /**
     * 🔥 优化版聚合查询（单指标查询避免复杂聚合超时）
     */
    private void processOptimizedAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                        String functionName,
                                        Map<String, String> propMapping,
                                        TimeQueryDTO queryDTO,
                                        DataSourceVo dataSourceVo) {

        log.info("启用单指标聚合查询优化: 函数={}, 指标数={}", functionName, propMapping.size());

        try {
            // 1. 将多指标查询拆分为单指标查询
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            // 2. 为每个指标创建单独的查询任务
            for (Map.Entry<String, String> entry : propMapping.entrySet()) {
                String indicator = entry.getKey();
                String property = entry.getValue();

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 构建单指标查询的参数映射
                        Map<String, String> singlePropMapping = new HashMap<>();
                        singlePropMapping.put(indicator, property);

                        // 执行单指标聚合查询
                        log.debug("执行单指标聚合查询: 函数={}, 指标={}", functionName, indicator);
                        processStandardAggQuery(resultMap, functionName, singlePropMapping, queryDTO, dataSourceVo);

                    } catch (Exception e) {
                        log.error("单指标聚合查询失败: 函数={}, 指标={}", functionName, indicator, e);
                    }
                }, executor);

                futures.add(future);

                // 控制并发度，避免过多并发查询
                if (futures.size() >= 5) { // 最多5个并发查询
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                    futures.clear();
                }
            }

            // 等待剩余查询完成
            if (!futures.isEmpty()) {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }

            log.info("单指标聚合查询优化完成: 函数={}, 指标数={}", functionName, propMapping.size());

        } catch (Exception e) {
            log.error("单指标聚合查询优化失败，降级为标准查询: 函数={}", functionName, e);
            // 降级为标准查询
            processStandardAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        }
    }

    /**
     * 标准聚合查询（原有逻辑）
     */
    private void processStandardAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                       String functionName,
                                       Map<String, String> propMapping,
                                       TimeQueryDTO queryDTO,
                                       DataSourceVo dataSourceVo) {

        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = "";
        if(queryDTO.isEquallySpacedQuery()){
            sql = sqlBuilder.buildIntervalAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        }else{
            sql = sqlBuilder.buildTimeRangeAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        }
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeAggQuery(resultMap, sql, dataSourceVo, propMapping);
    }

    /**
     * 判断是否需要TDengine优化
     */
    private boolean shouldOptimizeForTDengine(DataSourceVo dataSourceVo, Map<String, String> propMapping, TimeQueryDTO queryDTO) {
        // 1. 检查是否为TDengine
        if (dataSourceVo.getDbType() == null || dataSourceVo.getDbType() != 3) { // 假设3是TDengine的类型
            return false;
        }

        // 2. 聚合查询且多指标时启用优化（避免复杂聚合查询超时）
        return propMapping.size() > 1;  // 多于1个指标就启用单指标查询优化
    }



 /*   public void processRangeAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String functionName, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildTimeRangeAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeAggQuery(resultMap, sql, dataSourceVo, propMapping);

    }*/

    /*
     * <AUTHOR>
     * @Description //时间切面查询
     * @Date 17:03 2024/12/9
     * @Param
     * @return
     **/
    public void queryTimeSliceData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, DataSourceVo dataSourceVo, TimeQueryDTO queryDTO, List<String> dataCodes) {

        String ts = queryDTO.getStartTime();
        String stime = DateUtils.longToStr(DateUtils.parseDate(ts).getTime() - 1000 * 60 * 15);
        String etime = ts;
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildTimeSliceDataSql(stime, etime, dataCodes, dataSourceVo.getTableName());
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);
    }


    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> dataCodes, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.insertData(resultMap, dataCodes, dataSourceVo);
    }
    /**
     * 优化的分批查询处理
     * 相比原有batchQuery方法，增加了更好的错误处理和监控
     */
    private void batchQueryOptimized(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO,
            List<String> allDataCodes) {

        List<CompletableFuture<Void>> futureList = Lists.newArrayList();
        int batchSize = calculateDynamicBatchSize(queryDTO, allDataCodes.size());
        List<List<String>> batches = Lists.partition(allDataCodes, batchSize);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始优化批量查询基础指标");

        log.info("开始优化分批查询，总指标数: {}, 分批数: {}, 每批大小: {}",
                allDataCodes.size(), batches.size(), batchSize);

        // 创建所有批次的查询任务
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            final List<String> batch = batches.get(i);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                long batchStartTime = System.currentTimeMillis();
                try {
                    log.debug("执行批次 {}/{}, 指标数: {}", batchIndex + 1, batches.size(), batch.size());

                    TimeQueryDTO batchQueryDTO = copyQueryDTO(queryDTO);
                    batchQueryDTO.setDataCodes(batch);
                    singleQuery(resultMap, dataSourceVo, batchQueryDTO);

                    long batchDuration = System.currentTimeMillis() - batchStartTime;
                    log.debug("批次 {}/{} 执行完成，耗时: {}ms", batchIndex + 1, batches.size(), batchDuration);

                } catch (Exception e) {
                    log.error("批次 {}/{} 执行失败，指标数: {}", batchIndex + 1, batches.size(), batch.size(), e);
                    // 不抛出异常，让其他批次继续执行
                }
            }, executor);

            futureList.add(future);
        }

        // 等待所有批次完成
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        } catch (Exception e) {
            log.warn("部分批次执行失败，但其他批次可能已成功", e);
        }

        stopWatch.stop();
        log.info("优化分批查询完成，总耗时: {}ms, 成功批次: {}/{}",
                stopWatch.getTotalTimeMillis(),
                futureList.stream().mapToInt(f -> f.isCompletedExceptionally() ? 0 : 1).sum(),
                batches.size());
    }

    /**
     * 增强的单批次查询处理
     * 添加了更详细的监控和错误处理
     */
    private void singleQueryEnhanced(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO) {

        long startTime = System.currentTimeMillis();
        String timeRange = queryDTO.getStartTime() + " -> " + queryDTO.getEndTime();
        int metricCount = queryDTO.getDataCodes() != null ? queryDTO.getDataCodes().size() : 0;

        try {
            log.debug("开始单次查询: 时间范围[{}], 指标数[{}]", timeRange, metricCount);

            Integer dbType = dataSourceVo.getDbType();
            AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
            String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);

            log.debug("生成SQL: {}", sql.length() > 200 ? sql.substring(0, 200) + "..." : sql);

            AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
            dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);

            long duration = System.currentTimeMillis() - startTime;
            log.debug("单次查询完成: 时间范围[{}], 指标数[{}], 耗时[{}ms]", timeRange, metricCount, duration);

            // 记录慢查询
            if (duration > 5000) {
                log.warn("检测到慢查询: 时间范围[{}], 指标数[{}], 耗时[{}ms]", timeRange, metricCount, duration);
            }

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("单次查询失败: 时间范围[{}], 指标数[{}], 耗时[{}ms]", timeRange, metricCount, duration, e);
            throw e;
        }
    }
}
