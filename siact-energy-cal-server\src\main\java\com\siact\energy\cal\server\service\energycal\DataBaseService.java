package com.siact.energy.cal.server.service.energycal;/**
 * @Package com.siact.energy.cal.server.service.energycal
 * @description: 数据库服务
 * <AUTHOR>
 * @create 2024/12/5 16:53
 */

import com.google.common.collect.Lists;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import com.siact.energy.cal.server.common.datasource.db.factory.TimeSeriesDbFactory;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.QueryPlanner;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.ruleDetail.impl.RuleDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName DataBaseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 16:53
 * @Version 1.0
 **/
@Service
@Slf4j
public class DataBaseService {


    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;
    @Autowired
    private RedisUtil redisUtil;
    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    @Autowired
    private RuleDetailServiceImpl ruleDetailService;
    @Autowired
    private TimeSeriesDbFactory timeSeriesDbFactory;
    @Autowired
    private TimeWindowGenerator timeWindowGenerator;
    @Autowired
    private QueryPlanner queryPlanner;

    /**
     * 查询等间隔时序数据（简化版）
     * 专注于执行具体的数据库查询操作，分片逻辑由QueryPlanner负责
     */
    public void queryDataByEquallySpacedTime(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO,
            List<String> allDataCodes) {

        try {
            log.debug("执行数据库查询，指标数量: {}", allDataCodes.size());
            queryDTO.setDataCodes(allDataCodes);
            singleQuery(resultMap, dataSourceVo, queryDTO);
        } catch (Exception e) {
            log.error("Error querying time series data: {}", e.getMessage(), e);
            throw new BizException("查询时序数据异常", e);
        }
    }



    /**
     * 【重构版】检查并返回数据点数不足的指标列表。
     */
    public List<String> findIncompleteIndicators(
            Set<String> sourceProps,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        if (sourceProps.isEmpty() || CollectionUtils.isEmpty(timeWindows)) {
            return Collections.emptyList();
        }

        // 1. 🔥 调用 QueryPlanner 来高效地获取所有指标的总点数
        Map<String, Long> actualCounts = queryPlanner.executeCountPlan(
                new ArrayList<>(sourceProps), queryDTO, dataSourceVo
        );

        // 2. 在内存中进行比较
        long expectedCount = timeWindows.size();
        List<String> incompleteIndicators = new ArrayList<>();
        for (String prop : sourceProps) {
            long actualCount = actualCounts.getOrDefault(prop, 0L);
            if (actualCount < expectedCount) {
                log.warn("数据完整性检查：指标 [{}] 期望点数 {}, 实际点数 {}，需要补算。", prop, expectedCount, actualCount);
                incompleteIndicators.add(prop);
            }
        }
        return incompleteIndicators;
    }

    /**
     * 【新增】查询单个分片的数据点数，供 QueryPlanner 调用。
     */
    public Map<String, Long> queryCountsForShard(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);

        // a. 构建用于统计点数的SQL
        String sql = sqlBuilder.buildCountByIntervalSql(new HashSet<>(queryDTO.getDataCodes()), queryDTO, dataSourceVo.getTableName());

        // b. 执行查询并获取该分片内每个指标的实际点数
        return dbOperator.executeCountQuery(sql, dataSourceVo);
    }


    /**
     * 单批次查询处理 - 简化版
     */
    private void singleQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO) {

        try {
            log.debug("执行单次查询: 指标数[{}]", queryDTO.getDataCodes().size());

            Integer dbType = dataSourceVo.getDbType();
            AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
            String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);
            AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
            dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);

        } catch (Exception e) {
            log.error("单次查询失败: 指标数[{}]", queryDTO.getDataCodes().size(), e);
            throw e;
        }
    }

    public void processDiffQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        //先组装sql
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        //这里查询的应该是聚合指标的源指标，也就是proMapping中的values
        queryDTO.setDataCodes(propMapping.keySet().stream().collect(Collectors.toList()));
        String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);
        //查询sql
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeDiffQuery(resultMap, sql, dataSourceVo, propMapping);
    }

    public void processAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String functionName, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {

        // 🔥 检查是否为TDengine且需要优化
        if (shouldOptimizeForTDengine(dataSourceVo, propMapping, queryDTO)) {
            processOptimizedAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        } else {
            // 使用原有逻辑
            processStandardAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        }
    }

    /**
     * 🔥 优化版聚合查询（单指标查询避免复杂聚合超时）
     */
    private void processOptimizedAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                        String functionName,
                                        Map<String, String> propMapping,
                                        TimeQueryDTO queryDTO,
                                        DataSourceVo dataSourceVo) {

        log.info("启用单指标聚合查询优化: 函数={}, 指标数={}", functionName, propMapping.size());

        try {
            // 1. 将多指标查询拆分为单指标查询
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            // 2. 为每个指标创建单独的查询任务
            for (Map.Entry<String, String> entry : propMapping.entrySet()) {
                String indicator = entry.getKey();
                String property = entry.getValue();

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 构建单指标查询的参数映射
                        Map<String, String> singlePropMapping = new HashMap<>();
                        singlePropMapping.put(indicator, property);

                        // 执行单指标聚合查询
                        log.debug("执行单指标聚合查询: 函数={}, 指标={}", functionName, indicator);
                        processStandardAggQuery(resultMap, functionName, singlePropMapping, queryDTO, dataSourceVo);

                    } catch (Exception e) {
                        log.error("单指标聚合查询失败: 函数={}, 指标={}", functionName, indicator, e);
                    }
                }, executor);

                futures.add(future);

                // 控制并发度，避免过多并发查询
                if (futures.size() >= 5) { // 最多5个并发查询
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                    futures.clear();
                }
            }

            // 等待剩余查询完成
            if (!futures.isEmpty()) {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }

            log.info("单指标聚合查询优化完成: 函数={}, 指标数={}", functionName, propMapping.size());

        } catch (Exception e) {
            log.error("单指标聚合查询优化失败，降级为标准查询: 函数={}", functionName, e);
            // 降级为标准查询
            processStandardAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        }
    }

    /**
     * 标准聚合查询（原有逻辑）
     */
    private void processStandardAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                       String functionName,
                                       Map<String, String> propMapping,
                                       TimeQueryDTO queryDTO,
                                       DataSourceVo dataSourceVo) {

        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = "";
        if(queryDTO.isEquallySpacedQuery()){
            sql = sqlBuilder.buildIntervalAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        }else{
            sql = sqlBuilder.buildTimeRangeAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        }
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeAggQuery(resultMap, sql, dataSourceVo, propMapping);
    }

    /**
     * 判断是否需要TDengine优化
     */
    private boolean shouldOptimizeForTDengine(DataSourceVo dataSourceVo, Map<String, String> propMapping, TimeQueryDTO queryDTO) {
        // 1. 检查是否为TDengine
        if (dataSourceVo.getDbType() == null || dataSourceVo.getDbType() != 3) { // 假设3是TDengine的类型
            return false;
        }

        // 2. 聚合查询且多指标时启用优化（避免复杂聚合查询超时）
        return propMapping.size() > 1;  // 多于1个指标就启用单指标查询优化
    }



 /*   public void processRangeAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String functionName, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildTimeRangeAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeAggQuery(resultMap, sql, dataSourceVo, propMapping);

    }*/

    /*
     * <AUTHOR>
     * @Description //时间切面查询
     * @Date 17:03 2024/12/9
     * @Param
     * @return
     **/
    public void queryTimeSliceData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, DataSourceVo dataSourceVo, TimeQueryDTO queryDTO, List<String> dataCodes) {

        String ts = queryDTO.getStartTime();
        String stime = DateUtils.longToStr(DateUtils.parseDate(ts).getTime() - 1000 * 60 * 15);
        String etime = ts;
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildTimeSliceDataSql(stime, etime, dataCodes, dataSourceVo.getTableName());
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);
    }


    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> dataCodes, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.insertData(resultMap, dataCodes, dataSourceVo);
    }

}
