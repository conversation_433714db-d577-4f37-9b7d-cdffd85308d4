package com.siact.energy.cal.server.xxljob.controller.interceptor;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-01
 * @Description: 权限拦截器
 * @Version: 1.0
 */
@Component
public class CookieInterceptor implements AsyncHandlerInterceptor {

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
//
//		// cookie
//		if (modelAndView!=null && request.getCookies()!=null && request.getCookies().length>0) {
//			HashMap<String, Cookie> cookieMap = new HashMap<String, Cookie>();
//			for (Cookie ck : request.getCookies()) {
//				cookieMap.put(ck.getName(), ck);
//			}
//			modelAndView.addObject("cookieMap", cookieMap);
//		}
//
//		// static method
//		if (modelAndView != null) {
//		//	modelAndView.addObject("I18nUtil", FtlUtil.generateStaticModel(I18nUtil.class.getName()));
//		}

	}
	
}
