package com.siact.energy.cal.server.common.datasource.db.impl.influxdb;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("influxDBSqlBuilder")
@Slf4j
public class InfluxDBSqlBuilder extends AbstractSqlBuilder {

    /**
     * 构建基础查询SQL (Flux版本)
     */
    @Override
    public String buildBasicQuerySql(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        List<String> devpropertyList = queryDTO.getDataCodes();
        String devpropFilter = devpropertyList.stream()
                .map(value -> String.format("r[\"devproperty\"] == \"%s\"", value))
                .collect(Collectors.joining(" or "));

        StringBuilder flux = new StringBuilder();
        
        if (!queryDTO.isEquallySpacedQuery()) {
            // 非等间隔查询：获取时间范围内的第一个和最后一个值
            flux.append(String.format(
                "from(bucket: \"%s\")\n" +
                "  |> range(start: %s, stop: %s)\n" +
                "  |> filter(fn: (r) => %s)\n" +
                "  |> first()\n" +
                "  |> group(columns: [\"devproperty\"])\n" +
                "  |> yield(name: \"first\")\n\n" +
                "from(bucket: \"%s\")\n" +
                "  |> range(start: %s, stop: %s)\n" +
                "  |> filter(fn: (r) => %s)\n" +
                "  |> last()\n" +
                "  |> group(columns: [\"devproperty\"])\n" +
                "  |> yield(name: \"last\")",
                dataSourceVo.getTableName(),
                formatTime(queryDTO.getStartTime()),
                formatTime(queryDTO.getEndTime()),
                devpropFilter,
                dataSourceVo.getTableName(),
                formatTime(queryDTO.getStartTime()),
                formatTime(queryDTO.getEndTime()),
                devpropFilter
            ));
        } else {
            // 等间隔查询
            String windowPeriod = queryDTO.getInterval() + convertTsUnit(queryDTO.getTsUnit());
            flux.append(String.format(
                "from(bucket: \"%s\")\n" +
                "  |> range(start: %s, stop: %s)\n" +
                "  |> filter(fn: (r) => %s)\n" +
                "  |> aggregateWindow(\n" +
                "      every: %s,\n" +
                "      fn: first,\n" +
                "      createEmpty: false\n" +
                "    )\n" +
                "  |> group(columns: [\"devproperty\"])",
                dataSourceVo.getTableName(),
                formatTime(queryDTO.getStartTime()),
                formatTime(queryDTO.getEndTime()),
                devpropFilter,
                windowPeriod
            ));
        }
        
        return flux.toString();
    }

    /**
     * 构建时间范围聚合查询 (Flux版本)
     */
    @Override
    public String buildTimeRangeAggSql(String function, Map<String, String> propMapping,
            TimeQueryDTO queryDTO, String tableName) {
        String devpropFilter = propMapping.keySet().stream()
                .map(value -> String.format("r[\"devproperty\"] == \"%s\"", value))
                .collect(Collectors.joining(" or "));

        // 验证并转换聚合函数
        String fluxFunction = convertAggregateFunction(function);

        return String.format(
            "from(bucket: \"%s\")\n" +
            "  |> range(start: %s, stop: %s)\n" +
            "  |> filter(fn: (r) => %s)\n" +
            "  |> %s()\n" +
            "  |> group(columns: [\"devproperty\"])",
            tableName,
            formatTime(queryDTO.getStartTime()),
            formatTime(queryDTO.getEndTime()),
            devpropFilter,
            fluxFunction
        );
    }

    @Override
    public String buildCountByIntervalSql(Set<String> sourceProps, TimeQueryDTO queryDTO, String tableName) {
        return null;
    }

    /**
     * 构建时间间隔聚合查询 (Flux版本)
     */
    @Override
    public String buildIntervalAggSql(String function, Map<String, String> propMapping, 
            TimeQueryDTO queryDTO, String tableName) {
        String devpropFilter = propMapping.keySet().stream()
                .map(value -> String.format("r[\"devproperty\"] == \"%s\"", value))
                .collect(Collectors.joining(" or "));

        String windowPeriod = queryDTO.getInterval() + convertTsUnit(queryDTO.getTsUnit());
        String fluxFunction = convertAggregateFunction(function);

        return String.format(
            "from(bucket: \"%s\")\n" +
            "  |> range(start: %s, stop: %s)\n" +
            "  |> filter(fn: (r) => %s)\n" +
            "  |> aggregateWindow(\n" +
            "      every: %s,\n" +
            "      fn: %s,\n" +
            "      createEmpty: false\n" +
            "    )\n" +
            "  |> group(columns: [\"devproperty\"])",
            tableName,
            formatTime(queryDTO.getStartTime()),
            formatTime(queryDTO.getEndTime()),
            devpropFilter,
            windowPeriod,
            fluxFunction
        );
    }

    /**
     * 构建时间切片数据查询 (Flux版本)
     */
    @Override
    public String buildTimeSliceDataSql(String startTime, String endTime, 
            List<String> dataCodes, String tableName) {
        String devpropFilter = dataCodes.stream()
                .map(value -> String.format("r[\"devproperty\"] == \"%s\"", value))
                .collect(Collectors.joining(" or "));

        return String.format(
            "from(bucket: \"%s\")\n" +
            "  |> range(start: %s, stop: %s)\n" +
            "  |> filter(fn: (r) => %s)\n" +
            "  |> last()\n" +
            "  |> group(columns: [\"devproperty\"])",
            tableName,
            formatTime(startTime),
            formatTime(endTime),
            devpropFilter
        );
    }

    /**
     * 构建差值查询 (Flux版本)
     */
    @Override
    public String buildDiffSql(Map<String, String> propMapping, String tableName, 
            String startTime, String endTime, String interval, String unit) {
        String devpropFilter = propMapping.keySet().stream()
                .map(value -> String.format("r[\"devproperty\"] == \"%s\"", value))
                .collect(Collectors.joining(" or "));

        String windowPeriod = interval + convertTsUnit(unit);

        return String.format(
            "from(bucket: \"%s\")\n" +
            "  |> range(start: %s, stop: %s)\n" +
            "  |> filter(fn: (r) => %s)\n" +
            "  |> aggregateWindow(\n" +
            "      every: %s,\n" +
            "      fn: last,\n" +
            "      createEmpty: false\n" +
            "    )\n" +
            "  |> difference()\n" +
            "  |> group(columns: [\"devproperty\"])",
            tableName,
            formatTime(startTime),
            formatTime(endTime),
            devpropFilter,
            windowPeriod
        );
    }

    /**
     * 转换时间单位
     */
    private String convertTsUnit(String tsUnit) {
        if (tsUnit == null) {
            return null;
        }
        
        Map<String, String> unitMap = new HashMap<>();
        unitMap.put("y", "y");
        unitMap.put("n", "mo");
        unitMap.put("d", "d");
        unitMap.put("h", "h");
        unitMap.put("m", "m");
        
        return unitMap.getOrDefault(tsUnit.toUpperCase(), tsUnit);
    }

    /**
     * 转换聚合函数
     */
    private String convertAggregateFunction(String function) {
        Set<String> supportedFunctions = new HashSet<>(Arrays.asList(
                "sum", "mean", "max", "min", "last", "first"));
        String fluxFunction = function.toLowerCase();
        // 特殊处理：avg 转换为 mean
        if ("avg".equals(fluxFunction)) {
            fluxFunction = "mean";
        }
        if (!supportedFunctions.contains(fluxFunction)) {
            throw new BizException("不支持的聚合函数: " + function);
        }
        return fluxFunction;
    }

    /**
     * 格式化时间字符串
     */
    private String formatTime(String timeStr) {
        // 假设输入的时间格式为 "yyyy-MM-dd HH:mm:ss"
        return String.format("time(v: \"%s\")", timeStr);
    }
}