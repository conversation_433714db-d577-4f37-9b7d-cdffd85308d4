package com.siact.energy.cal.server.core.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 🔥 衍生指标性能监控器
 * 
 * 功能：
 * 1. 实时监控计算性能
 * 2. 统计计算效率指标
 * 3. 提供性能优化建议
 * 4. 支持性能报告生成
 */
@Component
@Slf4j
public class DerivedIndicatorPerformanceMonitor {
    
    // 性能统计数据
    private final LongAdder totalCalculations = new LongAdder();
    private final LongAdder totalCalculationTime = new LongAdder();
    private final LongAdder highPerformanceModeUsage = new LongAdder();
    private final LongAdder traditionalModeUsage = new LongAdder();
    
    // 批次性能统计
    private final ConcurrentHashMap<String, BatchPerformanceStats> batchStats = new ConcurrentHashMap<>();
    
    // 指标复杂度统计
    private final ConcurrentHashMap<String, IndicatorComplexityStats> complexityStats = new ConcurrentHashMap<>();
    
    // 最近的性能数据（用于趋势分析）
    private final AtomicLong lastCalculationTime = new AtomicLong();
    private final AtomicLong bestPerformanceTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong worstPerformanceTime = new AtomicLong();
    
    /**
     * 记录计算开始
     */
    public CalculationSession startCalculation(String sessionId, int indicatorCount, boolean isHighPerformanceMode) {
        log.debug("开始监控计算会话: sessionId={}, 指标数={}, 高性能模式={}", 
                sessionId, indicatorCount, isHighPerformanceMode);
        
        if (isHighPerformanceMode) {
            highPerformanceModeUsage.increment();
        } else {
            traditionalModeUsage.increment();
        }
        
        return new CalculationSession(sessionId, indicatorCount, isHighPerformanceMode, System.currentTimeMillis());
    }
    
    /**
     * 记录计算完成
     */
    public void endCalculation(CalculationSession session) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - session.startTime;
        
        // 更新全局统计
        totalCalculations.increment();
        totalCalculationTime.add(duration);
        lastCalculationTime.set(duration);
        
        // 更新最佳/最差性能记录
        updatePerformanceRecords(duration);
        
        // 计算性能指标
        double indicatorsPerSecond = session.indicatorCount * 1000.0 / duration;
        
        log.info("计算会话完成: sessionId={}, 耗时={}ms, 指标数={}, 性能={:.2f}指标/秒, 模式={}", 
                session.sessionId, duration, session.indicatorCount, indicatorsPerSecond,
                session.isHighPerformanceMode ? "高性能" : "传统");
        
        // 性能分析和建议
        analyzePerformanceAndSuggest(session, duration, indicatorsPerSecond);
    }
    
    /**
     * 记录批次性能
     */
    public void recordBatchPerformance(String batchType, String complexity, int indicatorCount, long duration) {
        String key = batchType + "_" + complexity;
        
        batchStats.compute(key, (k, stats) -> {
            if (stats == null) {
                stats = new BatchPerformanceStats(batchType, complexity);
            }
            stats.addRecord(indicatorCount, duration);
            return stats;
        });
        
        log.debug("批次性能记录: 类型={}, 复杂度={}, 指标数={}, 耗时={}ms", 
                batchType, complexity, indicatorCount, duration);
    }
    
    /**
     * 记录指标复杂度统计
     */
    public void recordIndicatorComplexity(String indicator, String complexity, long calculationTime) {
        complexityStats.compute(indicator, (k, stats) -> {
            if (stats == null) {
                stats = new IndicatorComplexityStats(indicator, complexity);
            }
            stats.addCalculationTime(calculationTime);
            return stats;
        });
    }
    
    /**
     * 获取性能报告
     */
    public PerformanceReport generatePerformanceReport() {
        long totalCalcs = totalCalculations.sum();
        long totalTime = totalCalculationTime.sum();
        
        double avgCalculationTime = totalCalcs > 0 ? (double) totalTime / totalCalcs : 0;
        double highPerformanceRatio = totalCalcs > 0 ? 
                (double) highPerformanceModeUsage.sum() / totalCalcs * 100 : 0;
        
        PerformanceReport report = new PerformanceReport();
        report.totalCalculations = totalCalcs;
        report.totalCalculationTime = totalTime;
        report.averageCalculationTime = avgCalculationTime;
        report.highPerformanceModeUsageRatio = highPerformanceRatio;
        report.bestPerformanceTime = bestPerformanceTime.get() == Long.MAX_VALUE ? 0 : bestPerformanceTime.get();
        report.worstPerformanceTime = worstPerformanceTime.get();
        report.lastCalculationTime = lastCalculationTime.get();
        
        // 批次性能统计
        report.batchPerformanceStats = new ConcurrentHashMap<>(batchStats);
        
        // 复杂度统计
        report.complexityStats = new ConcurrentHashMap<>(complexityStats);
        
        return report;
    }
    
    /**
     * 获取性能优化建议
     */
    public String getPerformanceRecommendations() {
        StringBuilder recommendations = new StringBuilder();
        PerformanceReport report = generatePerformanceReport();
        
        // 1. 模式使用建议
        if (report.highPerformanceModeUsageRatio < 30) {
            recommendations.append("建议：高性能模式使用率较低(")
                    .append(String.format("%.1f", report.highPerformanceModeUsageRatio))
                    .append("%)，考虑在大批量计算时启用高性能模式。\n");
        }
        
        // 2. 性能趋势建议
        if (report.lastCalculationTime > report.averageCalculationTime * 1.5) {
            recommendations.append("警告：最近一次计算耗时(")
                    .append(report.lastCalculationTime)
                    .append("ms)明显高于平均水平(")
                    .append(String.format("%.1f", report.averageCalculationTime))
                    .append("ms)，建议检查系统负载。\n");
        }
        
        // 3. 批次性能建议
        batchStats.forEach((key, stats) -> {
            if (stats.getAverageTime() > 1000) { // 超过1秒
                recommendations.append("建议：批次类型 ")
                        .append(key)
                        .append(" 平均耗时较长(")
                        .append(String.format("%.1f", stats.getAverageTime()))
                        .append("ms)，考虑进一步优化或分片。\n");
            }
        });
        
        // 4. 系统资源建议
        long freeMemory = Runtime.getRuntime().freeMemory() / 1024 / 1024; // MB
        if (freeMemory < 200) {
            recommendations.append("警告：可用内存较低(")
                    .append(freeMemory)
                    .append("MB)，可能影响高性能模式效果。\n");
        }
        
        if (recommendations.length() == 0) {
            recommendations.append("系统性能良好，无特殊建议。");
        }
        
        return recommendations.toString();
    }
    
    /**
     * 重置统计数据
     */
    public void resetStatistics() {
        totalCalculations.reset();
        totalCalculationTime.reset();
        highPerformanceModeUsage.reset();
        traditionalModeUsage.reset();
        batchStats.clear();
        complexityStats.clear();
        bestPerformanceTime.set(Long.MAX_VALUE);
        worstPerformanceTime.set(0);
        lastCalculationTime.set(0);
        
        log.info("性能统计数据已重置");
    }
    
    // 私有方法
    private void updatePerformanceRecords(long duration) {
        // 更新最佳性能记录
        bestPerformanceTime.updateAndGet(current -> Math.min(current, duration));
        
        // 更新最差性能记录
        worstPerformanceTime.updateAndGet(current -> Math.max(current, duration));
    }
    
    private void analyzePerformanceAndSuggest(CalculationSession session, long duration, double indicatorsPerSecond) {
        // 性能阈值分析
        if (indicatorsPerSecond < 10) {
            log.warn("性能警告: 计算效率较低({:.2f}指标/秒)，建议检查公式复杂度或启用高性能模式", indicatorsPerSecond);
        } else if (indicatorsPerSecond > 100) {
            log.info("性能优秀: 计算效率很高({:.2f}指标/秒)", indicatorsPerSecond);
        }
        
        // 模式选择建议
        if (!session.isHighPerformanceMode && session.indicatorCount > 100) {
            log.info("建议: 指标数量较多({}个)，建议使用高性能模式", session.indicatorCount);
        }
    }
    
    // 内部类
    public static class CalculationSession {
        public final String sessionId;
        public final int indicatorCount;
        public final boolean isHighPerformanceMode;
        public final long startTime;
        
        public CalculationSession(String sessionId, int indicatorCount, boolean isHighPerformanceMode, long startTime) {
            this.sessionId = sessionId;
            this.indicatorCount = indicatorCount;
            this.isHighPerformanceMode = isHighPerformanceMode;
            this.startTime = startTime;
        }
    }
    
    public static class BatchPerformanceStats {
        private final String batchType;
        private final String complexity;
        private final LongAdder totalTime = new LongAdder();
        private final LongAdder totalIndicators = new LongAdder();
        private final LongAdder executionCount = new LongAdder();
        
        public BatchPerformanceStats(String batchType, String complexity) {
            this.batchType = batchType;
            this.complexity = complexity;
        }
        
        public void addRecord(int indicatorCount, long duration) {
            totalTime.add(duration);
            totalIndicators.add(indicatorCount);
            executionCount.increment();
        }
        
        public double getAverageTime() {
            long count = executionCount.sum();
            return count > 0 ? (double) totalTime.sum() / count : 0;
        }
        
        public double getAverageIndicatorsPerExecution() {
            long count = executionCount.sum();
            return count > 0 ? (double) totalIndicators.sum() / count : 0;
        }
        
        public String getBatchType() { return batchType; }
        public String getComplexity() { return complexity; }
        public long getExecutionCount() { return executionCount.sum(); }
    }
    
    public static class IndicatorComplexityStats {
        private final String indicator;
        private final String complexity;
        private final LongAdder totalCalculationTime = new LongAdder();
        private final LongAdder calculationCount = new LongAdder();
        
        public IndicatorComplexityStats(String indicator, String complexity) {
            this.indicator = indicator;
            this.complexity = complexity;
        }
        
        public void addCalculationTime(long time) {
            totalCalculationTime.add(time);
            calculationCount.increment();
        }
        
        public double getAverageCalculationTime() {
            long count = calculationCount.sum();
            return count > 0 ? (double) totalCalculationTime.sum() / count : 0;
        }
        
        public String getIndicator() { return indicator; }
        public String getComplexity() { return complexity; }
        public long getCalculationCount() { return calculationCount.sum(); }
    }
    
    public static class PerformanceReport {
        public long totalCalculations;
        public long totalCalculationTime;
        public double averageCalculationTime;
        public double highPerformanceModeUsageRatio;
        public long bestPerformanceTime;
        public long worstPerformanceTime;
        public long lastCalculationTime;
        public ConcurrentHashMap<String, BatchPerformanceStats> batchPerformanceStats;
        public ConcurrentHashMap<String, IndicatorComplexityStats> complexityStats;
        
        @Override
        public String toString() {
            return String.format(
                    "性能报告:\n" +
                    "- 总计算次数: %d\n" +
                    "- 总计算时间: %dms\n" +
                    "- 平均计算时间: %.2fms\n" +
                    "- 高性能模式使用率: %.1f%%\n" +
                    "- 最佳性能: %dms\n" +
                    "- 最差性能: %dms\n" +
                    "- 最近计算时间: %dms",
                    totalCalculations, totalCalculationTime, averageCalculationTime,
                    highPerformanceModeUsageRatio, bestPerformanceTime, worstPerformanceTime, lastCalculationTime
            );
        }
    }
}
