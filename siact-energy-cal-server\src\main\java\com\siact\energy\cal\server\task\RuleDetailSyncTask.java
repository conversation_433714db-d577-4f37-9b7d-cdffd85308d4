package com.siact.energy.cal.server.task;

import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableScheduling
public class RuleDetailSyncTask {

    @Autowired
    private RuleDetailService ruleDetailService;

    /**
     * 任务开关
     */
    @Value("${update-ruleDetail.task.enable:true}")
    private boolean taskEnable;

    /**
     * 定时同步规则公式到实例表
     * 每5分钟执行一次
     */
    @Scheduled(cron = "${update-ruleDetail.task.cron:0 0/5 * * * ?}")
    public void syncRuleDetailToInstance() {

        if(!taskEnable) {
            return;
        }
        log.info("开始执行规则公式同步任务...");
        long startTime = System.currentTimeMillis();

        try {
            // 调用服务执行同步
            ruleDetailService.syncAllFormulaToInstance();

            long costTime = System.currentTimeMillis() - startTime;
            log.info("规则公式同步任务执行完成，耗时: {}ms", costTime);
        } catch (Exception e) {
            log.error("规则公式同步任务执行失败: {}", e.getMessage(), e);
        }
    }
}