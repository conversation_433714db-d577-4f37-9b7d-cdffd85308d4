<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.dataTrans.DataTransDao">

    <resultMap type="com.siact.energy.cal.server.entity.dataTrans.DataTrans" id="DataTransMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="host" column="host" jdbcType="VARCHAR"/>
        <result property="port" column="port" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="topic" column="topic" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into data_trans(project_id, host, port, user_name, password, topic, creator, create_time, updater,
        update_time, deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectId}, #{entity.host}, #{entity.port}, #{entity.userName}, #{entity.password},
            #{entity.topic}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime},
            #{entity.deleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into data_trans(project_id, host, port, user_name, password, topic, creator, create_time, updater,
        update_time, deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectId}, #{entity.host}, #{entity.port}, #{entity.userName}, #{entity.password},
            #{entity.topic}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime},
            #{entity.deleted})
        </foreach>
        on duplicate key update
        project_id = values(project_id) , host = values(host) , port = values(port) , user_name = values(user_name) ,
        password = values(password) , topic = values(topic) , creator = values(creator) , create_time =
        values(create_time) , updater = values(updater) , update_time = values(update_time) , deleted = values(deleted)
    </insert>

</mapper>

