package com.siact.energy.cal.common.datasource.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.OrderBy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * BaseEntity
 *
 * <AUTHOR>
 * @since 2024-05-13 16:03:06
 */
@Data
public class BaseEntity {

    /**
     * 创建者id
     */
    @ApiModelProperty(value = "创建者id", position = 10000)
    private Long creator;

    /**
     * 创建时间
     */
    @OrderBy
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", position = 10001)
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者", position = 10002)
    private Long updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间", position = 10003)
    private Date updateTime;

    /**
     * 删除标志(0-正常，1-已删除)
     */
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    @ApiModelProperty(value = "删除标志(0-正常，1-已删除)", position = 10004)
    private Integer deleted;
}
