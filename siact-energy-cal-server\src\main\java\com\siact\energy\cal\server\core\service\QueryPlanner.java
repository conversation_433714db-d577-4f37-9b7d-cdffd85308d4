package com.siact.energy.cal.server.core.service;

import com.google.common.collect.Lists;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.config.CalculationProperties;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.core.model.QueryContext;
import com.siact.energy.cal.server.core.model.ShardingParams;
import com.siact.energy.cal.server.core.monitor.SystemMonitorService;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.strategy.IntelligentShardingStrategy;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.energycal.DataBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.siact.energy.cal.server.core.strategy.IntelligentShardingStrategy;
import com.siact.energy.cal.server.core.monitor.SystemMonitorService;
import com.siact.energy.cal.server.core.model.QueryContext;
import com.siact.energy.cal.server.core.model.ShardingParams;
import org.springframework.beans.BeanUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 智能查询规划器
 * <p>
 * 负责接收大型查询任务，根据预设的成本阈值，
 * 智能地将其分解为多个可控的、可并行执行的子任务，
 * 以解决查询超时和数据库压力过大的问题。
 */
@Service
@Slf4j
public class QueryPlanner {

    @Autowired
    private CalculationProperties props;
    @Autowired
    private TimeWindowGenerator timeWindowGenerator;
    @Autowired
    private DataBaseService dataBaseService;
    private final ForkJoinPool calculationPool = new ForkJoinPool(Math.min(Runtime.getRuntime().availableProcessors() * 2, 32));
    @Autowired
    private IntelligentShardingStrategy intelligentShardingStrategy;
    @Autowired
    private SystemMonitorService systemMonitorService;
    /**
     * 【重构版】执行查询计划 - 专注于分片策略，委托执行给DataBaseService
     */
    public ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> executePlan(
            List<String> indicators,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 1. 构建查询上下文
        QueryContext context = QueryContext.builder()
                .queryDTO(queryDTO)
                .dataSourceVo(dataSourceVo)
                .indicators(indicators)
                .systemCpuUsage(systemMonitorService.getCpuUsage())
                .systemMemoryUsage(systemMonitorService.getMemoryUsage())
                .activeConnections(getCurrentActiveConnections(dataSourceVo))
                .build();

        // 2. 制定分片策略
        ShardingParams shardingParams = intelligentShardingStrategy.createOptimalPlan(context);

        // 3. 生成子任务
        List<TimeQueryDTO> subTasks = createSubTasksWithIntelligentSharding(
                indicators, queryDTO, shardingParams);

        String planDescription = buildIntelligentPlanDescription(shardingParams, context);

        if (subTasks.size() > 1) {
            log.info("【分片计划已制定】原始任务将被分解为 {} 个子任务。{}", subTasks.size(), planDescription);
        } else {
            log.info("【分片计划已制定】任务无需分片，将直接执行。{}", planDescription);
        }

        // 4. 委托给DataBaseService执行具体的查询操作
        return delegateToDataBaseService(subTasks, dataSourceVo, shardingParams);
    }

    /**
     * 委托给DataBaseService执行查询操作
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> delegateToDataBaseService(
            List<TimeQueryDTO> subTasks, DataSourceVo dataSourceVo, ShardingParams shardingParams) {

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResultMap = new ConcurrentHashMap<>();

        if (subTasks.size() == 1) {
            // 单个任务，直接执行
            TimeQueryDTO singleTask = subTasks.get(0);
            dataBaseService.queryDataByEquallySpacedTime(globalResultMap, dataSourceVo, singleTask, singleTask.getDataCodes());
        } else {
            // 多个子任务，并行执行
            executeSubTasksInParallel(subTasks, dataSourceVo, globalResultMap, shardingParams);
        }

        return globalResultMap;
    }

    /**
     * 并行执行子任务
     */
    private void executeSubTasksInParallel(List<TimeQueryDTO> subTasks, DataSourceVo dataSourceVo,
                                         ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResultMap,
                                         ShardingParams shardingParams) {

        Semaphore semaphore = new Semaphore(shardingParams.getMaxConcurrency());
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (TimeQueryDTO subTask : subTasks) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    semaphore.acquire();
                    log.debug("执行子任务: 时间[{} -> {}], 指标数[{}]",
                            subTask.getStartTime(), subTask.getEndTime(), subTask.getDataCodes().size());

                    dataBaseService.queryDataByEquallySpacedTime(globalResultMap, dataSourceVo, subTask, subTask.getDataCodes());

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("子任务执行被中断", e);
                } catch (Exception e) {
                    log.error("子任务执行失败: 时间[{} -> {}], 指标数[{}]",
                            subTask.getStartTime(), subTask.getEndTime(), subTask.getDataCodes().size(), e);
                } finally {
                    semaphore.release();
                }
            }, calculationPool);

            futures.add(future);
        }

        // 等待所有子任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    /**
     * 【新增】执行一个“数据完整性检查”的查询计划。
     * <p>
     * 它会智能地分片执行 COUNT 查询，并在内存中汇总结果。
     *
     * @param indicators  需要检查的指标列表
     * @param queryDTO    定义了时间范围和检查粒度 (interval/tsUnit)
     * @param dataSourceVo 数据源信息
     * @return 一个 Map，Key是指标编码，Value是该指标在总时间范围内的总数据点数。
     */
    public Map<String, Long> executeCountPlan(
            List<String> indicators,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 1. 制定分片计划（与等间隔查询的分片逻辑完全相同）
        long days = TimeWindowGenerator.getDaysBetween(queryDTO.getStartTime(), queryDTO.getEndTime());
        boolean needsTimeSharding = days > props.getTimeShardThresholdDays();
        boolean needsMetricSharding = indicators.size() > props.getMetricShardThresholdCount();

        List<TimeQueryDTO> subTasks = breakdownForEqualInterval(indicators, queryDTO, props, needsTimeSharding, needsMetricSharding);

        log.info("【计数计划已制定】完整性检查任务被分解为 {} 个子任务。", subTasks.size());

        // 2. 并行调度与执行
        ConcurrentHashMap<String, Long> globalCounts = new ConcurrentHashMap<>();
        Semaphore semaphore = new Semaphore(props.getMaxConcurrentQueries());

        List<CompletableFuture<Void>> futures = subTasks.stream().map(subTask ->
                CompletableFuture.runAsync(() -> {
                    try {
                        semaphore.acquire();
                        // a. 调用 DataBaseService 执行单个分片的 COUNT 查询
                        Map<String, Long> shardCounts = dataBaseService.queryCountsForShard(subTask, dataSourceVo);

                        // b. 🔥【核心】在内存中进行二次汇总 (线程安全)
                        shardCounts.forEach((indicator, count) ->
                                globalCounts.merge(indicator, count, Long::sum)
                        );
                    } catch (Exception e) {
                        log.error("计数子任务执行失败", e);
                    } finally {
                        semaphore.release();
                    }
                }, calculationPool)
        ).collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return globalCounts;
    }

    /**
     * 分解策略一：为“等时间间隔”查询进行分片 (可同时按时间和指标)
     */
    private List<TimeQueryDTO> breakdownForEqualInterval(
            List<String> indicators,
            TimeQueryDTO dto,
            CalculationProperties props,
            boolean needsTimeSharding,
            boolean needsMetricSharding) {

        List<TimeWindow> timeShards;
        if (needsTimeSharding) {
            long days = TimeWindowGenerator.getDaysBetween(dto.getStartTime(), dto.getEndTime());
            String shardUnit = determineTimeShardUnit(days);
            timeShards = timeWindowGenerator.generateShards(dto.getStartTime(), dto.getEndTime(), 1, shardUnit);
        } else {
            timeShards = Collections.singletonList(new TimeWindow(dto.getStartTime(), dto.getEndTime(), null));
        }

        List<List<String>> metricShards;
        if (needsMetricSharding) {
            metricShards = Lists.partition(indicators, props.getMetricShardSize());
        } else {
            metricShards = Collections.singletonList(indicators);
        }

        List<TimeQueryDTO> subTasks = new ArrayList<>();
        for (TimeWindow shard : timeShards) {
            for (List<String> metricGroup : metricShards) {
                TimeQueryDTO subTaskDTO = new TimeQueryDTO();
                BeanUtils.copyProperties(dto, subTaskDTO);
                subTaskDTO.setStartTime(shard.getStartTime());
                subTaskDTO.setEndTime(shard.getEndTime());
                subTaskDTO.setDataCodes(metricGroup);
                subTasks.add(subTaskDTO);
            }
        }
        return subTasks;
    }

    /**
     * 分解策略二：为“时间区间采样 (FIRST/LAST)”查询进行分片 (只能按指标)
     */
    private List<TimeQueryDTO> breakdownForIntervalSampling(
            List<String> indicators,
            TimeQueryDTO dto,
            CalculationProperties props,
            boolean needsMetricSharding) {
        
        if (needsMetricSharding) {
            List<List<String>> metricShards = Lists.partition(indicators, props.getMetricShardSize());
            return metricShards.stream().map(group -> {
                TimeQueryDTO subTaskDTO = new TimeQueryDTO();
                BeanUtils.copyProperties(dto, subTaskDTO);
                subTaskDTO.setDataCodes(group);
                // 时间范围保持为完整的原始范围
                return subTaskDTO;
            }).collect(Collectors.toList());
        } else {
            dto.setDataCodes(indicators);
            return Collections.singletonList(dto);
        }
    }

    /**
     * 构建清晰的计划描述日志
     */
    private String buildPlanDescription(
            String queryType,
            boolean timeSharding,
            boolean metricSharding,
            long days,
            int metricCount) {
                
        StringBuilder sb = new StringBuilder();
        sb.append("查询类型: [").append(queryType).append("]。");
        sb.append(" 原始负载: [").append(metricCount).append("个指标, ").append(days).append("天]。");
        
        List<String> actions = new ArrayList<>();
        if (timeSharding) {
            actions.add("按 [时间] (粒度: " + determineTimeShardUnit(days) + ") 分片");
        }
        if (metricSharding) {
            actions.add("按 [指标] (每批 " + props.getMetricShardSize() + " 个) 分片");
        }
        
        if (actions.isEmpty()) {
            sb.append(" 分片策略: [无]。");
        } else {
            sb.append(" 分片策略: [").append(String.join(", ", actions)).append("]。");
        }
        return sb.toString();
    }
    
    /**
     * 智能决策时间分片粒度
     */
    private String determineTimeShardUnit(long days) {
        if (days <= 31) return "天(d)";
        if (days <= 180) return "周(w)";
        return "月(M)";
    }

    /**
     * 根据智能分片参数创建子任务
     */
    private List<TimeQueryDTO> createSubTasksWithIntelligentSharding(
            List<String> indicators,
            TimeQueryDTO dto,
            ShardingParams params) {

        switch (params.getStrategy()) {
            case NO_SHARDING:
                dto.setDataCodes(indicators);
                return Collections.singletonList(dto);

            case TIME_ONLY:
                return createTimeOnlyShards(indicators, dto, params);

            case METRIC_ONLY:
                return createMetricOnlyShards(indicators, dto, params);

            case BOTH:
                return createBothShards(indicators, dto, params);

            case ADAPTIVE:
                return createAdaptiveShards(indicators, dto, params);

            default:
                // 降级到原有逻辑
                return breakdownForEqualInterval(indicators, dto, props,
                        params.isNeedsTimeSharding(), params.isNeedsMetricSharding());
        }
    }

    /**
     * 创建仅时间分片的子任务
     */
    private List<TimeQueryDTO> createTimeOnlyShards(List<String> indicators, TimeQueryDTO dto, ShardingParams params) {
        List<TimeWindow> timeShards = timeWindowGenerator.generateShards(
                dto.getStartTime(), dto.getEndTime(), params.getTimeShardDays(), "d");

        return timeShards.stream().map(shard -> {
            TimeQueryDTO subTaskDTO = new TimeQueryDTO();
            BeanUtils.copyProperties(dto, subTaskDTO);
            subTaskDTO.setStartTime(shard.getStartTime());
            subTaskDTO.setEndTime(shard.getEndTime());
            subTaskDTO.setDataCodes(indicators);
            return subTaskDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 创建仅指标分片的子任务
     */
    private List<TimeQueryDTO> createMetricOnlyShards(List<String> indicators, TimeQueryDTO dto, ShardingParams params) {
        List<List<String>> metricShards = Lists.partition(indicators, params.getMetricShardSize());

        return metricShards.stream().map(metricGroup -> {
            TimeQueryDTO subTaskDTO = new TimeQueryDTO();
            BeanUtils.copyProperties(dto, subTaskDTO);
            subTaskDTO.setDataCodes(metricGroup);
            return subTaskDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 创建时间和指标都分片的子任务
     */
    private List<TimeQueryDTO> createBothShards(List<String> indicators, TimeQueryDTO dto, ShardingParams params) {
        List<TimeWindow> timeShards = timeWindowGenerator.generateShards(
                dto.getStartTime(), dto.getEndTime(), params.getTimeShardDays(), "d");
        List<List<String>> metricShards = Lists.partition(indicators, params.getMetricShardSize());

        List<TimeQueryDTO> subTasks = new ArrayList<>();
        for (TimeWindow timeShard : timeShards) {
            for (List<String> metricGroup : metricShards) {
                TimeQueryDTO subTaskDTO = new TimeQueryDTO();
                BeanUtils.copyProperties(dto, subTaskDTO);
                subTaskDTO.setStartTime(timeShard.getStartTime());
                subTaskDTO.setEndTime(timeShard.getEndTime());
                subTaskDTO.setDataCodes(metricGroup);
                subTasks.add(subTaskDTO);
            }
        }
        return subTasks;
    }

    /**
     * 创建自适应分片的子任务
     */
    private List<TimeQueryDTO> createAdaptiveShards(List<String> indicators, TimeQueryDTO dto, ShardingParams params) {
        // 自适应策略：根据数据密度动态决定分片方式
        long timeSpan = TimeWindowGenerator.getDaysBetween(dto.getStartTime(), dto.getEndTime());
        int metricCount = indicators.size();

        // 如果时间跨度大但指标少，优先时间分片
        if (timeSpan > metricCount / 10) {
            return createTimeOnlyShards(indicators, dto, params);
        }
        // 如果指标多但时间跨度小，优先指标分片
        else if (metricCount > timeSpan * 10) {
            return createMetricOnlyShards(indicators, dto, params);
        }
        // 否则两者都分片
        else {
            return createBothShards(indicators, dto, params);
        }
    }



    /**
     * 构建智能分片计划描述
     */
    private String buildIntelligentPlanDescription(ShardingParams params, QueryContext context) {
        StringBuilder sb = new StringBuilder();
        sb.append("智能分片策略: [").append(params.getStrategy()).append("]。");
        sb.append(" 原始负载: [").append(context.getMetricCount()).append("个指标, ")
                .append(context.getTimeSpanDays()).append("天]。");
        sb.append(" 系统状态: [CPU=").append(String.format("%.1f", context.getSystemCpuUsage()))
                .append("%, 内存=").append(String.format("%.1f", context.getSystemMemoryUsage())).append("%]。");

        if (params.isNeedsTimeSharding()) {
            sb.append(" 时间分片: [").append(params.getTimeShardDays()).append("天/片]。");
        }
        if (params.isNeedsMetricSharding()) {
            sb.append(" 指标分片: [").append(params.getMetricShardSize()).append("个/片]。");
        }
        sb.append(" 并发控制: [").append(params.getMaxConcurrency()).append("个并发]。");

        return sb.toString();
    }

    /**
     * 获取当前活跃连接数（简化实现）
     */
    private int getCurrentActiveConnections(DataSourceVo dataSourceVo) {
        // 这里可以根据实际情况实现连接池监控
        // 暂时返回一个估算值
        return 20;
    }
}