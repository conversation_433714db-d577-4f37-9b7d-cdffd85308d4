package com.siact.energy.cal.server.core.strategy;

import com.siact.energy.cal.server.core.model.QueryContext;
import com.siact.energy.cal.server.core.model.ShardingParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 🔥 聚合指标专用智能分片策略
 * 
 * 核心原则：
 * 1. 优先使用指标分片，避免时间分片导致的二次聚合问题
 * 2. 针对不同聚合函数采用不同的分片策略
 * 3. 考虑聚合函数的数学特性，确保结果精确性
 */
@Component
@Slf4j
public class AggregateShardingStrategy {
    
    // 聚合函数分类
    private static final Set<String> DISTRIBUTIVE_FUNCTIONS = new HashSet<>(Arrays.asList(
            "sum", "count", "max", "min")); // 可分布式计算的函数
    
    private static final Set<String> ALGEBRAIC_FUNCTIONS = new HashSet<>(Arrays.asList(
            "avg")); // 需要额外信息的代数函数
    
    private static final Set<String> HOLISTIC_FUNCTIONS = new HashSet<>(Arrays.asList(
            "first", "last", "diff")); // 需要完整数据集的整体函数
    
    /**
     * 🎯 为聚合指标创建专用分片策略
     */
    public ShardingParams createAggregateOptimalPlan(QueryContext context, 
                                                   Map<String, Set<String>> functionToMetrics) {
        
        log.info("🔥 启动聚合指标智能分片: 指标数={}, 时间跨度={}天, 聚合函数={}", 
                context.getMetricCount(), context.getTimeSpanDays(), functionToMetrics.keySet());
        
        // 1. 分析聚合函数特性
        AggregationComplexity complexity = analyzeAggregationComplexity(functionToMetrics);
        log.debug("聚合复杂度分析: {}", complexity);
        
        // 2. 基于聚合特性制定分片策略
        AggregateShardingDecision decision = makeAggregateShardingDecision(complexity, context);
        log.info("聚合分片决策: 策略={}, 时间分片={}, 指标分片={}, 原因={}", 
                decision.strategy, decision.allowTimeSharding, decision.metricShardSize, decision.reason);
        
        // 3. 构建分片参数
        return buildAggregateShardingParams(decision, context);
    }
    
    /**
     * 分析聚合复杂度
     */
    private AggregationComplexity analyzeAggregationComplexity(Map<String, Set<String>> functionToMetrics) {
        boolean hasDistributive = functionToMetrics.keySet().stream()
                .anyMatch(DISTRIBUTIVE_FUNCTIONS::contains);
        boolean hasAlgebraic = functionToMetrics.keySet().stream()
                .anyMatch(ALGEBRAIC_FUNCTIONS::contains);
        boolean hasHolistic = functionToMetrics.keySet().stream()
                .anyMatch(HOLISTIC_FUNCTIONS::contains);
        
        int totalMetrics = functionToMetrics.values().stream()
                .mapToInt(Set::size).sum();
        
        // 计算复杂度等级
        ComplexityLevel level;
        if (hasHolistic || (hasAlgebraic && totalMetrics > 100)) {
            level = ComplexityLevel.HIGH;
        } else if (hasAlgebraic || (hasDistributive && totalMetrics > 200)) {
            level = ComplexityLevel.MEDIUM;
        } else {
            level = ComplexityLevel.LOW;
        }
        
        return new AggregationComplexity(hasDistributive, hasAlgebraic, hasHolistic, totalMetrics, level);
    }
    
    /**
     * 制定聚合分片决策
     */
    private AggregateShardingDecision makeAggregateShardingDecision(AggregationComplexity complexity, 
                                                                  QueryContext context) {
        
        long timeSpan = context.getTimeSpanDays();
        int metricCount = context.getMetricCount();
        boolean isEquallySpaced = context.getQueryDTO().isEquallySpacedQuery();
        
        // 🔥 核心决策逻辑：优先考虑聚合函数特性
        
        if (complexity.hasHolistic) {
            // 整体函数：绝对不能时间分片，只能指标分片
            return new AggregateShardingDecision(
                    ShardingParams.ShardingStrategy.METRIC_ONLY,
                    false, // 禁止时间分片
                    calculateConservativeMetricShardSize(metricCount, timeSpan),
                    "包含整体函数(first/last/diff)，禁止时间分片以确保精确性"
            );
        }
        
        if (complexity.hasAlgebraic) {
            // 代数函数(avg)：尽量避免时间分片
            if (metricCount > 150 && timeSpan > 60) {
                // 极大查询：必须时间分片，但要特殊处理AVG
                return new AggregateShardingDecision(
                        ShardingParams.ShardingStrategy.BOTH,
                        true, // 允许时间分片，但需要特殊处理
                        calculateConservativeMetricShardSize(metricCount, timeSpan),
                        "极大查询包含AVG函数，允许时间分片但需要特殊聚合逻辑"
                );
            } else {
                // 中等查询：只用指标分片
                return new AggregateShardingDecision(
                        ShardingParams.ShardingStrategy.METRIC_ONLY,
                        false,
                        calculateOptimalMetricShardSize(metricCount, timeSpan, isEquallySpaced),
                        "包含AVG函数，优先使用指标分片避免复杂的加权平均计算"
                );
            }
        }
        
        if (complexity.hasDistributive) {
            // 分布式函数(sum/max/min/count)：可以安全地时间分片
            if (metricCount > 100 && timeSpan > 30) {
                return new AggregateShardingDecision(
                        ShardingParams.ShardingStrategy.BOTH,
                        true,
                        calculateOptimalMetricShardSize(metricCount, timeSpan, isEquallySpaced),
                        "分布式函数支持安全的时间分片"
                );
            } else if (metricCount > 50) {
                return new AggregateShardingDecision(
                        ShardingParams.ShardingStrategy.METRIC_ONLY,
                        false,
                        calculateOptimalMetricShardSize(metricCount, timeSpan, isEquallySpaced),
                        "中等规模查询，优先使用指标分片"
                );
            }
        }
        
        // 默认：小规模查询，无需分片
        return new AggregateShardingDecision(
                ShardingParams.ShardingStrategy.NO_SHARDING,
                false,
                metricCount,
                "小规模聚合查询，无需分片"
        );
    }
    
    /**
     * 计算保守的指标分片大小（用于复杂聚合函数）
     */
    private int calculateConservativeMetricShardSize(int metricCount, long timeSpan) {
        // 对于复杂聚合函数，使用更小的分片以减少单次查询压力
        int baseSize;
        if (timeSpan > 90) {
            baseSize = 10; // 超长时间跨度，极小分片
        } else if (timeSpan > 30) {
            baseSize = 15; // 长时间跨度，小分片
        } else if (timeSpan > 7) {
            baseSize = 25; // 中等时间跨度，中小分片
        } else {
            baseSize = 40; // 短时间跨度，中等分片
        }
        
        return Math.max(5, Math.min(baseSize, metricCount));
    }
    
    /**
     * 计算最优指标分片大小
     */
    private int calculateOptimalMetricShardSize(int metricCount, long timeSpan, boolean isEquallySpaced) {
        int baseSize;
        
        if (isEquallySpaced) {
            // 等间隔查询：数据量大，需要较小分片
            if (timeSpan > 60) {
                baseSize = 15;
            } else if (timeSpan > 30) {
                baseSize = 25;
            } else if (timeSpan > 7) {
                baseSize = 35;
            } else {
                baseSize = 50;
            }
        } else {
            // 区间查询：数据量相对固定，可以较大分片
            if (timeSpan > 60) {
                baseSize = 30;
            } else if (timeSpan > 30) {
                baseSize = 50;
            } else {
                baseSize = 80;
            }
        }
        
        return Math.max(5, Math.min(baseSize, metricCount));
    }
    
    /**
     * 构建聚合分片参数
     */
    private ShardingParams buildAggregateShardingParams(AggregateShardingDecision decision, QueryContext context) {
        // 时间分片策略：如果允许时间分片，使用较大的时间分片以减少二次聚合复杂度
        int timeShardDays = decision.allowTimeSharding ? 
                calculateAggregateTimeShardDays(context) : (int) context.getTimeSpanDays();
        
        // 并发控制：聚合查询通常更消耗资源，使用较低并发
        int maxConcurrency = calculateAggregateConcurrency(context, decision);
        
        return ShardingParams.builder()
                .strategy(decision.strategy)
                .timeShardDays(timeShardDays)
                .metricShardSize(decision.metricShardSize)
                .needsTimeSharding(decision.allowTimeSharding && timeShardDays < context.getTimeSpanDays())
                .needsMetricSharding(decision.metricShardSize < context.getMetricCount())
                .maxConcurrency(maxConcurrency)
                .build();
    }
    
    /**
     * 计算聚合查询的时间分片天数
     */
    private int calculateAggregateTimeShardDays(QueryContext context) {
        long timeSpan = context.getTimeSpanDays();
        
        // 聚合查询的时间分片应该较大，以减少二次聚合的复杂度
        if (timeSpan > 180) {
            return 30; // 超长时间跨度，月分片
        } else if (timeSpan > 90) {
            return 15; // 长时间跨度，半月分片
        } else if (timeSpan > 30) {
            return 10; // 中等时间跨度，10天分片
        } else {
            return 7;  // 短时间跨度，周分片
        }
    }
    
    /**
     * 计算聚合查询的并发数
     */
    private int calculateAggregateConcurrency(QueryContext context, AggregateShardingDecision decision) {
        // 聚合查询通常更消耗资源，使用较低并发
        int baseConcurrency = 4;
        
        // 根据系统负载调整
        if (context.getSystemCpuUsage() > 80 || context.getSystemMemoryUsage() > 85) {
            baseConcurrency = 2; // 系统高负载时降低并发
        } else if (context.getSystemCpuUsage() < 50 && context.getSystemMemoryUsage() < 60) {
            baseConcurrency = 6; // 系统低负载时可以提高并发
        }
        
        // 根据复杂度调整
        if (decision.strategy == ShardingParams.ShardingStrategy.BOTH) {
            baseConcurrency = Math.max(2, baseConcurrency - 1); // 双重分片时降低并发
        }
        
        return Math.max(2, Math.min(baseConcurrency, 8));
    }
    
    // 数据类定义
    private static class AggregationComplexity {
        final boolean hasDistributive;
        final boolean hasAlgebraic;
        final boolean hasHolistic;
        final int totalMetrics;
        final ComplexityLevel level;
        
        AggregationComplexity(boolean hasDistributive, boolean hasAlgebraic, boolean hasHolistic, 
                            int totalMetrics, ComplexityLevel level) {
            this.hasDistributive = hasDistributive;
            this.hasAlgebraic = hasAlgebraic;
            this.hasHolistic = hasHolistic;
            this.totalMetrics = totalMetrics;
            this.level = level;
        }
        
        @Override
        public String toString() {
            return String.format("分布式=%s, 代数=%s, 整体=%s, 总指标=%d, 等级=%s", 
                    hasDistributive, hasAlgebraic, hasHolistic, totalMetrics, level);
        }
    }
    
    private static class AggregateShardingDecision {
        final ShardingParams.ShardingStrategy strategy;
        final boolean allowTimeSharding;
        final int metricShardSize;
        final String reason;
        
        AggregateShardingDecision(ShardingParams.ShardingStrategy strategy, boolean allowTimeSharding, 
                                int metricShardSize, String reason) {
            this.strategy = strategy;
            this.allowTimeSharding = allowTimeSharding;
            this.metricShardSize = metricShardSize;
            this.reason = reason;
        }
    }
    
    private enum ComplexityLevel {
        LOW, MEDIUM, HIGH
    }
}
