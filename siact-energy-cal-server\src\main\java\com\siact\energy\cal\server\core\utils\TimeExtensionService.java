package com.siact.energy.cal.server.core.utils;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 统一时间扩展服务
 * 
 * 解决时间边界问题的系统化方案：
 * 1. 统一扩展时间窗口和查询DTO
 * 2. 确保所有计算器（基础指标、聚合指标、衍生指标）使用一致的时间范围
 * 3. 避免在各个计算器中重复实现时间扩展逻辑
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class TimeExtensionService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 扩展时间窗口列表
     * 在原有时间窗口基础上，扩展查询时间范围以确保边界数据完整
     * 
     * @param originalTimeWindows 原始时间窗口列表
     * @param queryDTO 查询参数
     * @return 扩展后的时间窗口列表
     */
    public List<TimeWindow> extendTimeWindows(List<TimeWindow> originalTimeWindows, TimeQueryDTO queryDTO) {
        List<TimeWindow> timeWindows = new ArrayList<>();
        for (TimeWindow originalTimeWindow : originalTimeWindows) {
            timeWindows.add(originalTimeWindow);
        }
        if (timeWindows == null || timeWindows.isEmpty()) {
            log.warn("时间窗口列表为空，无法扩展");
            return timeWindows;
        }

        try {
            // 获取最后一个时间窗口
            TimeWindow lastWindow = timeWindows.get(timeWindows.size() - 1);
            String originalEndTime = lastWindow.getEndTime();

            // 关键修复：为差值计算总是扩展一个时间窗口
            // 差值计算需要下一个时间点的first值，所以即使最后窗口完整也需要扩展
            String extendedEndTime = extendQueryEndTime(originalEndTime, queryDTO.getInterval(), queryDTO.getTsUnit());

            // 如果扩展后的时间与原始时间相同，说明扩展失败
            if (originalEndTime.equals(extendedEndTime)) {
                log.debug("时间扩展失败，使用原始时间窗口: {}", originalEndTime);
                return timeWindows;
            }
            
            // 创建扩展的时间窗口
            // 关键修改：扩展窗口的alignedTime应该是窗口开始时间，用于差值计算
            TimeWindow extendedWindow = new TimeWindow(
                lastWindow.getEndTime(),     // 新窗口的开始时间是原最后窗口的结束时间
                extendedEndTime,             // 扩展后的结束时间
                lastWindow.getEndTime()      // 对齐时间（扩展窗口的开始时间）
            );
            
            // 添加扩展窗口到列表
            timeWindows.add(extendedWindow);
            
            log.info("时间窗口扩展完成: 原窗口数={}, 扩展后窗口数={}, 扩展时间范围: {} -> {}",
                    timeWindows.size() - 1, timeWindows.size(), originalEndTime, extendedEndTime);
            
            return timeWindows;
            
        } catch (Exception e) {
            log.error("扩展时间窗口失败", e);
            return timeWindows;
        }
    }

    /**
     * 扩展查询DTO的时间范围
     * 
     * @param originalQueryDTO 原始查询DTO
     * @return 扩展后的查询DTO
     */
    public TimeQueryDTO extendQueryDTO(TimeQueryDTO originalQueryDTO) {
        if (originalQueryDTO == null) {
            return null;
        }

        try {
            // 创建新的查询DTO，避免修改原始对象
            TimeQueryDTO extendedQueryDTO = new TimeQueryDTO();
            
            // 复制所有属性
            extendedQueryDTO.setDataCodes(originalQueryDTO.getDataCodes());
            extendedQueryDTO.setStartTime(originalQueryDTO.getStartTime());
            extendedQueryDTO.setInterval(originalQueryDTO.getInterval());
            extendedQueryDTO.setTsUnit(originalQueryDTO.getTsUnit());
            extendedQueryDTO.setQueryType(originalQueryDTO.getQueryType());
            
            // 扩展结束时间
            String extendedEndTime = extendQueryEndTime(
                originalQueryDTO.getEndTime(),
                originalQueryDTO.getInterval(),
                originalQueryDTO.getTsUnit()
            );
            extendedQueryDTO.setEndTime(extendedEndTime);
            
            log.debug("查询DTO时间扩展: {} -> {}", 
                originalQueryDTO.getEndTime(), extendedEndTime);
            
            return extendedQueryDTO;
            
        } catch (Exception e) {
            log.warn("扩展查询DTO失败，使用原始DTO: {}", e.getMessage());
            return originalQueryDTO;
        }
    }

    /**
     * 扩展查询结束时间
     * 支持所有时间单位：m(分钟), h(小时), d(天), n(月), y(年)
     * 
     * @param originalEndTime 原始结束时间
     * @param interval 时间间隔
     * @param tsUnit 时间单位
     * @return 扩展后的结束时间
     */
    public String extendQueryEndTime(String originalEndTime, Integer interval, String tsUnit) {
        try {
            if (interval == null || tsUnit == null || originalEndTime == null) {
                return originalEndTime;
            }

            LocalDateTime endTime = LocalDateTime.parse(originalEndTime, DATE_TIME_FORMATTER);

            // 根据时间单位扩展一个间隔，支持所有时间单位
            LocalDateTime extendedEndTime;
            String normalizedUnit = tsUnit.toLowerCase();

            switch (normalizedUnit) {
                case "m":
                case "min":
                case "minute":
                    extendedEndTime = endTime.plusMinutes(interval);
                    break;
                case "h":
                case "hour":
                    extendedEndTime = endTime.plusHours(interval);
                    break;
                case "d":
                case "day":
                    extendedEndTime = endTime.plusDays(interval);
                    break;
                case "n":
                case "month":
                    extendedEndTime = endTime.plusMonths(interval);
                    break;
                case "y":
                case "year":
                    extendedEndTime = endTime.plusYears(interval);
                    break;
                default:
                    log.warn("不支持的时间单位: {}, 支持的单位: m(分钟), h(小时), d(天), n(月), y(年), 使用原始结束时间", tsUnit);
                    return originalEndTime;
            }

            String result = extendedEndTime.format(DATE_TIME_FORMATTER);
            log.debug("扩展查询结束时间: {} -> {} (扩展{}个{}, 单位: {})",
                originalEndTime, result, interval, normalizedUnit, tsUnit);
            return result;

        } catch (Exception e) {
            log.warn("扩展查询结束时间失败: {}, 使用原始时间", e.getMessage());
            return originalEndTime;
        }
    }

    /**
     * 检查是否需要时间窗口扩展
     *
     * 扩展条件：
     * 1. 最后一个时间窗口不是完整的间隔窗口
     * 2. 最后一个时间窗口的结束时间不在间隔边界上
     *
     * @param lastWindow 最后一个时间窗口
     * @param queryDTO 查询参数
     * @return true表示需要扩展，false表示不需要
     */
    private boolean needsTimeWindowExtension(TimeWindow lastWindow, TimeQueryDTO queryDTO) {
        try {
            if (queryDTO.getInterval() == null || queryDTO.getTsUnit() == null) {
                return false;
            }

            LocalDateTime windowStart = LocalDateTime.parse(lastWindow.getStartTime(), DATE_TIME_FORMATTER);
            LocalDateTime windowEnd = LocalDateTime.parse(lastWindow.getEndTime(), DATE_TIME_FORMATTER);

            // 计算完整间隔的预期结束时间
            LocalDateTime expectedEnd;
            String normalizedUnit = queryDTO.getTsUnit().toLowerCase();

            switch (normalizedUnit) {
                case "m":
                case "min":
                case "minute":
                    expectedEnd = windowStart.plusMinutes(queryDTO.getInterval());
                    break;
                case "h":
                case "hour":
                    expectedEnd = windowStart.plusHours(queryDTO.getInterval());
                    break;
                case "d":
                case "day":
                    expectedEnd = windowStart.plusDays(queryDTO.getInterval());
                    break;
                case "n":
                case "month":
                    expectedEnd = windowStart.plusMonths(queryDTO.getInterval());
                    break;
                case "y":
                case "year":
                    expectedEnd = windowStart.plusYears(queryDTO.getInterval());
                    break;
                default:
                    log.warn("不支持的时间单位: {}, 不扩展时间窗口", queryDTO.getTsUnit());
                    return false;
            }

            // 如果实际结束时间小于预期结束时间，说明最后一个窗口不完整，需要扩展
            boolean needsExtension = windowEnd.isBefore(expectedEnd);

            log.debug("时间窗口扩展检查: 窗口[{} - {}], 预期结束时间: {}, 需要扩展: {}",
                lastWindow.getStartTime(), lastWindow.getEndTime(),
                expectedEnd.format(DATE_TIME_FORMATTER), needsExtension);

            return needsExtension;

        } catch (Exception e) {
            log.warn("检查时间窗口扩展需求失败: {}, 默认不扩展", e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否需要时间扩展
     *
     * @param queryDTO 查询参数
     * @return true表示需要扩展，false表示不需要
     */
    public boolean needsTimeExtension(TimeQueryDTO queryDTO) {
        return queryDTO != null
            && queryDTO.getInterval() != null
            && queryDTO.getTsUnit() != null
            && queryDTO.getEndTime() != null;
    }

    /**
     * 获取时间扩展统计信息
     */
    public String getExtensionStats(TimeQueryDTO originalQueryDTO, TimeQueryDTO extendedQueryDTO) {
        if (originalQueryDTO == null || extendedQueryDTO == null) {
            return "无扩展信息";
        }
        
        return String.format("时间扩展: %s -> %s (间隔: %d%s)", 
            originalQueryDTO.getEndTime(), 
            extendedQueryDTO.getEndTime(),
            originalQueryDTO.getInterval(),
            originalQueryDTO.getTsUnit());
    }
}
