package com.siact.energy.cal.server.common.flow.init;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.siact.energy.cal.common.util.utils.StringUtils;
import com.siact.energy.cal.server.dao.flow.RuleDetailMapper;
import com.siact.energy.cal.server.entity.flow.RuleDetailEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-03
 * @Description: 初始化
 * @Version: 1.0
 */
@Component
@Slf4j
public class TaoSnit {

    // 规则id与点位对应关系
    public Map<String, Set<String>> idAndProperty = new HashMap<>();

    @Resource
    RuleDetailMapper ruleDetailMapper;
    @PostConstruct
    public void devPropertyInit(){
        log.info("---初始化点位开始----");
        // 初始化所有规则需要查询的指标
        LambdaQueryWrapper<RuleDetailEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<RuleDetailEntity> ruleDetailEntities = ruleDetailMapper.selectList(lambdaQueryWrapper);
        ruleDetailEntities.forEach(i->{
            String ruleFormula = i.getRuleFormula();
            if (ruleFormula != null && !ruleFormula.trim().isEmpty()) {
                Set<String> code = StringUtils.getCode(ruleFormula);
                idAndProperty.put(String.valueOf(i.getId()), code);
            } else {
                log.warn("规则ID: {} 的公式为空，跳过处理", i.getId());
                idAndProperty.put(String.valueOf(i.getId()), new HashSet<>());
            }
        });
        log.info("----初始化点位结束----");
    }

}
