package com.siact.energy.cal.common.pojo.dto.ruleDetail;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * 指标详情表(RuleDetail) 查询DTO
 *
 * <AUTHOR>
 * @since 2024-05-21 10:07:00
 */
@ApiModel("指标详情表查询DTO")
@Data
public class RuleDetailQueryDTO {

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", position = 1)
    private String ruleName;

    /**
     * 指标类型（0-实例级规则，1-模型级规则）
     */
    @ApiModelProperty(value = "指标类型（0-实例级规则，1-模型级规则）", position = 3)
    private Integer ruleType;

    /**
     * 计算类型（0-数值计算，1-逻辑计算，2-正则表达式）
     */
    @ApiModelProperty(value = "计算类型（0-数值计算，1-逻辑计算，2-正则表达式）", position = 4)
    private Integer calType;

    /**
     * 节点（模型）名称
     */
    @ApiModelProperty(value = "节点（模型）名称", position = 6)
    private String devName;

    /**
     * 属性（模型）名称
     */
    @ApiModelProperty(value = "属性（模型）名称", position = 8)
    private String propName;

    /**
     * 启用状态（0-启用,1-未启用）
     */
    @ApiModelProperty(value = "启用状态（0-启用,1-未启用）", position = 13)
    private Integer activeState;

    /**
     * 指标集名称
     */
    @ApiModelProperty(value = "指标集名称", position = 14)
    private String ruleColName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", position = 15)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

}

