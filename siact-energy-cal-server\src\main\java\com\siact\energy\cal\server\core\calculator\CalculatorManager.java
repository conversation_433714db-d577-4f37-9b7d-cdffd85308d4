    package com.siact.energy.cal.server.core.calculator;


    import cn.hutool.core.bean.BeanUtil;
    import cn.hutool.core.collection.CollUtil;
    import cn.hutool.core.util.ObjectUtil;
    import cn.hutool.core.util.StrUtil;
    import com.github.benmanes.caffeine.cache.Cache;
    import com.github.benmanes.caffeine.cache.Caffeine;
    import com.siact.energy.cal.common.core.exception.BizException;
    import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
    import com.siact.energy.cal.common.pojo.enums.ConstantBase;
    import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
    import com.siact.energy.cal.common.util.utils.FormulaUtils;
    import com.siact.energy.cal.common.util.utils.InsCodeUtil;
    import com.siact.energy.cal.server.common.cache.MultiLevelCacheTemplate;
    import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
    import com.siact.energy.cal.server.core.pojo.CalculationResult;
    import com.siact.energy.cal.server.core.pojo.IndicatorClassification;
    import com.siact.energy.cal.server.core.pojo.TimeWindow;
    import com.siact.energy.cal.server.core.service.FormulaCacheService;
    import com.siact.energy.cal.server.core.utils.TimeExtensionService;
    import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
    import com.siact.energy.cal.server.service.dataSource.DataSourceService;
    import com.siact.energy.cal.server.service.ruleDetail.RuleDetailInstanceService;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.beans.factory.annotation.Autowired;
    import org.springframework.beans.factory.annotation.Qualifier;
    import org.springframework.stereotype.Service;
    import org.springframework.util.StopWatch;

    import javax.annotation.Resource;
    import java.math.BigDecimal;
    import java.time.Duration;
    import java.util.*;
    import java.util.concurrent.CompletableFuture;
    import java.util.concurrent.ConcurrentHashMap;
    import java.util.concurrent.Executor;
    import java.util.concurrent.TimeUnit;

    /**
     * @Package com.siact.energy.cal.server.core
     * @description: 计算器统一入口类
     * <AUTHOR>
     * @create 2025/8/8 15:43
     */

    @Service
    @Slf4j
    public class CalculatorManager {

        @Resource
        RuleDetailInstanceService ruleDetailInstanceService;

        @Autowired
        private MultiLevelCacheTemplate cacheTemplate;
        @Resource
        TimeWindowGenerator timeWindowGenerator;
        @Resource
        FormulaCacheService formulaCacheService;
        @Resource
        private TimeExtensionService timeExtensionService;

        @Autowired
        private BaseIndicatorCalculator baseIndicatorCalculator;

        @Autowired
        private AggIndicatorCalculator aggIndicatorCalculator;

        @Autowired
        private DerivedIndicatorCalculator derivedIndicatorCalculator;

        @Autowired
        private DataSourceService dataSourceService;



        // 🔥【修改】定义Caffeine缓存实例，但不再自己编写加载逻辑
        private final Cache<String, Map<String, List<String>>> calTypeConfigCache = Caffeine.newBuilder()
                .maximumSize(10) // 缓存最多10个项目的配置
                .expireAfterWrite(1, TimeUnit.HOURS) // 配置缓存1小时
                .build();
        /**
         * <AUTHOR>
         * @Description //等时间间隔统一计算
         * @Date 19:55 2025/8/10
         * @Param queryDTO
         * @return java.util.concurrent.CompletableFuture<com.siact.energy.cal.server.core.pojo.CalculationResult>
         **/
        public CompletableFuture<CalculationResult> calculateEquallySpacedTimeData(TimeQueryDTO queryDTO) {
            return CompletableFuture.supplyAsync(() -> {
                StopWatch stopWatch = new StopWatch("等时间间隔计算");

                try {
                    // 1.获取项目编码
                    String projectCode = getProjectCode(queryDTO.getDataCodes());

                    //2.获取所有涉及到计算指标，并设置到DTO中
                    List<String> formulaVarList = getFormulaVarList(queryDTO.getDataCodes());
                    TimeQueryDTO timeQueryDTO = new TimeQueryDTO();
                    BeanUtil.copyProperties(queryDTO, timeQueryDTO);
                    timeQueryDTO.setDataCodes(formulaVarList);

                    // 2. 指标分类 (10ms)
                    stopWatch.start("指标分类");
                    IndicatorClassification classification = classifyIndicators(timeQueryDTO.getDataCodes(),projectCode);
                    stopWatch.stop();

                    // 2. 生成时间窗口 (5ms)
                    stopWatch.start("时间窗口生成");
                    List<TimeWindow> originalTimeWindows = timeWindowGenerator.generateTimeWindows(timeQueryDTO);
                    stopWatch.stop();

                    // 3. 系统化时间扩展 (5ms)
                    stopWatch.start("时间扩展");
                    List<TimeWindow> timeWindows = timeExtensionService.extendTimeWindows(originalTimeWindows, timeQueryDTO);
                    TimeQueryDTO extendedQueryDTO = timeExtensionService.extendQueryDTO(timeQueryDTO);
                    log.info("系统化时间扩展完成: {}", timeExtensionService.getExtensionStats(timeQueryDTO, extendedQueryDTO));
                    stopWatch.stop();

                    // 4. 创建全局结果容器
                    ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults = new ConcurrentHashMap<>();

                    // 5. 三阶段并行计算 - 使用扩展后的时间参数
                    stopWatch.start("三阶段计算");
                    executeThreePhaseCalculation(projectCode, classification, timeWindows, extendedQueryDTO, globalResults);
                    stopWatch.stop();

                    log.info("高性能计算完成: 指标数={}, 原始时间窗口数={}, 扩展时间窗口数={}, 总耗时={}ms, 详情: {}",
                            timeQueryDTO.getDataCodes().size(), originalTimeWindows.size(), timeWindows.size(),
                            stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

                    // 返回计算结果和原始时间窗口（用于结果组装），由控制器层进行结果组装和过滤
                    return new CalculationResult(globalResults, originalTimeWindows, stopWatch.prettyPrint());

                } catch (Exception e) {
                    log.error("高性能计算异常", e);
                    throw new RuntimeException("计算失败", e);
                }
            });
        }

        /**
         * <AUTHOR>
         * @Description //时间区间统一计算
         * @Date 20:28 2025/8/10
         * @Param [queryDTO]
         * @return java.util.concurrent.CompletableFuture<com.siact.energy.cal.server.core.pojo.CalculationResult>
         **/

        public CompletableFuture<CalculationResult> calculateIntervalTimeData(TimeQueryDTO queryDTO) {
            return CompletableFuture.supplyAsync(() -> {
                StopWatch stopWatch = new StopWatch("时间区间计算");

                try {
                    // 1.获取项目编码
                    String projectCode = getProjectCode(queryDTO.getDataCodes());

                    //2.获取所有涉及到计算指标，并设置到DTO中
                    List<String> formulaVarList = getFormulaVarList(queryDTO.getDataCodes());
                    TimeQueryDTO timeQueryDTO = new TimeQueryDTO();
                    BeanUtil.copyProperties(queryDTO, timeQueryDTO);
                    timeQueryDTO.setDataCodes(formulaVarList);

                    // 2. 指标分类 (10ms)
                    stopWatch.start("指标分类");
                    IndicatorClassification classification = classifyIndicators(timeQueryDTO.getDataCodes(),projectCode);
                    stopWatch.stop();

                    // 3. 生成时间窗口 (5ms)
                    stopWatch.start("时间窗口生成");
                    List<TimeWindow> timeWindows = timeWindowGenerator.createIntervalTimeWindow(timeQueryDTO);
                    stopWatch.stop();
                    // 3. 创建全局结果容器
                    ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults = new ConcurrentHashMap<>();

                    // 4. 三阶段并行计算 - 使用扩展后的时间参数
                    stopWatch.start("三阶段计算");
                    executeThreePhaseCalculation(projectCode, classification, timeWindows, timeQueryDTO, globalResults);
                    stopWatch.stop();

                    // 返回计算结果和原始时间窗口（用于结果组装），由控制器层进行结果组装和过滤
                    return new CalculationResult(globalResults, timeWindows, stopWatch.prettyPrint());

                } catch (Exception e) {
                    log.error("高性能计算异常", e);
                    throw new RuntimeException("计算失败", e);
                }
            });
        }


        /**
         * <AUTHOR>
         * @Description //时间截面统一计算
         * @Date 20:28 2025/8/10
         * @Param [queryDTO]
         * @return java.util.concurrent.CompletableFuture<com.siact.energy.cal.server.core.pojo.CalculationResult>
         **/

        public CompletableFuture<CalculationResult> calculateTimeSliceData(TimeQueryDTO queryDTO) {
            return CompletableFuture.supplyAsync(() -> {
                StopWatch stopWatch = new StopWatch("时间截面计算");

                try {
                    // 1.获取项目编码
                    String projectCode = getProjectCode(queryDTO.getDataCodes());

                    //2.获取所有涉及到计算指标，并设置到DTO中
                    List<String> formulaVarList = getFormulaVarList(queryDTO.getDataCodes());
                    TimeQueryDTO timeQueryDTO = new TimeQueryDTO();
                    BeanUtil.copyProperties(queryDTO, timeQueryDTO);
                    timeQueryDTO.setDataCodes(formulaVarList);

                    // 2. 指标分类 (10ms)
                    stopWatch.start("指标分类");
                    IndicatorClassification classification = classifyIndicators(timeQueryDTO.getDataCodes(),projectCode);
                    stopWatch.stop();

                    // 3. 创建全局结果容器
                    ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults = new ConcurrentHashMap<>();

                    // 4. 基础指标计算
                    stopWatch.start("基础指标计算");
                    //获取数据源
                    DataSourceVo dataSourceConfig = dataSourceService.getDataSourceConfig(projectCode);
                    // 阶段1：基础指标计算（同时间截面不同设备属性并行计算）
                    if (!classification.getBaseIndicators().isEmpty()) {
                        CompletableFuture<Void> basePhase = baseIndicatorCalculator.calculateBaseIndicators(
                                classification.getBaseIndicators(), null, queryDTO,dataSourceConfig, globalResults);
                        basePhase.join(); // 等待基础指标完成
                    }

                    stopWatch.stop();
                    // 时间断面特殊处理：将查询到的时间替换为目标时间点
                    stopWatch.start("时间断面结果处理");
                    processTimeSliceResults(globalResults, queryDTO.getStartTime());
                    stopWatch.stop();

                    // 返回计算结果和原始时间窗口（用于结果组装），由控制器层进行结果组装和过滤
                    return new CalculationResult(globalResults, null, stopWatch.prettyPrint());

                } catch (Exception e) {
                    log.error("计算异常", e);
                    throw new RuntimeException("计算失败", e);
                }
            });
        }

        /**
         * 处理时间断面结果：将查询到的时间替换为目标时间点
         * 时间断面查询从数据库查询最近15分钟的数据，但需要统一时间戳为目标时间点
         */
        private void processTimeSliceResults(
                ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
                String targetTime) {

            log.debug("开始处理时间断面结果，目标时间点: {}", targetTime);

            globalResults.forEach((indicator, timeValueMap) -> {
                if (timeValueMap != null && !timeValueMap.isEmpty()) {
                    // 获取最近的一个值（通常是最后一包数据）
                    BigDecimal latestValue = null;
                    String latestTime = null;

                    // 找到最新的时间点和对应的值
                    for (Map.Entry<String, BigDecimal> entry : timeValueMap.entrySet()) {
                        if (latestTime == null || entry.getKey().compareTo(latestTime) > 0) {
                            latestTime = entry.getKey();
                            latestValue = entry.getValue();
                        }
                    }

                    // 清空原有数据，用目标时间点重新设置
                    timeValueMap.clear();
                    if (latestValue != null) {
                        timeValueMap.put(targetTime, latestValue);
                        log.debug("指标 {} 时间断面处理: {} -> {} (值: {})",
                                indicator, latestTime, targetTime, latestValue);
                    }
                }
            });

            log.debug("时间断面结果处理完成，统一时间点为: {}", targetTime);
        }

        /**
         * 三阶段计算：基础指标 -> 聚合指标 -> 衍生指标
         *
         * 优化策略：
         * 1. 时间断面计算：只执行基础指标计算，跳过聚合指标和衍生指标
         * 2. 等时间间隔/时间区间：执行完整的三阶段计算
         * 3. 确保时间戳处理的稳定性和精确性
         */
        private void executeThreePhaseCalculation(
                String projectCode,
                IndicatorClassification classification,
                List<TimeWindow> timeWindows,
                TimeQueryDTO queryDTO,
                ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

            //获取数据源
            DataSourceVo dataSourceConfig = dataSourceService.getDataSourceConfig(projectCode);
             if(ObjectUtil.isEmpty(dataSourceConfig)){
                 throw new BizException("数据源配置不存在");
             }
            // 阶段1：基础指标计算（所有计算模式都需要）
            if (!classification.getBaseIndicators().isEmpty()) {
                CompletableFuture<Void> basePhase = baseIndicatorCalculator.calculateBaseIndicators(
                        classification.getBaseIndicators(), timeWindows, queryDTO, dataSourceConfig, globalResults);
                basePhase.join(); // 等待基础指标完成
            }

            // 阶段2：聚合指标计算（仅等时间间隔和时间区间模式）
            if (!classification.getAggregateIndicators().isEmpty()) {
                CompletableFuture<Void> aggPhase = aggIndicatorCalculator.calculateAggregateIndicators(
                        classification.getAggregateIndicators(), timeWindows, queryDTO, dataSourceConfig, globalResults);
                aggPhase.join(); // 等待聚合指标完成
            }

            // 阶段3：衍生指标计算（仅等时间间隔和时间区间模式）
            if (!classification.getDerivedIndicators().isEmpty()) {
                CompletableFuture<Void> derivedPhase = derivedIndicatorCalculator.calculateDerivedIndicators(
                        classification.getDerivedIndicators(), timeWindows, queryDTO, globalResults);
                derivedPhase.join(); // 等待衍生指标完成
            }
        }




        /**
         * 获取公式中涉及的所有变量（包含递归依赖）- 超高性能优化版本
         */
        public List<String> getFormulaVarList(List<String> targetPropList) {
            if (CollUtil.isEmpty(targetPropList)) {
                return Collections.emptyList();
            }

            try {
                // 🔥 使用高性能公式缓存服务（Redis + 内存双重缓存）
                log.info("🚀 使用高性能公式缓存处理{}个指标", targetPropList.size());

                // 🚀 直接使用最优策略：Redis缓存 + 批量查询

                // � 使用智能高性能模式（自动根据数据量选择最优策略）
                return formulaCacheService.getFormulaVarList(targetPropList, ConstantBase.RULECOLID_COMMON);
            } catch (Exception e) {
                log.error("高性能获取公式变量失败，降级到原始方法: {}", e.getMessage(), e);
                // 降级处理：使用原始方法
                return getFormulaVarListFallback(targetPropList);
            }
        }

        /**
         * 降级方法：原始的公式变量获取逻辑
         */
        private List<String> getFormulaVarListFallback(List<String> targetPropList) {
            Set<String> resultSet = new LinkedHashSet<>();
            Set<String> processed = new HashSet<>();

            try {
                StopWatch stopWatch = new StopWatch("获取公式变量-降级版");
                stopWatch.start("处理变量依赖");

                // 使用原始的递归方法
                for (String prop : targetPropList) {
                    processVariableDependenciesOptimized(prop, resultSet, processed, new HashMap<>(), 0);
                }

                stopWatch.stop();
                log.info("降级获取公式变量完成，目标属性数: {}, 总变量数: {}, 耗时统计:\n{}",
                        targetPropList.size(), resultSet.size(), stopWatch.prettyPrint());

                return new ArrayList<>(resultSet);

            } catch (Exception e) {
                log.error("降级获取公式变量失败: {}", e.getMessage(), e);
                return new ArrayList<>(targetPropList);  // 最终降级：直接返回目标属性列表
            }
        }



        /**
         * 使用缓存的公式递归处理变量依赖
         */
        private void processVariableDependenciesOptimized(String propCode, Set<String> resultSet,
                                                         Set<String> processed, Map<String, String> formulaCache, int depth) {
            // 防止递归过深
            if (depth > 100) {
                log.warn("变量依赖递归深度超过限制: {}", propCode);
                return;
            }

            if (!processed.add(propCode)) {
                return;
            }

            resultSet.add(propCode);

            // 从缓存中获取公式，避免数据库查询
            String formula = formulaCache.get(propCode);

            if (StrUtil.isNotBlank(formula)) {
                List<String> variables = FormulaUtils.getVarList(formula);
                if (CollUtil.isNotEmpty(variables)) {
                    for (String var : variables) {
                        if (!processed.contains(var)) {
                            resultSet.add(var);
                            // 递归处理变量依赖
                            processVariableDependenciesOptimized(var, resultSet, processed, formulaCache, depth + 1);
                        }
                    }
                }
            }
        }


        /**
      * <AUTHOR>
      * @Description //指标分类，将指标分为基础指标，聚合指标和衍生指标
      * @Date 18:03 2025/8/8
      * @Param indicators 指标列表
      * @Param projectCode 项目编码
      * @return com.siact.energy.cal.server.core.pojo.IndicatorClassification
      **/


        public IndicatorClassification classifyIndicators(List<String> indicators, String projectCode) {
           IndicatorClassification classification = new IndicatorClassification();

            try {
                Long ruleColID = ConstantBase.RULECOLID_COMMON;

                // 🔥【修改】使用通用缓存模板获取计算类型配置
                String redisKey = "calTypeConfig:" + projectCode + ":" + ruleColID;
                String sourceKey = projectCode; // 源头加载只需要 projectCode
                // 获取计算类型配置
//                Map<Integer, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ConstantBase.RULECOLID_COMMON);
              // 缓存 L1 -> L2 -> DB 的完整逻辑
                Map<String, List<String>> calTypeConfigMap = cacheTemplate.get(
                        calTypeConfigCache,
                        redisKey,
                        sourceKey,
                        Duration.ofHours(1),
                        (key) -> { // 定义源头加载逻辑 (SourceLoader)
                            log.info("缓存未命中，从数据库加载项目 {} 的计算类型配置...", key);
                            return ruleDetailInstanceService.getCalTypeConfigMap(key, ruleColID);
                        }
                );
                // 按计算类型分组
                Map<String, List<String>> calTypePropMap = groupDevpropertyByCalType(indicators, calTypeConfigMap);

                // 设置分类结果
                classification.setBaseIndicators(
                        calTypePropMap.getOrDefault(ConstantBase.ORI_BASE_PROP, new ArrayList<>()));
                classification.setAggregateIndicators(
                        calTypePropMap.getOrDefault(ConstantBase.CAL_AGG, new ArrayList<>()));
                classification.setDerivedIndicators(
                        calTypePropMap.getOrDefault(ConstantBase.CAL_DER, new ArrayList<>()));

                log.info("指标分类完成: 项目={}, 基础指标={}, 聚合指标={}, 衍生指标={}",
                        projectCode,
                        classification.getBaseIndicators().size(),
                        classification.getAggregateIndicators().size(),
                        classification.getDerivedIndicators().size());

            } catch (Exception e) {
                log.error("指标分类失败", e);
                // 降级处理：将所有指标归类为基础指标
                classification.setBaseIndicators(new ArrayList<>(indicators));
            }

            return classification;
        }

        /*
         * <AUTHOR>
         * @Description //将设备属性列表按照计算类型分组,将采集属性/基础指标属性与聚合指标/衍生指标区分开，前者从数据库中查询，后者基于前者进行计算
         * @Date 9:07 2024/8/5
         * @Param
         * @return
         **/

        public Map<String, List<String>> groupDevpropertyByCalType(List<String> devpropList, Map<String, List<String>> calTypeMap) {
            Map<String, List<String>> groupedMap = new HashMap<>();
            //获取计算类型是2(聚合指标)和3(衍生指标)devpropertyList列表
            List<String> aggList = calTypeMap.getOrDefault(ConstantBase.CAL_AGGREGATION_TYPE, new ArrayList<>());
            List<String> derivedList = calTypeMap.getOrDefault(ConstantBase.CAL_DERIVED_TYPE, new ArrayList<>());
            List<String> baseList = calTypeMap.getOrDefault(ConstantBase.CAL_BASE_TYPE, new ArrayList<>());
            for (String propCode : devpropList) {
                if (aggList.contains(propCode)) {
                    groupedMap.computeIfAbsent(ConstantBase.CAL_AGG, k -> new ArrayList<>()).add(propCode);
                } else if (derivedList.contains(propCode)) {
                    groupedMap.computeIfAbsent(ConstantBase.CAL_DER, k -> new ArrayList<>()).add(propCode);
                } else if (baseList.contains(propCode)) {
                    groupedMap.computeIfAbsent(ConstantBase.BASE_PROP, k -> new ArrayList<>()).add(propCode);
                    groupedMap.computeIfAbsent(ConstantBase.ORI_BASE_PROP, k -> new ArrayList<>()).add(propCode);
                } else {
                    groupedMap.computeIfAbsent(ConstantBase.ORI_BASE_PROP, k -> new ArrayList<>()).add(propCode);
                }
            }
            return groupedMap;
        }

        /**
         * <AUTHOR>
         * @Description //获取项目编码
         * @Date 17:54 2025/8/8
         * @Param indicators: 指标列表
         * @return java.lang.String
         **/

        public String getProjectCode(List<String> indicators) {
            if (indicators == null || indicators.isEmpty()) {
                throw new BizException("指标列表不能为空");
            }
            // 从第一个指标中提取项目编码，作为统一的项目编码
            return extractProjectCode(indicators.get(0));
        }

        /**
         * <AUTHOR>
         * @Description //从指标中提取项目编码
         * @Date 17:55 2025/8/8
         * @Param indicator:指标编码
         * @return java.lang.String
         **/

        private String extractProjectCode(String indicator) {
            if (indicator == null || indicator.isEmpty()) {
                throw new BizException("指标编码不能为空");
            }
            return InsCodeUtil.getProjectDataCode(indicator);
        }


    }
