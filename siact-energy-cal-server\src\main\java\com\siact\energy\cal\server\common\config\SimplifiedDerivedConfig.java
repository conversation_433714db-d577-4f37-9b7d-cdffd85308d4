package com.siact.energy.cal.server.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 🔥 精简版衍生指标配置
 */
@Component
@ConfigurationProperties(prefix = "siact.energy.derived")
@Data
public class SimplifiedDerivedConfig {
    
    /**
     * 高性能模式触发的最小指标数量
     */
    private int highPerformanceModeThreshold = 50;
    
    /**
     * 高性能模式触发的最小时间窗口数量
     */
    private int highPerformanceTimeWindowThreshold = 100;
    
    /**
     * 高频查询的最大间隔（分钟）
     */
    private int highFrequencyMaxInterval = 5;
    
    /**
     * 启用高性能模式的最小可用内存（MB）
     */
    private long minAvailableMemoryMB = 500;
    
    /**
     * 启用高性能模式的最小CPU核心数
     */
    private int minCpuCores = 4;
    
    /**
     * 低复杂度指标的批次大小
     */
    private int lowComplexityBatchSize = 100;
    
    /**
     * 中等复杂度指标的批次大小
     */
    private int mediumComplexityBatchSize = 50;
    
    /**
     * 高复杂度指标的批次大小
     */
    private int highComplexityBatchSize = 20;
    
    /**
     * 最大并发线程数
     */
    private int maxConcurrentThreads = 32;
    
    /**
     * 是否启用性能监控
     */
    private boolean enablePerformanceMonitoring = true;
    
    /**
     * 慢查询阈值（毫秒）
     */
    private long slowQueryThresholdMs = 5000;
    
    /**
     * 检查是否应该启用高性能模式
     */
    public boolean shouldEnableHighPerformanceMode(int indicatorCount, int timeWindowCount, 
                                                  Integer queryInterval, String tsUnit) {
        
        // 检查指标数量
        boolean hasLargeIndicatorCount = indicatorCount >= highPerformanceModeThreshold;
        
        // 检查时间窗口数量
        boolean hasLongTimeSpan = timeWindowCount >= highPerformanceTimeWindowThreshold;
        
        // 检查查询频率
        boolean isHighFrequency = false;
        if (queryInterval != null && tsUnit != null) {
            int intervalInMinutes = convertToMinutes(queryInterval, tsUnit);
            isHighFrequency = intervalInMinutes <= highFrequencyMaxInterval;
        }
        
        // 检查系统资源
        boolean hasGoodSystemResources = checkSystemResources();
        
        return (hasLargeIndicatorCount || hasLongTimeSpan || isHighFrequency) && hasGoodSystemResources;
    }
    
    /**
     * 检查系统资源是否充足
     */
    public boolean checkSystemResources() {
        // 检查CPU核心数
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        boolean hasSufficientCpu = availableProcessors >= minCpuCores;
        
        // 检查可用内存
        long freeMemoryMB = Runtime.getRuntime().freeMemory() / 1024 / 1024;
        boolean hasSufficientMemory = freeMemoryMB >= minAvailableMemoryMB;
        
        return hasSufficientCpu && hasSufficientMemory;
    }
    
    /**
     * 根据复杂度获取批次大小
     */
    public int getBatchSizeByComplexity(String complexity) {
        switch (complexity.toUpperCase()) {
            case "LOW":
                return lowComplexityBatchSize;
            case "MEDIUM":
                return mediumComplexityBatchSize;
            case "HIGH":
                return highComplexityBatchSize;
            default:
                return mediumComplexityBatchSize;
        }
    }
    
    /**
     * 获取当前系统内存使用率
     */
    public double getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        return (double) usedMemory / totalMemory * 100;
    }
    
    /**
     * 获取推荐的线程池大小
     */
    public int getRecommendedThreadPoolSize() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        double memoryUsage = getCurrentMemoryUsage();
        
        // 根据内存使用情况调整线程池大小
        if (memoryUsage >= 90.0) {
            return Math.max(2, cpuCores / 2); // 内存紧张时减少线程数
        } else if (memoryUsage >= 80.0) {
            return cpuCores; // 内存警告时使用CPU核心数
        } else {
            return Math.min(maxConcurrentThreads, cpuCores * 2); // 内存充足时可以超配
        }
    }
    
    /**
     * 将时间间隔转换为分钟
     */
    private int convertToMinutes(int interval, String tsUnit) {
        switch (tsUnit.toLowerCase()) {
            case "m": return interval;
            case "h": return interval * 60;
            case "d": return interval * 24 * 60;
            case "n": return interval * 30 * 24 * 60; // 月
            case "y": return interval * 365 * 24 * 60; // 年
            default: return interval;
        }
    }
}
