package com.siact.energy.cal.server.common.flow.job;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.MapToDbUtils;
import com.siact.energy.cal.server.common.flow.init.TaoSnit;
import com.siact.energy.cal.server.entity.flow.ComponentFlowEntity;
import com.siact.energy.cal.server.service.flow.IFlowTableService;
import com.siact.energy.cal.server.service.flow.IFlowViewService;
import com.siact.energy.cal.server.service.flow.IProcessService;
import com.siact.energy.cal.server.xxljob.context.XxlJobHelper;
import com.siact.energy.cal.server.xxljob.handler.annotation.XxlJob;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-06
 * @Description:
 * @Version: 1.0
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "xxl.job", name = "enabled", havingValue = "true", matchIfMissing = false)
public class JobHandler {

    @Resource
    IProcessService processService;

    @Resource
    HikariDataSource hikariDataSource;

    @Resource
    IFlowViewService flowViewService;



    @Resource
    TaoSnit taoSnit;

    @Resource
    private Environment environment;

    @XxlJob("deleteViewJobHandler")
    public void deleteViewJobHandler(){
        if (!isXxlJobEnabled()) {
            return;
        }
        log.info("删除视图开始");
        TimeInterval timer = DateUtil.timer();
        List<String> viewNames = flowViewService.selectViewNames();
        if (!viewNames.isEmpty()){
            log.info("需要删除视图个数为：{}", viewNames.size());
            //删除视图
            flowViewService.deleteViews(viewNames);
            //删除关系表中的数据
            flowViewService.deleteViewName(viewNames);
        }
        log.info("删除视图成功：{}", timer.interval());
    }

    @XxlJob("deleteTablesJobHandler")
    public void deleteTablesJobHandler(){
        if (!isXxlJobEnabled()) {
            return;
        }
        log.info("删除临时表开始");
        TimeInterval timer = DateUtil.timer();
        List<String> tableNames = flowViewService.selectTableNames();

        if (!tableNames.isEmpty()){
            log.info("需要删除临时表个数为：{}", tableNames.size());
            //删除临时表
            try {
                flowViewService.deleteTables(tableNames);
                //删除关系表中的数据
                flowViewService.deleteTables(tableNames);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        log.info("删除临时表成功：{}", timer.interval());
    }


    @XxlJob("pullDataJobHandler")
    public void pullDataJobHandler(){
        String today = DateUtil.today().replaceAll("-", "");
        //获取要拉取的公式字段
        Set<String> fileds = taoSnit.idAndProperty.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());

        StringBuilder sqlIn = new StringBuilder();
        String tableId = "taoS_data_"+today;
        createTale(tableId ,fileds);
        for (String field : fileds) {
            sqlIn.append("'").append(field).append("',");
        }
        StringBuilder sql = sqlIn.deleteCharAt(sqlIn.lastIndexOf(","));

        log.info("拉取taoS数据库数据");
        Connection con = DBConnection.connection(DbTypeEnum.TaoS.getDbType(),"121.36.3.28", "29001", "test","root", "taosdata");

        StringBuilder sb = new StringBuilder();
        sb.append("select ts, itemvalue, devproperty from test.xych where devproperty in (").append(sql).append(" ) limit 500000");
        try {
            TimeInterval timer = DateUtil.timer();
            ResultSet rs = DBTools.executeQuerySql(con, sb.toString());
            List<Map<String, String>> resultList = new ArrayList<>();
            while (true) {
                assert rs != null;
                if (!rs.next()) break;    // 判断是否还有下一个数据
                HashMap<String, String> map = new HashMap<>();
                // 根据字段名获取相应的值
                String code = rs.getString("devproperty");
                String itemValue = rs.getString("itemvalue");
                map.put(code, itemValue);
                String ts = rs.getString("ts");
                map.put("ts", ts);
                //输出查到的记录的各个字段的值
                resultList.add(map);
            }
            MapToDbUtils.mapToDb(resultList, tableId);
            log.info("共执行{}条记录，耗时{}", resultList.size(), timer.interval());
        }catch (Exception e) {
            log.error("查询失败:{}", e.getMessage(), e);
        }finally {
            try {
                assert con != null;
                con.close();
            } catch (SQLException e) {
                log.error("关闭数据库失败，{}", e.getMessage(), e);
            }
        }
    }


    private void createTale(String tableId, Set<String> fields){
        StringBuilder sb = new StringBuilder();
        sb.append("create table ").append(tableId).append("( ts varchar(255) ,");
        for (String field : fields) {
            field = field.replaceAll("]", "").replaceAll("\"", "");
            sb.append(field).append(" varchar(255) ,");
        }
        sb.deleteCharAt(sb.lastIndexOf(","));
        sb.append(")");
        Connection connection = null;
        try {
            connection = hikariDataSource.getConnection();
            DBTools.executeSql(connection, sb.toString());
        }catch (Exception e){
            log.error("sql执行失败", e);
            throw new BizException("sql执行失败");
        }finally {
            DBConnection.close(connection);
        }
    }

    private boolean isXxlJobEnabled() {
        return Boolean.TRUE.equals(environment.getProperty("xxl.job.enabled", Boolean.class, false));
    }
}
