package com.siact.energy.cal.server.xxljob.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobGroupDao;
import com.siact.energy.cal.server.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.server.xxljob.controller.annotation.PermissionLimit;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobGroup;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobRegistry;
import com.siact.energy.cal.server.xxljob.core.util.I18nUtil;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobInfoDao;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobRegistryDao;
import com.siact.energy.cal.server.xxljob.enums.RegistryConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-01
 * @Description: 执行器管理
 * @Version: 1.0
 */
@Controller
@RequestMapping("/jobgroup")
@ApiSort(100)
@Api(tags = {"执行器管理"})
public class JobGroupController {

	@Resource
	public XxlJobGroupDao xxlJobGroupDao;

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	@ApiOperation("保存执行器")
	@ApiOperationSupport(order = 10)
	@ResponseBody
	public ReturnT<String> save(XxlJobGroup xxlJobGroup){
		// process
		xxlJobGroup.setUpdateTime(new Date());

		int ret = xxlJobGroupDao.save(xxlJobGroup);
		return (ret>0)?ReturnT.SUCCESS:ReturnT.FAIL;
	}

	@RequestMapping(value = "/update", method = RequestMethod.POST)
	@ApiOperation("更新执行器")
	@ApiOperationSupport(order = 20)
	@ResponseBody
	public ReturnT<String> update(XxlJobGroup xxlJobGroup){
		xxlJobGroup.setUpdateTime(new Date());
		int ret = xxlJobGroupDao.update(xxlJobGroup);
		return (ret>0)?ReturnT.SUCCESS:ReturnT.FAIL;
	}

	@RequestMapping(value = "/remove", method = RequestMethod.POST)
	@ApiOperation("删除执行器")
	@ApiOperationSupport(order = 30)
	@ResponseBody
	public ReturnT<String> remove(int id){
		return (xxlJobGroupDao.remove(id)>0)?ReturnT.SUCCESS:ReturnT.FAIL;
	}

	@RequestMapping(value = "/loadById", method = RequestMethod.GET)
	@ApiOperation("获取执行器")
	@ApiOperationSupport(order = 40)
	@ResponseBody
	public ReturnT<XxlJobGroup> loadById(int id){
		XxlJobGroup jobGroup = xxlJobGroupDao.load(id);
		return jobGroup!=null?new ReturnT<XxlJobGroup>(jobGroup):new ReturnT<XxlJobGroup>(ReturnT.FAIL_CODE, null);
	}

}
