package com.siact.energy.cal.server.controller.ruleCol.flow;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ApiConfigDto;
import com.siact.energy.cal.common.pojo.vo.flow.ApiConfigVO;
import com.siact.energy.cal.server.service.flow.IApiConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-11
 * @Description: api接口配置
 * @Version: 1.0
 */
@RestController
@RequestMapping("/api/config")
@ApiSort(130)
@Api(tags = "api接口配置")
public class ApiConfigController {


    @Resource
    private IApiConfigService apiConfigService;
    @RequestMapping(value = "/addModifyApiConfig", method = RequestMethod.POST)
    @ApiOperation("添加或修改api接口配置")
    public R<String> addModifyApiConfig(@RequestBody ApiConfigDto apiConfigDto){
        return R.OK(apiConfigService.addModifyApiConfig(apiConfigDto));
    }

    @RequestMapping(value = "/deleteApiConfig/{id}", method = RequestMethod.GET)
    @ApiOperation("删除api接口配置")
    public R<String> deleteApiConfig(@PathVariable String id){
        return R.OK(apiConfigService.deleteApiConfig(id));
    }

    @RequestMapping(value = "/getApiConfig/{id}", method = RequestMethod.GET)
    @ApiOperation("获取api接口配置")
    public R<ApiConfigVO> getApiConfig(@PathVariable String id){
        return R.OK(apiConfigService.getApiConfig(id));
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation("获取api接口配置列表")
    public R<List<ApiConfigVO>> getApiConfigList(PageBean<ApiConfigVO> page, @RequestBody ApiConfigDto apiConfigDto) {
        page.setSize(3);
        return R.OK(apiConfigService.getApiConfigList(page, apiConfigDto));
    }
}
