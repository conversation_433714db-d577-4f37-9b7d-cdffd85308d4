package com.siact.energy.cal.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 高性能计算框架配置类
 */
@Configuration
@ComponentScan(basePackages = "com.siact.energy.cal.server.service.energycal")
@Slf4j
public class HighPerformanceConfig {

    public HighPerformanceConfig() {
        log.info("高性能计算框架配置已加载");
    }
}
