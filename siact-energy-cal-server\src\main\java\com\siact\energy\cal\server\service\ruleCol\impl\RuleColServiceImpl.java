package com.siact.energy.cal.server.service.ruleCol.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColUpdateDTO;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import com.siact.energy.cal.common.pojo.vo.ruleCol.RuleColVO;
import com.siact.energy.cal.common.util.utils.ClassUtil;
import com.siact.energy.cal.common.util.utils.CommonUtils;
import com.siact.energy.cal.common.util.utils.HttpClientUtil;
import com.siact.energy.cal.server.convertor.ruleCol.RuleColConvertor;
import com.siact.energy.cal.server.dao.ruleCol.RuleColDao;
import com.siact.energy.cal.server.entity.ruleCol.RuleCol;
import com.siact.energy.cal.server.service.ruleCol.RuleColService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * 指标集表(RuleCol)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:13
 */
@Service("ruleColService")
public class RuleColServiceImpl extends ServiceImpl<RuleColDao, RuleCol> implements RuleColService {

  /*  @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;*/

    @Override
    public PageBean<RuleColVO> listPage(PageBean<RuleColVO> page, RuleColQueryDTO ruleColQueryDTO) {
        return page(page, ruleColQueryDTO);
    }

    @Override
    public RuleColVO getVoById(Serializable id) {
        RuleCol ruleCol = getById(id);
        return RuleColConvertor.INSTANCE.entity2Vo(ruleCol);
    }

    @Override
    public Boolean save(RuleColInsertDTO ruleColInsertDTO) {
        return save(RuleColConvertor.INSTANCE.insertDTO2Entity(ruleColInsertDTO));
    }

    @Override
    public Boolean updateVoById(RuleColUpdateDTO ruleColUpdateDTO) {
       return updateById(RuleColConvertor.INSTANCE.updateDTO2Entity(ruleColUpdateDTO));
    }

    @Override
    public boolean toggle(Integer activeState, List<Long> ids) {

        LambdaUpdateWrapper<RuleCol> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(RuleCol::getId, ids)
                .set(RuleCol::getActiveState, activeState);

        return update(updateWrapper);

    }

    @Override
    public List<RuleColVO> listAll() {
        // 创建查询条件，只获取激活状态的指标集
        LambdaQueryWrapper<RuleCol> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleCol::getActiveState, 0); // 0-激活,1-未激活
        // 获取所有激活的指标集并转换为VO
        return RuleColConvertor.INSTANCE.entities2Vos(list(queryWrapper));
    }

    @Override
    public void updateAttrList(Long ruleColId, List<AttrObject> list) {
        RuleCol ruleCol = getById(ruleColId);
        if(ObjectUtil.isNotEmpty(ruleCol)){
            List<AttrObject> attrList = ruleCol.getAttrList();
            if(ObjectUtil.isNotEmpty(attrList)){
                list.addAll(attrList);
            }
        }

        HashMap<String, AttrObject> map = new HashMap<>();
        for (AttrObject attrObject : list) {
            map.put(attrObject.getAttrCode(), attrObject);
        }
        List<AttrObject> attrObjects = new ArrayList<>();
        map.values().forEach(attrObjects::add);
        ruleCol.setAttrList(attrObjects);
        updateById(ruleCol);
    }


    /**
     * 分页查询
     *
     * @param page            分页对象
     * @param ruleColQueryDTO 查询实体
     * @return 分页数据
     */
    private PageBean<RuleColVO> page(PageBean<RuleColVO> page, RuleColQueryDTO ruleColQueryDTO) {

        // 转换器
        RuleColConvertor convertor = RuleColConvertor.INSTANCE;
        // VO转实体
        RuleCol ruleCol = convertor.queryDTO2Entity(ruleColQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<RuleCol> queryWrapper = new LambdaQueryWrapper<>(ruleCol);

        List<String> voFieldNameList = ClassUtil.getClassAllFields(RuleColVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<RuleCol> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        PageBean<RuleColVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);

        return voPageBean;
    }

}

