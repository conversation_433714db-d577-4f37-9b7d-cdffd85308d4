package com.siact.energy.cal.server.common.datasource.db.impl;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.concurrent.TimeUnit;

/**
 * 动态数据源连接池管理器
 * <p>
 * 负责根据运行时的数据源配置，动态地创建、缓存和销毁 HikariCP 连接池。
 */
@Component
@Slf4j
public class DynamicDataSourceManager {

    // 🔥 使用 Caffeine 缓存 DataSource (连接池) 实例
    private final LoadingCache<String, HikariDataSource> dataSourceCache;

    public DynamicDataSourceManager() {
        this.dataSourceCache = Caffeine.newBuilder()
                .maximumSize(100) // 最多缓存100个不同数据源的连接池
                .expireAfterAccess(60, TimeUnit.MINUTES) // 连接池如果1小时内未被访问，则关闭并移除
                .removalListener((RemovalListener<String, HikariDataSource>) (key, dataSource, cause) -> {
                    // 当连接池实例从缓存中被移除时，确保其被安全关闭
                    if (dataSource != null && !dataSource.isClosed()) {
                        log.info("关闭到数据源 [{}] 的空闲连接池。移除原因: {}", key, cause);
                        dataSource.close();
                    }
                })
                .build(this::createHikariDataSource); // 当缓存未命中时，调用此方法创建新的连接池
    }

    /**
     * 根据数据源配置，获取一个可用的连接池 (DataSource)。
     *
     * @param dataSourceVo 数据源的配置信息
     * @return 一个 HikariDataSource 连接池实例
     */
    public DataSource getDataSource(DataSourceVo dataSourceVo) {
        // 使用数据源的关键信息构成唯一 key
        String cacheKey = buildCacheKey(dataSourceVo);
        return dataSourceCache.get(cacheKey, key -> createHikariDataSource(dataSourceVo));
    }

    /**
     * Caffeine 的 CacheLoader 实现，用于创建新的 HikariDataSource 实例。
     * @param dataSourceVo 数据源配置
     * @return 新创建的 HikariDataSource 实例
     */
    private HikariDataSource createHikariDataSource(DataSourceVo dataSourceVo) {
        log.info("为数据源 [{}] 创建一个新的 HikariCP 连接池...", buildCacheKey(dataSourceVo));
        HikariDataSource dataSource = new HikariDataSource();
        
        // 根据数据库类型设置 Driver 和 JDBC URL
        // (您需要根据您的 DBATypeEnum 来完善这部分)
        if (isTdengine(dataSourceVo)) {
            dataSource.setDriverClassName("com.taosdata.jdbc.rs.RestfulDriver");
            String url = String.format("jdbc:TAOS-RS://%s:%s/%s",
                dataSourceVo.getDatabaseIp(), dataSourceVo.getDatabasePort(), dataSourceVo.getDb());
            dataSource.setJdbcUrl(url);
        } else if (isInfluxDB(dataSourceVo)) {
            // 注意：InfluxDB 1.x 通常不使用JDBC连接池，而是我们之前讨论的 OkHttp 连接池。
            // 这个管理器主要适用于JDBC协议的数据库。
        } // ... 其他数据库类型
        
        dataSource.setUsername(dataSourceVo.getUserName());
        dataSource.setPassword(dataSourceVo.getPassword());

        // --- 关键的连接池配置 ---
        dataSource.setMaximumPoolSize(20); // 每个动态数据源的连接池大小
        dataSource.setMinimumIdle(2);
        dataSource.setConnectionTimeout(30000); // 30秒
        dataSource.setIdleTimeout(600000); // 10分钟
        dataSource.setMaxLifetime(1800000); // 30分钟
        dataSource.setPoolName("HikariPool-" + dataSourceVo.getProjectCode());

        return dataSource;
    }

    // Caffeine 加载器需要一个接收 key 的方法重载
    private HikariDataSource createHikariDataSource(String key) {
        // 这个方法理论上不应该被直接调用，因为我们总是通过 get(key, mappingFunction) 提供完整的 dataSourceVo
        // 这是一个兜底实现
        log.warn("正在通过 key [{}] 创建数据源，信息可能不完整！", key);
        DataSourceVo vo = parseKeyToDataSourceVo(key);
        return createHikariDataSource(vo);
    }
    
    private String buildCacheKey(DataSourceVo vo) {
        return String.format("%s:%s:%s", vo.getDatabaseIp(), vo.getDatabasePort(), vo.getDb());
    }
    
    // 假设的辅助方法
    private boolean isTdengine(DataSourceVo vo) { return vo.getDbType() == 0;/* 假设0是TDengine */ }
    private boolean isInfluxDB(DataSourceVo vo) { return vo.getDbType() == 1; /* 假设1是InfluxDB */ }
    private DataSourceVo parseKeyToDataSourceVo(String key) { /* ... 实现从key反解析出vo ... */ return new DataSourceVo(); }
}