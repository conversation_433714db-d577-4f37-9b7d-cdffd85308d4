package com.siact.energy.cal.server.common.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.siact.energy.cal.server.common.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用多级缓存模板 (Caffeine + Redis)
 * <p>
 * 封装了 "L1(Caffeine) -> L2(Redis) -> Source(DB/API)" 的标准查询链路，
 * 并处理了缓存回填和空值缓存防止穿透的逻辑。
 */
@Component
@Slf4j
public class MultiLevelCacheTemplate {

    @Autowired
    @Qualifier(RedisConfig.CUSTOM_REDIS_TEMPLATE_NAME)
    private RedisTemplate<String, Object> redisTemplate;

    // 用于在缓存中表示一个null值，防止缓存穿透
    private static final String NULL_VALUE_REPRESENTATION = "CACHE_NULL_VALUE";

    /**
     * 【核心】获取单个缓存项的通用模板方法。
     *
     * @param caffeineCache Caffeine缓存实例
     * @param redisKey      在Redis中的完整Key
     * @param sourceKey     用于从源头加载数据的Key
     * @param redisExpire   Redis过期时间
     * @param sourceLoader  源头数据加载器 (Lambda表达式)
     * @param <K>           源头Key的类型
     * @param <V>           Value的类型
     * @return 获取到的值，如果源头也不存在，则返回null
     */
    @SuppressWarnings("unchecked")
    public <K, V> V get(Cache<String, V> caffeineCache,
                        String redisKey,
                        K sourceKey,
                        Duration redisExpire,
                        MultiLevelCacheInterfaces.SourceLoader<K, V> sourceLoader) {

        // 1. 从L1 Caffeine获取
        V value = caffeineCache.getIfPresent(redisKey);
        if (value != null) {
            log.trace("L1 Cache Hit: key={}", redisKey);
            return unwrapNullValue(value);
        }

        // 2. 从L2 Redis获取
        try {
            Object redisValue = redisTemplate.opsForValue().get(redisKey);
            if (redisValue != null) {
                log.trace("L2 Cache Hit: key={}", redisKey);
                V typedValue = (V) redisValue;
                caffeineCache.put(redisKey, typedValue); // 回填L1
                return unwrapNullValue(typedValue);
            }
        } catch (Exception e) {
            log.warn("从Redis获取缓存失败, key={}, error: {}", redisKey, e.getMessage());
        }

        // 3. L1和L2都未命中，从源头加载
        log.trace("Cache Miss, loading from source for key: {}", sourceKey);
        V sourceValue = sourceLoader.load(sourceKey);

        // 4. 回填两级缓存
        V valueToCache = wrapNullValue(sourceValue);
        caffeineCache.put(redisKey, valueToCache);
        try {
            redisTemplate.opsForValue().set(redisKey, valueToCache, redisExpire);
        } catch (Exception e) {
            log.warn("回填Redis缓存失败, key={}, error: {}", e.getMessage());
        }

        return sourceValue;
    }

    /**
     * 【核心】批量获取缓存项的通用模板方法。
     *
     * @param caffeineCache     Caffeine缓存实例
     * @param keyMapper         一个函数，用于将源头Key映射到Redis Key
     * @param sourceKeys        需要查询的一批源头Key
     * @param redisExpire       Redis过期时间
     * @param batchSourceLoader 源头数据批量加载器 (Lambda表达式)
     * @param <K>               源头Key的类型
     * @param <V>               Value的类型
     * @return 一个Map，包含所有成功获取到的Key-Value对
     */
    @SuppressWarnings("unchecked")
    public <K, V> Map<K, V> getAll(Cache<String, V> caffeineCache,
                                   Function<K, String> keyMapper,
                                   Collection<K> sourceKeys,
                                   Duration redisExpire,
                                   MultiLevelCacheInterfaces.BatchSourceLoader<K, V> batchSourceLoader) {

        if (CollectionUtils.isEmpty(sourceKeys)) {
            return Collections.emptyMap();
        }

        Map<K, V> result = new HashMap<>();
        Map<String, K> redisKeyToSourceKey = sourceKeys.stream()
                .distinct()
                .collect(Collectors.toMap(keyMapper, Function.identity(), (k1, k2) -> k1));
        
        // 1. 从L1 Caffeine批量获取
        Set<String> redisKeys = redisKeyToSourceKey.keySet();
        Map<String, V> caffeineResult = caffeineCache.getAllPresent(redisKeys);
        caffeineResult.forEach((redisKey, value) -> {
            K sourceKey = redisKeyToSourceKey.get(redisKey);
            result.put(sourceKey, unwrapNullValue(value));
        });
        log.trace("L1 Cache Bulk Hit: {}/{} items", caffeineResult.size(), sourceKeys.size());
        
        Set<String> uncachedRedisKeys = new HashSet<>(redisKeys);
        uncachedRedisKeys.removeAll(caffeineResult.keySet());

        if (uncachedRedisKeys.isEmpty()) {
            return result;
        }

        // 2. 从L2 Redis批量获取
        List<String> dbQueryRedisKeys = new ArrayList<>();
        try {
            List<Object> redisValues = redisTemplate.opsForValue().multiGet(uncachedRedisKeys);
            Map<String, V> redisResultToBackfill = new HashMap<>();
            int index = 0;
            for (String redisKey : uncachedRedisKeys) {
                Object redisValue = (redisValues != null && index < redisValues.size()) ? redisValues.get(index++) : null;
                if (redisValue != null) {
                    V typedValue = (V) redisValue;
                    result.put(redisKeyToSourceKey.get(redisKey), unwrapNullValue(typedValue));
                    redisResultToBackfill.put(redisKey, typedValue);
                } else {
                    dbQueryRedisKeys.add(redisKey);
                }
            }
            if(!redisResultToBackfill.isEmpty()){
                caffeineCache.putAll(redisResultToBackfill); // 回填L1
            }
            log.trace("L2 Cache Bulk Hit: {}/{} items", redisResultToBackfill.size(), uncachedRedisKeys.size());
        } catch (Exception e) {
            log.warn("从Redis批量获取缓存失败, error: {}", e.getMessage());
            dbQueryRedisKeys.addAll(uncachedRedisKeys); // Redis失败，全部查询源头
        }

        if (dbQueryRedisKeys.isEmpty()) {
            return result;
        }

        // 3. 从源头批量加载
        Collection<K> sourceKeysToLoad = dbQueryRedisKeys.stream()
                .map(redisKeyToSourceKey::get).collect(Collectors.toList());
        log.trace("Cache Miss, loading {} items from source", sourceKeysToLoad.size());
        Map<K, V> sourceResult = batchSourceLoader.load(sourceKeysToLoad);

        // 4. 回填两级缓存
        Map<String, V> redisBatchToSet = new HashMap<>();
        dbQueryRedisKeys.forEach(redisKey -> {
            K sourceKey = redisKeyToSourceKey.get(redisKey);
            V value = sourceResult.get(sourceKey);
            if(value != null){
                result.put(sourceKey, value);
            }
            
            V valueToCache = wrapNullValue(value);
            caffeineCache.put(redisKey, valueToCache);
            redisBatchToSet.put(redisKey, valueToCache);
        });
        
        if (!redisBatchToSet.isEmpty()) {
            try {
                redisTemplate.opsForValue().multiSet(redisBatchToSet);
                // 这里可以添加逻辑为每个key设置过期时间，如果需要的话
            } catch (Exception e) {
                log.warn("回填Redis缓存失败, error: {}", e.getMessage());
            }
        }
        return result;
    }

    private <V> V wrapNullValue(V value) {
        return value == null ? (V) NULL_VALUE_REPRESENTATION : value;
    }

    @SuppressWarnings("unchecked")
    private <V> V unwrapNullValue(V value) {
        return NULL_VALUE_REPRESENTATION.equals(value) ? null : value;
    }
}