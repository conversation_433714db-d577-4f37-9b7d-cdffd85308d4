package com.siact.energy.cal.server.dao.dataProject;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.siact.energy.cal.server.entity.dataProject.DataProject;

/**
 * 项目表(DataProject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-14 15:27:00
 */
@Repository
public interface DataProjectDao extends BaseMapper<DataProject> {

    /**
     * 批量新增数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DataProject> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<DataProject> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param projectCode 项目编码
     * @return DataProject :项目实体类
     */
    DataProject selectByProjectCode(String projectCode);

    List<DataProject> selectListWithDeleted();


}

