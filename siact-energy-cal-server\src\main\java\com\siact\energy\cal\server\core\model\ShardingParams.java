package com.siact.energy.cal.server.core.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ShardingParams {
    /** 时间分片大小（天数） */
    private int timeShardDays;
    
    /** 指标分片大小 */
    private int metricShardSize;
    
    /** 最大并发数 */
    private int maxConcurrency;
    
    /** 是否需要时间分片 */
    private boolean needsTimeSharding;
    
    /** 是否需要指标分片 */
    private boolean needsMetricSharding;
    
    /** 分片策略类型 */
    private ShardingStrategy strategy;
    
    public enum ShardingStrategy {
        /** 无分片 */
        NO_SHARDING,
        /** 仅时间分片 */
        TIME_ONLY,
        /** 仅指标分片 */
        METRIC_ONLY,
        /** 时间和指标都分片 */
        BOTH,
        /** 自适应分片 */
        ADAPTIVE
    }
}