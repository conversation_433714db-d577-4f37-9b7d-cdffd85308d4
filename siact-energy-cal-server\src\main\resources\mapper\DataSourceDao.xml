<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.dataSource.DataSourceDao">

    <resultMap type="com.siact.energy.cal.server.entity.dataSource.DataSource" id="DataSourceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="databaseName" column="database_name" jdbcType="VARCHAR"/>
        <result property="databaseIp" column="database_ip" jdbcType="VARCHAR"/>
        <result property="databasePort" column="database_port" jdbcType="VARCHAR"/>
        <result property="db" column="db" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="jdbcUrl" column="jdbc_url" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into data_source(database_name, database_ip, database_port, db, status, user_name, password, jdbc_url,
        project_id, creator, create_time, updater, update_time, deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.databaseName}, #{entity.databaseIp}, #{entity.databasePort}, #{entity.db}, #{entity.status},
            #{entity.userName}, #{entity.password}, #{entity.jdbcUrl}, #{entity.projectId}, #{entity.creator},
            #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into data_source(database_name, database_ip, database_port, db, status, user_name, password, jdbc_url,
        project_id, creator, create_time, updater, update_time, deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.databaseName}, #{entity.databaseIp}, #{entity.databasePort}, #{entity.db}, #{entity.status},
            #{entity.userName}, #{entity.password}, #{entity.jdbcUrl}, #{entity.projectId}, #{entity.creator},
            #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
        on duplicate key update
        database_name = values(database_name) , database_ip = values(database_ip) , database_port =
        values(database_port) , db = values(db) , status = values(status) , user_name = values(user_name) , password =
        values(password) , jdbc_url = values(jdbc_url) , project_id = values(project_id) , creator = values(creator) ,
        create_time = values(create_time) , updater = values(updater) , update_time = values(update_time) , deleted =
        values(deleted)
    </insert>

    <select id="listPage" resultType="com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO">
        SELECT
            ds.id,
            ds.database_name,
            ds.database_ip,
            ds.database_port,
            ds.db,
            ds.table_name,
            ds.status,
            ds.user_name,
            ds.password,
            ds.jdbc_url,
            ds.project_id,
            ds.creator,
            ds.create_time,
            dp.project_name
        FROM
            data_source ds
                LEFT JOIN data_project dp ON dp.id = ds.project_id
        WHERE
            ds.deleted = 0
            <if test="query.projectName != null and query.projectName != ''">
                AND dp.project_name LIKE CONCAT('%', #{query.projectName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.databaseName != null and query.databaseName != ''">
                AND ds.database_name LIKE CONCAT('%', #{query.databaseName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.db != null and query.db != ''">
                AND ds.db LIKE CONCAT('%', #{query.db,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.status != null">
                AND ds.status = #{query.status,jdbcType=INTEGER}
            </if>
        ORDER BY ds.create_time DESC
    </select>
    <select id="saveDataSource" resultType="com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo">
        SELECT a.database_name,a.db_type,a.database_ip, a.database_port, a.db, a.table_name, a.user_name,a.password,a.jdbc_url,
               b.project_code FROM data_source a LEFT JOIN data_project b ON a.project_id = b.id
        WHERE a.deleted = 0
    </select>
    <select id="getDataSourceByProjectCode" resultType="com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo">
        SELECT a.database_name,a.db_type,a.database_ip, a.database_port, a.db, a.table_name, a.user_name,a.password,a.jdbc_url,
               b.project_code FROM data_source a LEFT JOIN data_project b ON a.project_id = b.id
        WHERE b.project_code = #{projectCode} and a.deleted = 0
    </select>

</mapper>

