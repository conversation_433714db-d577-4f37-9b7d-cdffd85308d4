package com.siact.energy.cal.server.core.model;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 聚合分片任务
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AggregateShardTask {
    
    /** 聚合函数名称 */
    private String functionName;
    
    /** 指标映射关系 */
    private Map<String, String> metricMapping;
    
    /** 查询DTO */
    private TimeQueryDTO queryDTO;
    
    /** 分片索引 */
    private int shardIndex;
    
    /** 分片描述 */
    private String shardDescription;
}
