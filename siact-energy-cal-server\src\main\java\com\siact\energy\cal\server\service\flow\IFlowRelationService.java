package com.siact.energy.cal.server.service.flow;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import org.apache.poi.ss.formula.functions.T;

public interface IFlowRelationService extends IService<SiComRelationEntity> {

    /**
     * 根据组件流id和节点id查询流程与节点关系
     * @param flowId 组件流id
     * @param nodeId 组件id
     * @return SiComRelationEntity
     */
    SiComRelationEntity getSiComRelationEntity(String flowId, String nodeId);

    <T> T getComponentConfigByRelation(SiComRelationEntity SiComRelationEntity, Class<T> tClass);
}
