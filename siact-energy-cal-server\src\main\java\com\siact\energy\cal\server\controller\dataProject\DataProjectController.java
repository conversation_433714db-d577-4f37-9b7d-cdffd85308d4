package com.siact.energy.cal.server.controller.dataProject;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;

import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotEmpty;

import lombok.RequiredArgsConstructor;

import com.siact.energy.cal.common.core.domain.ResponseCodeConstant;
import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.datasource.common.PageBean;

import com.siact.energy.cal.server.service.dataProject.DataProjectService;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectUpdateDTO;

/**
 * 项目表(DataProject)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-14 15:29:37
 */
@Api(tags = {"项目表"})
@ApiSort(10)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/data/project")
public class DataProjectController {

    private final DataProjectService dataProjectService;

    /**
     * 分页列表
     *
     * @param page                分页对象
     * @param dataProjectQueryDTO 查询实体
     * @return 查询结果
     */
    @ApiOperation(value = "分页列表", hidden = true)
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 10, ignoreParameters = {"total", "pages", "records", "orders", "id"})
    @GetMapping("/listPage")
    public R<PageBean<DataProjectVO>> listPage(PageBean<DataProjectVO> page, DataProjectQueryDTO dataProjectQueryDTO) {
        return R.OK(dataProjectService.listPage(page, dataProjectQueryDTO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    @ApiOperation(value = "通过主键查询单条数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 20)
    @GetMapping("{id}")
    public R<DataProjectVO> selectOne(@PathVariable Serializable id) {
        return R.OK(dataProjectService.getVoById(id));
    }

    /**
     * 新增数据
     *
     * @param dataProjectInsertDTO 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataProjectInsertDTO", value = "实体对象", dataType = "项目表新增DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 30, ignoreParameters = {"id"})
    @PostMapping
    public R<Boolean> insert(@RequestBody @Validated DataProjectInsertDTO dataProjectInsertDTO) {
        return R.OK(dataProjectService.save(dataProjectInsertDTO));
    }

    /**
     * 修改数据
     *
     * @param dataProjectUpdateDTO 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataProjectUpdateDTO", value = "实体对象", dataType = "项目表更新DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 40)
    @PutMapping
    public R<Boolean> update(@RequestBody @Validated(UpdateValidGroup.class) DataProjectUpdateDTO dataProjectUpdateDTO) {
        return R.OK(dataProjectService.updateVoById(dataProjectUpdateDTO));
    }

    /**
     * 删除数据
     *
     * @param ids 主键集合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 50)
    @DeleteMapping
    public R<Boolean> delete(@RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        return R.OK(dataProjectService.removeByIds(ids));
    }

    /**
     * 项目列表(全部)
     *
     * @return 查询结果
     */
    @ApiOperation(value = "项目列表(全部)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 60)
    @GetMapping("/listAll")
    public R<List<DataProjectVO>> listAll() {

        return R.OK(dataProjectService.listAll());
    }

    /**
     * 从数字孪生更新项目
     *
     * @return 更新结果
     */
    @ApiOperation(value = "从数字孪生更新项目", hidden = true)
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @PostMapping("/updateProjectByDigitalTwin")
    public R updateProjectByDigitalTwin() {
        dataProjectService.updateProjectByDigitalTwin();
        return R.OK();
    }

}

