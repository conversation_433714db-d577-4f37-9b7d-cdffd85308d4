package com.siact.energy.cal.server.service.funLib.impl;


import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.siact.energy.cal.common.pojo.dto.funLib.FunLibSelectQueryDTO;
import com.siact.energy.cal.common.pojo.vo.common.FunLibSelectOptionVO;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import com.siact.energy.cal.server.dao.funLib.FunLibDao;
import com.siact.energy.cal.server.entity.funLib.FunLib;
import com.siact.energy.cal.server.service.funLib.FunLibService;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.server.convertor.funLib.FunLibConvertor;
import com.siact.energy.cal.common.pojo.vo.funLib.FunLibVO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibQueryDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibInsertDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibUpdateDTO;
import com.siact.energy.cal.common.util.utils.ClassUtil;
import org.springframework.util.StringUtils;

/**
 * 常用函数库(FunLib)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-11 09:47:21
 */
@Service("funLibService")
public class FunLibServiceImpl extends ServiceImpl<FunLibDao, FunLib> implements FunLibService {

    @Override
    public PageBean<FunLibVO> listPage(PageBean<FunLibVO> page, FunLibQueryDTO funLibQueryDTO) {
        return page(page, funLibQueryDTO);
    }

    @Override
    public FunLibVO getVoById(Serializable id) {
        FunLib funLib = getById(id);
        return FunLibConvertor.INSTANCE.entity2Vo(funLib);
    }

    @Override
    public Boolean save(FunLibInsertDTO funLibInsertDTO) {
        return save(FunLibConvertor.INSTANCE.insertDTO2Entity(funLibInsertDTO));
    }

    @Override
    public Boolean updateVoById(FunLibUpdateDTO funLibUpdateDTO) {
        return updateById(FunLibConvertor.INSTANCE.updateDTO2Entity(funLibUpdateDTO));
    }

    @Override
    public List<FunLibSelectOptionVO> selectList(FunLibSelectQueryDTO dto) {

        LambdaQueryWrapper<FunLib> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCalType()), FunLib::getCalType, dto.getCalType());

        return FunLibConvertor.INSTANCE.entity2SelectVO(list(queryWrapper));
    }

    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param funLibQueryDTO 查询实体
     * @return 分页数据
     */
    private PageBean<FunLibVO> page(PageBean<FunLibVO> page, FunLibQueryDTO funLibQueryDTO) {

        // 转换器
        FunLibConvertor convertor = FunLibConvertor.INSTANCE;
        // VO转实体
        FunLib funLib = convertor.queryDTO2Entity(funLibQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<FunLib> queryWrapper = new LambdaQueryWrapper<>(funLib);

        List<String> voFieldNameList = ClassUtil.getClassAllFields(FunLibVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<FunLib> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        PageBean<FunLibVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);

        return voPageBean;
    }

}

