package com.siact.energy.cal.server.common.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.MapUtils;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Package com.siact.energy.cal.server.common
 * @description: 公式缓存
 * <AUTHOR>
 * @create 2024/12/12 13:56
 */


@Slf4j
@Component
public class FormulaCache {
    @Autowired
    private RedisUtil redisUtil;
/*    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;*/


    /**
     * 公式缓存
     */
    private final LoadingCache<String, String> formulaCache = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .initialCapacity(10000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .recordStats()
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) {
                    String formula = loadFormulaFromRedis(key);
                    return formula != null ? formula : "";
                }
            });
    /**
     * 变量解析缓存
     */
    private final LoadingCache<String, List<String>> variableCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<String, List<String>>() {
                @Override
                public List<String> load(String formula) {
                    return FormulaUtils.getVarList(formula);
                }
            });

    /**
     * 线程池配置
     */
    private final ExecutorService executor = new ThreadPoolExecutor(
            10,
            20,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("formula-process-%d").build()
    );

    public List<String> getFormulaVarList(List<String> targetPropList) {
        if (CollUtil.isEmpty(targetPropList)) {
            return Collections.emptyList();
        }

        Set<String> resultSet = Collections.synchronizedSet(new LinkedHashSet<>());
        Set<String> processed = Collections.synchronizedSet(new HashSet<>());

        try {
            // 1. 首先批量预加载所有目标属性的公式
            StopWatch stopWatch = new StopWatch("获取公式变量");
            stopWatch.start("预加载公式");

            // 批量加载公式到缓存
            Map<String, String> formulaCache = loadFormulasFromSources(
                    targetPropList.stream()
                            .map(this::buildRedisKey)
                            .collect(Collectors.toList())
            );

            stopWatch.stop();
            stopWatch.start("处理变量依赖");

            // 2. 处理变量依赖关系
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                targetPropList.parallelStream().forEach(prop ->
                        processVariableDependencies(prop, resultSet, processed, formulaCache, 0));
            }, executor);

            // 设置超时
            try {
                future.get(5, TimeUnit.MINUTES);
            } catch (TimeoutException e) {
                log.error("处理变量依赖超时", e);
                future.cancel(true);
            }

            stopWatch.stop();
            log.info("获取公式变量完成，目标属性数: {}, 总变量数: {}, 耗时统计:\n{}",
                    targetPropList.size(), resultSet.size(), stopWatch.prettyPrint());

            return new ArrayList<>(resultSet);

        } catch (Exception e) {
            log.error("获取公式变量列表失败: {}", e.getMessage(), e);
            return new ArrayList<>(targetPropList);  // 降级处理：直接返回目标属性列表
        }
    }

    private void processVariableDependencies(String propCode, Set<String> resultSet,
                                             Set<String> processed, Map<String, String> formulaCache,
                                             int depth) {
        // 防止递归过深
        if (depth > 100) {
            log.warn("变量依赖递归深度超过限制: {}", propCode);
            return;
        }

        if (!processed.add(propCode)) {
            return;
        }

        resultSet.add(propCode);

        String redisKey = buildRedisKey(propCode);
        String formula = formulaCache.get(redisKey);  // 直接从预加载的缓存中获取

        if (StrUtil.isNotBlank(formula)) {
            List<String> variables = FormulaUtils.getVarList(formula);
            if (CollUtil.isNotEmpty(variables)) {
                variables.parallelStream().forEach(var -> {
                    String varRedisKey = buildRedisKey(var);
                    if (formulaCache.containsKey(varRedisKey)) {
                        processVariableDependencies(var, resultSet, processed, formulaCache, depth + 1);
                    } else {
                        resultSet.add(var);
                    }
                });
            }
        }
    }

    private Map<String, String> loadFormulasFromSources(List<String> redisKeys) {
        Map<String, String> result = new HashMap<>();
        StopWatch stopWatch = new StopWatch("加载公式");
        stopWatch.start("获取公式");

        try {
            // 1. 串行获取所有数据源的公式，避免并发导致的资源竞争
            Map<String, Map<Object, Object>> allFormulas = new HashMap<>();

            // 使用try-catch分别处理每个数据源，避免一个失败影响全部
            try {
                allFormulas.put("INS", redisUtil.hmget(ConstantBase.INS_REDISKEY));
            } catch (Exception e) {
                log.warn("获取INS公式失败", e);
            }

            try {
                allFormulas.put("MODEL", redisUtil.hmget(ConstantBase.MODEL_REDISKEY));
            } catch (Exception e) {
                log.warn("获取MODEL公式失败", e);
            }

            try {
                allFormulas.put("TWINS", redisUtil.hmget(ConstantBase.TWINS_REDISKEY));
            } catch (Exception e) {
                log.warn("获取TWINS公式失败", e);
            }

            stopWatch.stop();
            stopWatch.start("处理公式");

            // 2. 按优先级处理公式
            String[] priorities = {"INS", "MODEL", "TWINS"};
            for (String redisKey : redisKeys) {
                for (String source : priorities) {
                    Map<Object, Object> formulas = allFormulas.get(source);
                    if (MapUtils.isNotEmpty(formulas) && formulas.containsKey(redisKey)) {
                        try {
                            String formula = processFormulaValue(formulas.get(redisKey));
                            if (StrUtil.isNotBlank(formula)) {
                                result.put(redisKey, formula);
                                break;
                            }
                        } catch (Exception e) {
                            log.warn("处理公式值失败: source={}, key={}", source, redisKey, e);
                        }
                    }
                }
            }

            stopWatch.stop();
            log.info("加载公式完成，处理 {} 个key，获取到 {} 个公式，耗时统计：\n{}",
                    redisKeys.size(), result.size(), stopWatch.prettyPrint());

        } catch (Exception e) {
            log.error("加载公式失败", e);
            // 3. 使用缓存作为降级策略
            return getFromLocalCache(redisKeys);
        }

        return result;
    }

    /**
     * 从本地缓存获取公式
     */
    private Map<String, String> getFromLocalCache(List<String> redisKeys) {
        Map<String, String> result = new HashMap<>();
        for (String key : redisKeys) {
            try {
                String formula = formulaCache.getIfPresent(key);
                if (StrUtil.isNotBlank(formula)) {
                    result.put(key, formula);
                }
            } catch (Exception e) {
                log.warn("从本地缓存获取公式失败: key={}", key, e);
            }
        }
        return result;
    }

    /**
     * 处理公式值
     */
    private String processFormulaValue(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return value.toString().trim();
        } catch (Exception e) {
            log.warn("处理公式值失败", e);
            return null;
        }
    }

    /**
     * 更新公式缓存
     */
    private void updateFormulaCache(Map<String, String> formulas) {
        if (MapUtils.isEmpty(formulas)) {
            return;
        }

        formulas.forEach((key, value) -> {
            try {
                formulaCache.put(key, value);
            } catch (Exception e) {
                log.warn("更新公式缓存失败: key={}, error={}", key, e.getMessage());
            }
        });
    }



    /**
     * 系统启动时预热缓存
     */
    @PostConstruct
    public void warmUp() {
        try {
            log.info("开始预热公式缓存...");

            // 获取常用的属性编码列表
            List<String> commonPropCodes = getCommonPropCodes();

            // 预加载这些公式
            preloadFormulas(commonPropCodes);

            log.info("公式缓存预热完成");

        } catch (Exception e) {
            log.warn("公式缓存预热失败: {}", e.getMessage());
        }
    }

    /**
     * 获取常用的属性编码列表
     * 可以从配置文件或数据库中获取
     */
    private List<String> getCommonPropCodes() {
        // 实现获取常用属性编码的逻辑
        return Collections.emptyList();
    }

    /**
     * 获取公式
     *
     * @param propCode 属性编码
     * @return 计算公式
     */
    /**
     * 获取公式
     */
    public String getFormula(String propCode) {
        if (StrUtil.isBlank(propCode)) {
            throw new BizException("属性编码不能为空");
        }

        try {
            String redisKey = buildRedisKey(propCode);
            // 从缓存获取 Optional<String>，然后获取其值
            return formulaCache.get(redisKey);
        } catch (Exception e) {
            log.error("获取公式失败, propCode: {}, error: {}", propCode, e.getMessage());
            return null; // 返回 null 而不是抛出异常
        }
    }

    /**
     * 预加载公式集合
     *
     * @param propCodes 属性编码集合
     */
    public void preloadFormulas(Collection<String> propCodes) {
        if (propCodes == null || propCodes.isEmpty()) {
            log.warn("预加载公式的属性编码列表为空");
            return;
        }

        StopWatch stopWatch = new StopWatch("预加载公式");
        try {
            stopWatch.start("构建Redis键");
            List<String> redisKeys = buildRedisKeys(propCodes);

            stopWatch.stop();
            stopWatch.start("加载公式");

            Map<String, String> formulas = loadFormulasFromSources(redisKeys);

            stopWatch.stop();
            stopWatch.start("更新缓存");

            updateCache(formulas);

            stopWatch.stop();
            log.info("预加载公式完成，处理{}个公式，耗时统计：\n{}",
                    formulas.size(), stopWatch.prettyPrint());

        } catch (Exception e) {
            log.error("预加载公式失败: {}", e.getMessage(), e);
            throw new BizException("预加载公式失败", e);
        }
    }

    /**
     * 构建Redis键
     */
    private String buildRedisKey(String propCode) {
        return propCode + ConstantBase.SPLITREDISKEY + ConstantBase.RULECOLID_COMMON;
    }

    /**
     * 批量构建Redis键
     */
    private List<String> buildRedisKeys(Collection<String> propCodes) {
        return propCodes.stream()
                .map(this::buildRedisKey)
                .collect(Collectors.toList());
    }

    /**
     * 从Redis加载公式
     */
    private String loadFormulaFromRedis(String redisKey) {
        String formula = loadFormulasFromSources(Collections.singletonList(redisKey))
                .get(redisKey);
        return formula;
    }

    /**
     * 从所有来源加载公式，按优先级顺序
     */
    /*private Map<String, String> loadFormulasFromSources(List<String> redisKeys) {
        Map<String, String> result = new HashMap<>();

        // 获取所有来源的公式（只获取一次）
        Map<String, Map<Object, Object>> allFormulas = new HashMap<>();
        allFormulas.put("INS", redisUtil.hmget(ConstantBase.INS_REDISKEY));
        allFormulas.put("MODEL", redisUtil.hmget(ConstantBase.MODEL_REDISKEY));
        allFormulas.put("TWINS", redisUtil.hmget(ConstantBase.TWINS_REDISKEY));

        // 定义优先级顺序
        String[] priorities = {"INS", "MODEL", "TWINS"};

        // 处理每个key
        for (String redisKey : redisKeys) {
            // 按优先级查找公式
            for (String source : priorities) {
                Map<Object, Object> formulas = allFormulas.get(source);
                if (MapUtils.isNotEmpty(formulas) && formulas.containsKey(redisKey)) {
                    String formula = processFormulaValue(formulas.get(redisKey));
                    if (StrUtil.isNotBlank(formula)) {
                        result.put(redisKey, formula);
                        break;  // 找到公式后跳出优先级循环
                    }
                }
            }
        }

        return result;
    }*/

    /**
     * 处理公式值
     */
    /*private String processFormulaValue(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof String) {
                return (String) value;
            } else if (value instanceof byte[]) {
                return new String((byte[]) value, StandardCharsets.UTF_8);
            } else {
                return value.toString();
            }
        } catch (Exception e) {
            log.warn("处理公式值失败: {}", e.getMessage());
            return null;
        }
    }*/

    /**
     * 更新缓存
     */
    private void updateCache(Map<String, String> formulas) {
        if (MapUtils.isEmpty(formulas)) {
            return;
        }

        int successCount = 0;
        for (Map.Entry<String, String> entry : formulas.entrySet()) {
            try {
                formulaCache.put(entry.getKey(), entry.getValue());
                successCount++;
            } catch (Exception e) {
                log.error("更新缓存失败, key: {}, error: {}",
                        entry.getKey(), e.getMessage());
            }
        }

        log.info("成功更新缓存{}个公式", successCount);
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        return formulaCache.stats();
    }

    /**
     * 清理指定的缓存
     */
    public void invalidateFormula(String propCode) {
        if (StrUtil.isNotBlank(propCode)) {
            String redisKey = buildRedisKey(propCode);
            formulaCache.invalidate(redisKey);
        }
    }

    /**
     * 清理所有缓存
     */
    public void invalidateAllFormulas() {
        formulaCache.invalidateAll();
    }

    /**
     * 定期记录缓存统计信息
     */
    @Scheduled(fixedRate = 300000) // 每5分钟
    public void logCacheStats() {
        CacheStats stats = formulaCache.stats();
        log.info("公式缓存统计 - 命中率: {}, 加载成功率: {}, 驱逐数: {}",
                stats.hitRate(),
                stats.loadSuccessCount(),
                stats.evictionCount());
    }

    /**
     * 服务关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        formulaCache.cleanUp();
    }
}
