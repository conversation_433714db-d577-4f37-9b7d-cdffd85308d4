package com.siact.energy.cal.server.convertor.dataSource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.alenfive.rocketapi.entity.DBConfig;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.server.common.datasource.dto.DBConfigDTO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.siact.energy.cal.server.entity.dataSource.DataSource;
import com.siact.energy.cal.common.core.convertor.AbstractConvertor;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceUpdateDTO;

import java.util.Objects;

/**
 * 数据源表(DataSource) Convertor
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:12
 */
@Mapper
public interface DataSourceConvertor extends AbstractConvertor<DataSourceVO, DataSource> {

    DataSourceConvertor INSTANCE = Mappers.getMapper(DataSourceConvertor.class);

    /**
     * 实体分页转VO分页
     *
     * @param entityPage 实体分页
     * @return VO 分页
     */
    PageBean<DataSourceVO> entityPage2VoPageBean(Page<DataSource> entityPage);

    /**
     * VO分页转实体分页
     *
     * @param voPageBean 实体分页
     * @return VO分页
     */
    Page<DataSource> voPageBean2EntityPage(PageBean<DataSourceVO> voPageBean);

    /**
     * 查询DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataSource queryDTO2Entity(DataSourceQueryDTO dto);

    /**
     * 新增DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataSource insertDTO2Entity(DataSourceInsertDTO dto);

    /**
     * 更新DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataSource updateDTO2Entity(DataSourceUpdateDTO dto);

    /**
     * 实体转测试DTO
     *
     * @param dataSource 实体
     * @return 测试DTO
     */
    DataSourceTestDTO entity2TestDTO(DataSource dataSource);

    /**
     * 数据源测试DTO转数据库配置DTO
     *
     * @param dto 数据源测试DTO
     * @return 数据库配置DTO
     */
    default DBConfigDTO testDTODBConfigDTO(DataSourceTestDTO dto) {

        if(Objects.isNull(dto)) {
            return null;
        }

        DBConfigDTO dbConfigDTO = new DBConfigDTO();
        dbConfigDTO.setUrl(dto.getJdbcUrl());
        dbConfigDTO.setUser(dto.getUserName());
        dbConfigDTO.setPassword(dto.getPassword());
        return dbConfigDTO;
    }

}
