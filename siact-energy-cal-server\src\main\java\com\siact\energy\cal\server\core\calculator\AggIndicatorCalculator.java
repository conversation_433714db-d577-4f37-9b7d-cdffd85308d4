package com.siact.energy.cal.server.core.calculator;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.core.pojo.AggregateFormulaInfo;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.FormulaCacheService;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.energycal.DataBaseService;
import com.siact.energy.cal.server.core.strategy.AggregateShardingStrategy;
import com.siact.energy.cal.server.core.service.AggregateResultMerger;
import com.siact.energy.cal.server.core.model.QueryContext;
import com.siact.energy.cal.server.core.model.ShardingParams;
import com.siact.energy.cal.server.core.model.AggregateShardTask;
import com.siact.energy.cal.server.core.optimizer.TDengineQueryOptimizer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Package com.siact.energy.cal.server.core
 * @description: 聚合指标计算器
 * <AUTHOR>
 * @create 2025/8/8 15:44
 */

@Slf4j
@Service
public class AggIndicatorCalculator {
    @Autowired
    private DataBaseService dataBaseService;

    @Autowired
    private TimeWindowGenerator timeWindowGenerator;

    @Autowired
    private FormulaCacheService formulaCacheService;
    @Autowired
    private AggregateShardingStrategy aggregateShardingStrategy;
    @Autowired
    private AggregateResultMerger aggregateResultMerger;
    @Autowired
    private TDengineQueryOptimizer tdengineQueryOptimizer;
    // 使用 ApplicationContext 解决循环依赖 (Agg -> Base)
    @Autowired
    private ApplicationContext applicationContext;
    private BaseIndicatorCalculator baseIndicatorCalculator;

    @PostConstruct
    public void init() {
        this.baseIndicatorCalculator = applicationContext.getBean(BaseIndicatorCalculator.class);
    }


    // 专用于聚合指标计算的线程池
    private final ForkJoinPool aggregateCalculationPool = new ForkJoinPool(
            Math.min(Runtime.getRuntime().availableProcessors(), 8));

    // 支持的聚合函数类型
    private static final Set<String> SUPPORTED_AGG_FUNCTIONS = new HashSet<>(Arrays.asList(
            "sum", "avg", "max", "min", "count", "first", "last", "diff"
    ));

    /**
     * 🔥 智能聚合指标计算 - 主入口方法（升级版）
     * 优化策略：
     * 1. 智能分片策略，避免二次聚合精度问题
     * 2. 按聚合函数类型分组，数据库内批量执行聚合运算
     * 3. 针对不同聚合函数采用不同的分片策略
     */
    public CompletableFuture<Void> calculateAggregateIndicators(
            List<String> aggregateIndicators,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        return CompletableFuture.runAsync(() -> {
            if (CollectionUtils.isEmpty(aggregateIndicators)) return;

            StopWatch stopWatch = new StopWatch("智能聚合指标计算");
            stopWatch.start();
            try {
                // 1. 解析聚合公式
                Map<String, Map<String, String>> aggTypeMap = parseAggregateFormulas(aggregateIndicators);

                // 2. 检查是否需要智能分片
                if (shouldUseIntelligentSharding(aggTypeMap, queryDTO)) {
                    log.info("🚀 启用聚合指标智能分片策略");
                    executeIntelligentAggregateCalculations(aggTypeMap, timeWindows, queryDTO, dataSourceVo, globalResults);
                } else {
                    log.info("📋 使用传统聚合计算策略");
                    executeAggregateCalculations(aggTypeMap, timeWindows, queryDTO, dataSourceVo, globalResults);
                }
            } catch (Exception e) {
                log.error("聚合指标计算失败", e);
                throw new BizException("聚合指标计算失败", e);
            } finally {
                stopWatch.stop();
                log.info(stopWatch.prettyPrint());
            }
        }, aggregateCalculationPool);
    }

    /**
     * 🔥 智能聚合计算执行器
     */
    private void executeIntelligentAggregateCalculations(
            Map<String, Map<String, String>> aggTypeMap,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        // 1. 构建查询上下文
        QueryContext context = buildQueryContext(aggTypeMap, queryDTO);

        // 2. 为每个聚合函数制定专用分片策略
        aggTypeMap.forEach((functionName, metricMapping) -> {
            try {
                log.info("处理聚合函数: {}, 指标数量: {}", functionName, metricMapping.size());

                // 构建函数特定的上下文
                Map<String, Set<String>> functionToMetrics = new HashMap<>();
                functionToMetrics.put(functionName, new HashSet<>(metricMapping.values()));

                // 简化分片策略：指标数量大于3就进行分片
                if (metricMapping.size() > 3) {
                    // 执行指标分片计算（每3个指标一批）
                    executeShardedAggregateQuery(functionName, metricMapping, queryDTO, dataSourceVo, globalResults, null);
                } else {
                    // 指标数量少，直接执行
                    executeDirectAggregateQuery(functionName, metricMapping, queryDTO, dataSourceVo, globalResults);
                }

            } catch (Exception e) {
                log.error("聚合函数 {} 计算失败", functionName, e);
            }
        });
    }

    /**
     * 判断是否需要使用智能分片
     */
    private boolean shouldUseIntelligentSharding(Map<String, Map<String, String>> aggTypeMap, TimeQueryDTO queryDTO) {
        // 计算总指标数
        int totalMetrics = aggTypeMap.values().stream()
                .mapToInt(Map::size)
                .sum();

        // 计算时间跨度
        long timeSpanDays = TimeWindowGenerator.getDaysBetween(queryDTO.getStartTime(), queryDTO.getEndTime());

        // 智能分片触发条件
        boolean hasLargeMetricCount = totalMetrics > 30; // 超过30个指标
        boolean hasLongTimeSpan = timeSpanDays > 7; // 超过7天
        boolean hasComplexFunctions = aggTypeMap.keySet().stream()
                .anyMatch(func -> Arrays.asList("avg", "first", "last").contains(func.toLowerCase()));

        return hasLargeMetricCount || hasLongTimeSpan || hasComplexFunctions;
    }

    /**
     * 构建查询上下文
     */
    private QueryContext buildQueryContext(Map<String, Map<String, String>> aggTypeMap, TimeQueryDTO queryDTO) {
        // 收集所有指标
        List<String> allIndicators = aggTypeMap.values().stream()
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());

        // 使用Builder模式构建QueryContext
        return QueryContext.builder()
                .queryDTO(queryDTO)
                .indicators(allIndicators)
                .systemCpuUsage(50.0) // 简化实现，实际应该获取真实值
                .systemMemoryUsage(60.0)
                .activeConnections(5)
                .build();
    }

    /**
     * 直接执行聚合查询（无分片）
     */
    private void executeDirectAggregateQuery(String functionName,
                                           Map<String, String> metricMapping,
                                           TimeQueryDTO queryDTO,
                                           DataSourceVo dataSourceVo,
                                           ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        log.debug("直接执行 {} 聚合查询，指标数量: {}", functionName, metricMapping.size());

        try {
            if ("diff".equals(functionName)) {
                processDiffCalculation(globalResults, metricMapping);
            } else {
                dataBaseService.processAggQuery(globalResults, functionName, metricMapping, queryDTO, dataSourceVo);
            }
        } catch (Exception e) {
            log.error("直接聚合查询失败: {}", functionName, e);
            throw e;
        }
    }

    /**
     * 🔥 执行分片聚合查询
     */
    private void executeShardedAggregateQuery(String functionName,
                                            Map<String, String> metricMapping,
                                            TimeQueryDTO queryDTO,
                                            DataSourceVo dataSourceVo,
                                            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
                                            ShardingParams shardingParams) {

        log.info("执行指标分片聚合查询: 函数={}, 指标总数={}, 每批指标数=3",
                functionName, metricMapping.size());

        try {
            // 1. 按3个指标为一组进行分片
            List<Map<String, String>> metricBatches = createMetricBatches(metricMapping, 3);

            log.info("创建指标分片完成: 总批次数={}, 每批指标数=3", metricBatches.size());

            // 2. 并行执行各个指标批次的聚合查询
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < metricBatches.size(); i++) {
                Map<String, String> batchMetrics = metricBatches.get(i);
                final int batchIndex = i + 1;

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        log.debug("开始执行指标批次 {}/{}: 指标数={}",
                                batchIndex, metricBatches.size(), batchMetrics.size());

                        // 执行单个批次的聚合查询（不拆分时间）
                        if ("diff".equals(functionName)) {
                            processDiffCalculation(globalResults, batchMetrics);
                        } else {
                            dataBaseService.processAggQuery(globalResults, functionName,
                                    batchMetrics, queryDTO, dataSourceVo);
                        }

                        log.debug("指标批次 {}/{} 执行完成: 指标数={}",
                                batchIndex, metricBatches.size(), batchMetrics.size());

                    } catch (Exception e) {
                        log.error("指标批次 {}/{} 执行失败: 指标数={}",
                                batchIndex, metricBatches.size(), batchMetrics.size(), e);
                    }
                }, aggregateCalculationPool);

                futures.add(future);
            }

            // 3. 等待所有批次完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("指标分片聚合查询执行完成: 函数={}, 总批次数={}", functionName, metricBatches.size());

        } catch (Exception e) {
            log.error("指标分片聚合查询执行失败: 函数={}", functionName, e);
            throw e;
        }
    }

    /**
     * 创建指标分批（每批最多指定数量的指标）
     */
    private List<Map<String, String>> createMetricBatches(Map<String, String> metricMapping, int batchSize) {
        List<Map<String, String>> batches = new ArrayList<>();
        List<Map.Entry<String, String>> entries = new ArrayList<>(metricMapping.entrySet());

        for (int i = 0; i < entries.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, entries.size());
            Map<String, String> batch = new HashMap<>();

            for (int j = i; j < endIndex; j++) {
                Map.Entry<String, String> entry = entries.get(j);
                batch.put(entry.getKey(), entry.getValue());
            }

            batches.add(batch);
        }

        return batches;
    }

    /**
     * 创建聚合分片任务
     */
    private List<AggregateShardTask> createAggregateShardTasks(String functionName,
                                                             Map<String, String> metricMapping,
                                                             TimeQueryDTO queryDTO,
                                                             ShardingParams shardingParams) {

        List<AggregateShardTask> tasks = new ArrayList<>();

        if (shardingParams.getStrategy() == ShardingParams.ShardingStrategy.METRIC_ONLY) {
            // 仅指标分片
            tasks.addAll(createMetricShardTasks(functionName, metricMapping, queryDTO, shardingParams));
        } else if (shardingParams.getStrategy() == ShardingParams.ShardingStrategy.TIME_ONLY) {
            // 仅时间分片
            tasks.addAll(createTimeShardTasks(functionName, metricMapping, queryDTO, shardingParams));
        } else if (shardingParams.getStrategy() == ShardingParams.ShardingStrategy.BOTH) {
            // 双重分片
            tasks.addAll(createBothShardTasks(functionName, metricMapping, queryDTO, shardingParams));
        }

        return tasks;
    }

    /**
     * 创建指标分片任务
     */
    private List<AggregateShardTask> createMetricShardTasks(String functionName,
                                                          Map<String, String> metricMapping,
                                                          TimeQueryDTO queryDTO,
                                                          ShardingParams shardingParams) {

        List<AggregateShardTask> tasks = new ArrayList<>();
        List<String> sourceMetrics = new ArrayList<>(metricMapping.keySet());

        // 按指标分片大小分组
        for (int i = 0; i < sourceMetrics.size(); i += shardingParams.getMetricShardSize()) {
            int endIndex = Math.min(i + shardingParams.getMetricShardSize(), sourceMetrics.size());
            List<String> shardMetrics = sourceMetrics.subList(i, endIndex);

            // 创建该分片的指标映射
            Map<String, String> shardMapping = new HashMap<>();
            shardMetrics.forEach(metric -> shardMapping.put(metric, metricMapping.get(metric)));

            // 创建分片任务
            AggregateShardTask task = new AggregateShardTask(
                    functionName,
                    shardMapping,
                    queryDTO, // 时间范围不变
                    i / shardingParams.getMetricShardSize(),
                    String.format("指标分片[%d-%d]", i, endIndex - 1)
            );

            tasks.add(task);
        }

        return tasks;
    }

    /**
     * 创建时间分片任务
     */
    private List<AggregateShardTask> createTimeShardTasks(String functionName,
                                                        Map<String, String> metricMapping,
                                                        TimeQueryDTO queryDTO,
                                                        ShardingParams shardingParams) {

        List<AggregateShardTask> tasks = new ArrayList<>();

        // 生成时间分片
        List<TimeWindow> timeShards = timeWindowGenerator.generateShards(
                queryDTO.getStartTime(), queryDTO.getEndTime(),
                shardingParams.getTimeShardDays(), "d");

        for (int i = 0; i < timeShards.size(); i++) {
            TimeWindow timeShard = timeShards.get(i);

            // 创建该时间分片的查询DTO
            TimeQueryDTO shardQueryDTO = new TimeQueryDTO();
            BeanUtils.copyProperties(queryDTO, shardQueryDTO);
            shardQueryDTO.setStartTime(timeShard.getStartTime());
            shardQueryDTO.setEndTime(timeShard.getEndTime());

            // 创建分片任务
            AggregateShardTask task = new AggregateShardTask(
                    functionName,
                    metricMapping, // 指标映射不变
                    shardQueryDTO,
                    i,
                    String.format("时间分片[%s-%s]", timeShard.getStartTime(), timeShard.getEndTime())
            );

            tasks.add(task);
        }

        return tasks;
    }

    /**
     * 创建双重分片任务
     */
    private List<AggregateShardTask> createBothShardTasks(String functionName,
                                                        Map<String, String> metricMapping,
                                                        TimeQueryDTO queryDTO,
                                                        ShardingParams shardingParams) {

        List<AggregateShardTask> tasks = new ArrayList<>();

        // 先按时间分片
        List<TimeWindow> timeShards = timeWindowGenerator.generateShards(
                queryDTO.getStartTime(), queryDTO.getEndTime(),
                shardingParams.getTimeShardDays(), "d");

        // 再按指标分片
        List<String> sourceMetrics = new ArrayList<>(metricMapping.keySet());

        int taskIndex = 0;
        for (int timeIndex = 0; timeIndex < timeShards.size(); timeIndex++) {
            TimeWindow timeShard = timeShards.get(timeIndex);

            for (int metricStart = 0; metricStart < sourceMetrics.size(); metricStart += shardingParams.getMetricShardSize()) {
                int metricEnd = Math.min(metricStart + shardingParams.getMetricShardSize(), sourceMetrics.size());
                List<String> shardMetrics = sourceMetrics.subList(metricStart, metricEnd);

                // 创建该分片的指标映射
                Map<String, String> shardMapping = new HashMap<>();
                shardMetrics.forEach(metric -> shardMapping.put(metric, metricMapping.get(metric)));

                // 创建该时间分片的查询DTO
                TimeQueryDTO shardQueryDTO = new TimeQueryDTO();
                BeanUtils.copyProperties(queryDTO, shardQueryDTO);
                shardQueryDTO.setStartTime(timeShard.getStartTime());
                shardQueryDTO.setEndTime(timeShard.getEndTime());

                // 创建分片任务
                AggregateShardTask task = new AggregateShardTask(
                        functionName,
                        shardMapping,
                        shardQueryDTO,
                        taskIndex++,
                        String.format("双重分片[时间:%s-%s,指标:%d-%d]",
                                timeShard.getStartTime(), timeShard.getEndTime(), metricStart, metricEnd - 1)
                );

                tasks.add(task);
            }
        }

        return tasks;
    }

    /**
     * 🔥 并行执行聚合分片任务
     */
    private List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> executeAggregateShardTasks(
            List<AggregateShardTask> shardTasks, DataSourceVo dataSourceVo) {

        log.debug("开始并行执行 {} 个聚合分片任务", shardTasks.size());

        // 使用CompletableFuture并行执行分片任务
        List<CompletableFuture<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>>> futures =
                shardTasks.stream()
                        .map(task -> CompletableFuture.supplyAsync(() -> {
                            try {
                                log.debug("执行聚合分片任务: {}", task.getShardDescription());

                                ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> shardResult =
                                        new ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>();

                                // 执行单个分片的聚合查询
                                if ("diff".equals(task.getFunctionName())) {
                                    processDiffCalculation(shardResult, task.getMetricMapping());
                                } else {
                                    dataBaseService.processAggQuery(shardResult, task.getFunctionName(),
                                            task.getMetricMapping(), task.getQueryDTO(), dataSourceVo);
                                }

                                log.debug("聚合分片任务完成: {}, 结果指标数: {}",
                                        task.getShardDescription(), shardResult.size());

                                return shardResult;

                            } catch (Exception e) {
                                log.error("聚合分片任务执行失败: {}", task.getShardDescription(), e);
                                return new ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>();
                            }
                        }, aggregateCalculationPool))
                        .collect(Collectors.toList());

        // 等待所有分片任务完成并收集结果
        List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> results =
                futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());

        log.debug("所有聚合分片任务执行完成，共 {} 个分片结果", results.size());

        return results;
    }

    private Map<String, Map<String, String>> parseAggregateFormulas(List<String> aggregateIndicators) {
        Map<String, Map<String, String>> aggTypeMap = new HashMap<>();
        Map<String, String> formulaMap = formulaCacheService.batchGetFormulas(aggregateIndicators, ConstantBase.RULECOLID_COMMON);
        for (String indicator : aggregateIndicators) {
            String formula = formulaMap.get(indicator);
            if (StringUtils.isBlank(formula)) continue;
            AggregateFormulaInfo formulaInfo = parseAggregateFormula(formula);
            if (formulaInfo != null && SUPPORTED_AGG_FUNCTIONS.contains(formulaInfo.getFunctionName())) {
                aggTypeMap.computeIfAbsent(formulaInfo.getFunctionName(), k -> new HashMap<>())
                        .put(formulaInfo.getSourceProperty(), indicator);
            }
        }
        return aggTypeMap;
    }

    private void executeAggregateCalculations(
            Map<String, Map<String, String>> aggTypeMap,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        Set<String> highDensityFuncs = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "count"));

        // 1. 分离任务
        Map<String, Map<String, String>> highDensityTasks = new HashMap<>();
        Map<String, Map<String, String>> lowDensityTasks = new HashMap<>();
        aggTypeMap.forEach((func, mapping) -> {
            if (highDensityFuncs.contains(func.toLowerCase())) {
                highDensityTasks.put(func, mapping);
            } else {
                lowDensityTasks.put(func, mapping);
            }
        });

        // 2. 首先，并行执行所有低密度任务
        executeParallelTasks(lowDensityTasks, timeWindows, queryDTO, dataSourceVo, globalResults);

        // 3. 然后，集中处理所有高密度任务
        if (!highDensityTasks.isEmpty()) {
            //补算

            // a. 统一收集所有需要补算的源指标
            prepareHighDensityData(highDensityTasks, queryDTO, dataSourceVo);
            // c. 补算完成后，并行执行所有高密度聚合查询
            executeParallelTasks(highDensityTasks, timeWindows, queryDTO, dataSourceVo, globalResults);
        }
    }

    /**
     * 统一准备高密度数据：使用固定的1分钟粒度检查数据完整性，并在需要时一次性完成所有补算。
     */
    private void prepareHighDensityData(
            Map<String, Map<String, String>> highDensityTasks,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 收集所有高密度任务的源指标
        Set<String> allSourceProps = highDensityTasks.values().stream()
                .flatMap(mapping -> mapping.keySet().stream())
                .collect(Collectors.toSet());

        if (allSourceProps.isEmpty()) return;

        // 创建一个全新的、独立的 DTO，专门用于完整性检查，粒度固定为1分钟
        TimeQueryDTO checkQueryDTO = new TimeQueryDTO();
        BeanUtils.copyProperties(queryDTO, checkQueryDTO); // 继承起止时间等
        checkQueryDTO.setInterval(1);
        checkQueryDTO.setTsUnit("m"); // 使用 'm'，确保与 findIncompleteIndicators 内部逻辑一致
        List<TimeWindow> timeWindowsList = timeWindowGenerator.generateTimeWindows(checkQueryDTO);
        //  使用这个专门的 DTO 进行检查
        List<String> incompleteIndicators = dataBaseService.findIncompleteIndicators(allSourceProps,timeWindowsList, checkQueryDTO, dataSourceVo);

        // 如果需要补算，则一次性完成
        if (!incompleteIndicators.isEmpty()) {
            log.info("检测到 {} 个源指标数据不完整(基于1分钟粒度检查)，触发统一的高密度补算...", incompleteIndicators.size());

            // 补算时，我们需要计算的指标是那些不完整的指标
            checkQueryDTO.setDataCodes(incompleteIndicators);


            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> recomputationResultMap = new ConcurrentHashMap<>();
            CompletableFuture<Void> recomputationFuture = baseIndicatorCalculator.calculateBaseIndicators(
                    incompleteIndicators,
                    timeWindowsList,
                    checkQueryDTO,
                    dataSourceVo,
                    recomputationResultMap
            );

            recomputationFuture.join(); // 同步等待补算完成
            log.info("统一的高密度数据补算完成。");
        } else {
            log.info("所有高密度任务的源指标数据完整(基于1分钟粒度检查)，无需补算。");
        }
    }

    /**
     * 为给定的指标列表，执行一次性的高密度补算
     */
    private void performRecomputation(
            Set<String> propsToRecompute,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        log.info("检测到 {} 个源指标数据不完整，触发统一的高密度补算...", propsToRecompute.size());

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> recomputationResultMap = new ConcurrentHashMap<>();
        CompletableFuture<Void> recomputationFuture = baseIndicatorCalculator.calculateBaseIndicators(
                new ArrayList<>(propsToRecompute), timeWindows, queryDTO, dataSourceVo, recomputationResultMap
        );

        recomputationFuture.join(); // 同步等待补算完成
        log.info("统一的高密度数据补算完成。");
    }

    /**
     * 通用的并行任务执行器
     */
    private void executeParallelTasks(
            Map<String, Map<String, String>> tasks,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        if (tasks.isEmpty()) return;

        List<CompletableFuture<Void>> futures = tasks.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> {
                    String functionName = entry.getKey();
                    Map<String, String> propMapping = entry.getValue();
                    try {
                        if ("diff".equals(functionName)) {
                            processDiffCalculation(globalResults, propMapping);
                        } else {
                            dataBaseService.processAggQuery(globalResults, functionName, propMapping, queryDTO, dataSourceVo);
                        }
                    } catch (Exception e) {
                        log.error("执行 {} 聚合计算任务失败", functionName, e);
                    }
                }, aggregateCalculationPool))
                .collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    /**
     * 解析单个聚合公式
     * 支持格式：sum(property), avg(property), max(property) 等
     */
    private AggregateFormulaInfo parseAggregateFormula(String formula) {
        if (StringUtils.isBlank(formula)) {
            return null;
        }

        // 移除空格，保持原始大小写
        String cleanFormula = formula.trim();

        // 查找函数名和参数
        int openParen = cleanFormula.indexOf('(');
        int closeParen = cleanFormula.lastIndexOf(')');

        if (openParen == -1 || closeParen == -1 || openParen >= closeParen) {
            return null;
        }

        // 函数名转换为小写，属性编码保持原始大小写
        String functionName = cleanFormula.substring(0, openParen).trim().toLowerCase();
        String rawSourceProperty = cleanFormula.substring(openParen + 1, closeParen).trim();

        if (StringUtils.isBlank(functionName) || StringUtils.isBlank(rawSourceProperty)) {
            return null;
        }

        // 处理 @[property] 格式，提取纯净的属性编码（保持原始大小写）
        String sourceProperty = extractPropertyFromVariable(rawSourceProperty);
        if (StringUtils.isBlank(sourceProperty)) {
            log.warn("无法从公式参数中提取属性编码: {}", rawSourceProperty);
            return null;
        }

        return new AggregateFormulaInfo(functionName, sourceProperty);
    }

    /**
     * 从变量引用中提取属性编码
     * 处理格式：@[property_code] -> property_code
     */
    private String extractPropertyFromVariable(String variable) {
        if (StringUtils.isBlank(variable)) {
            return null;
        }

        String trimmed = variable.trim();

        // 处理 @[property_code] 格式
        if (trimmed.startsWith("@[") && trimmed.endsWith("]")) {
            String extracted = trimmed.substring(2, trimmed.length() - 1).trim();
            log.debug("从变量引用格式提取属性编码: {} -> {}", variable, extracted);
            return extracted;
        }

        // 如果不是 @[...] 格式，直接返回原值
        return trimmed;
    }

    /**
     * 差值计算（并行流式）
     * 直接使用globalResults中已有的数据，避免重复查询数据库
     * 统一使用并行流式计算，简化代码并提升性能
     *
     * @param globalResults 全局结果映射，包含所有已计算的指标数据
     * @param propMapping 属性映射，key为源指标，value为目标差值指标
     */
    private void processDiffCalculation(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            Map<String, String> propMapping) {

        if (propMapping.isEmpty()) {
            log.debug("差值计算属性映射为空，跳过处理");
            return;
        }

        log.debug("开始并行流式差值计算，源指标数量: {}", propMapping.size());

        // 统一使用并行流式计算，简化代码并提升性能
        propMapping.entrySet().parallelStream()
                .forEach(entry -> {
                    String sourceIndicator = entry.getKey();
                    String targetIndicator = entry.getValue();

                    try {
                        ConcurrentHashMap<String, BigDecimal> sourceData = globalResults.get(sourceIndicator);

                        if (sourceData == null || sourceData.isEmpty()) {
                            log.warn("源指标 {} 的数据为空，跳过差值计算", sourceIndicator);
                            return;
                        }

                        // 使用流式差值计算
                        calculateDiffForIndicatorStream(sourceData, targetIndicator, globalResults);
                        log.debug("指标 {} -> {} 并行流式差值计算完成", sourceIndicator, targetIndicator);

                    } catch (Exception e) {
                        log.error("指标 {} -> {} 并行流式差值计算失败: {}", sourceIndicator, targetIndicator, e.getMessage(), e);
                    }
                });

        log.debug("并行流式差值计算完成，处理指标数: {}", propMapping.size());
    }






    /**
     * 并行流式差值计算（统一高性能版本）
     * 使用并行流式处理，简化代码并提升性能
     *
     * @param sourceData 源指标的时间序列数据
     * @param targetIndicator 目标差值指标名称
     * @param globalResults 全局结果映射
     */
    private void calculateDiffForIndicatorStream(
            ConcurrentHashMap<String, BigDecimal> sourceData,
            String targetIndicator,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        if (sourceData.size() < 2) {
            log.debug("指标 {} 数据点少于2个，无法计算差值", targetIndicator);
            return;
        }

        // 创建目标指标的数据映射
        ConcurrentHashMap<String, BigDecimal> targetData = globalResults.computeIfAbsent(
                targetIndicator, k -> new ConcurrentHashMap<>());

        // 使用并行流式处理：排序 → 转数组 → 并行计算差值
        String[] timeStamps = sourceData.keySet().parallelStream()
                .sorted()
                .toArray(String[]::new);

        // 并行计算相邻时间点的差值
        IntStream.range(0, timeStamps.length - 1)
                .parallel()
                .forEach(i -> {
                    String currentTime = timeStamps[i];
                    String nextTime = timeStamps[i + 1];

                    BigDecimal currentValue = sourceData.get(currentTime);
                    BigDecimal nextValue = sourceData.get(nextTime);

                    if (currentValue != null && nextValue != null) {
                        try {
                            // 计算差值：下一个值 - 当前值
                            BigDecimal diff = nextValue.subtract(currentValue);
                            // 将差值存储到当前时间点
                            targetData.put(currentTime, diff);
                        } catch (Exception e) {
                            log.warn("时间点 {} 差值计算失败: {}", currentTime, e.getMessage());
                        }
                    }
                });

        log.debug("指标 {} 并行流式差值计算完成，生成差值数据点数: {}", targetIndicator, targetData.size());
    }

}
