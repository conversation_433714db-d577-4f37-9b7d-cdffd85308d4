package com.siact.energy.cal.server.core.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 🔥 聚合结果合并器 - 处理分片后的二次聚合
 * 
 * 核心功能：
 * 1. 正确处理不同聚合函数的分片结果合并
 * 2. 确保数学精确性
 * 3. 支持复杂的加权平均计算
 */
@Service
@Slf4j
public class AggregateResultMerger {
    
    /**
     * 🎯 合并分片聚合结果
     * 
     * @param shardResults 各分片的聚合结果列表
     * @param functionName 聚合函数名称
     * @param metricMapping 指标映射关系
     * @return 合并后的最终结果
     */
    public ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeShardResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            String functionName,
            Map<String, String> metricMapping) {
        
        if (shardResults == null || shardResults.isEmpty()) {
            log.warn("分片结果为空，返回空结果");
            return new ConcurrentHashMap<>();
        }
        
        if (shardResults.size() == 1) {
            log.debug("只有一个分片结果，直接返回");
            return shardResults.get(0);
        }
        
        log.info("开始合并 {} 个分片的 {} 聚合结果", shardResults.size(), functionName);
        
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergedResult = new ConcurrentHashMap<>();
        
        // 根据聚合函数类型选择合并策略
        switch (functionName.toLowerCase()) {
            case "sum":
                mergedResult = mergeSumResults(shardResults, metricMapping);
                break;
            case "max":
                mergedResult = mergeMaxResults(shardResults, metricMapping);
                break;
            case "min":
                mergedResult = mergeMinResults(shardResults, metricMapping);
                break;
            case "avg":
                mergedResult = mergeAvgResults(shardResults, metricMapping);
                break;
            case "count":
                mergedResult = mergeCountResults(shardResults, metricMapping);
                break;
            case "first":
                mergedResult = mergeFirstResults(shardResults, metricMapping);
                break;
            case "last":
                mergedResult = mergeLastResults(shardResults, metricMapping);
                break;
            default:
                log.warn("不支持的聚合函数: {}，使用默认合并策略", functionName);
                mergedResult = mergeDefaultResults(shardResults, metricMapping);
        }
        
        log.info("{} 聚合结果合并完成，合并后指标数: {}", functionName, mergedResult.size());
        return mergedResult;
    }
    
    /**
     * 合并SUM结果：各分片结果相加
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeSumResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行SUM结果合并");
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> result = new ConcurrentHashMap<>();
        
        // 遍历所有目标指标
        metricMapping.values().forEach(targetMetric -> {
            ConcurrentHashMap<String, BigDecimal> targetData = new ConcurrentHashMap<>();
            
            // 收集所有时间点
            Set<String> allTimePoints = shardResults.stream()
                    .filter(shard -> shard.containsKey(targetMetric))
                    .flatMap(shard -> shard.get(targetMetric).keySet().stream())
                    .collect(Collectors.toSet());
            
            // 对每个时间点进行SUM合并
            allTimePoints.forEach(timePoint -> {
                BigDecimal sum = shardResults.stream()
                        .filter(shard -> shard.containsKey(targetMetric))
                        .map(shard -> shard.get(targetMetric).get(timePoint))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                if (sum.compareTo(BigDecimal.ZERO) != 0) {
                    targetData.put(timePoint, sum);
                }
            });
            
            if (!targetData.isEmpty()) {
                result.put(targetMetric, targetData);
            }
        });
        
        return result;
    }
    
    /**
     * 合并MAX结果：取各分片结果的最大值
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeMaxResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行MAX结果合并");
        return mergeMinMaxResults(shardResults, metricMapping, true);
    }
    
    /**
     * 合并MIN结果：取各分片结果的最小值
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeMinResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行MIN结果合并");
        return mergeMinMaxResults(shardResults, metricMapping, false);
    }
    
    /**
     * 通用的MIN/MAX合并逻辑
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeMinMaxResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping,
            boolean isMax) {
        
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> result = new ConcurrentHashMap<>();
        
        metricMapping.values().forEach(targetMetric -> {
            ConcurrentHashMap<String, BigDecimal> targetData = new ConcurrentHashMap<>();
            
            // 收集所有时间点
            Set<String> allTimePoints = shardResults.stream()
                    .filter(shard -> shard.containsKey(targetMetric))
                    .flatMap(shard -> shard.get(targetMetric).keySet().stream())
                    .collect(Collectors.toSet());
            
            // 对每个时间点进行MIN/MAX合并
            allTimePoints.forEach(timePoint -> {
                Optional<BigDecimal> extremeValue = shardResults.stream()
                        .filter(shard -> shard.containsKey(targetMetric))
                        .map(shard -> shard.get(targetMetric).get(timePoint))
                        .filter(Objects::nonNull)
                        .reduce(isMax ? BigDecimal::max : BigDecimal::min);
                
                extremeValue.ifPresent(value -> targetData.put(timePoint, value));
            });
            
            if (!targetData.isEmpty()) {
                result.put(targetMetric, targetData);
            }
        });
        
        return result;
    }
    
    /**
     * 🔥 合并AVG结果：复杂的加权平均计算
     * 注意：这需要额外的COUNT信息来正确计算加权平均
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeAvgResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行AVG结果合并（需要重新计算加权平均）");
        
        // 对于AVG，我们需要重新查询原始数据来计算正确的平均值
        // 或者在分片时同时计算SUM和COUNT
        log.warn("AVG函数的分片合并需要额外的COUNT信息，当前实现可能不够精确");
        
        // 简化实现：取各分片AVG的平均值（不够精确，但可用）
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> result = new ConcurrentHashMap<>();
        
        metricMapping.values().forEach(targetMetric -> {
            ConcurrentHashMap<String, BigDecimal> targetData = new ConcurrentHashMap<>();
            
            Set<String> allTimePoints = shardResults.stream()
                    .filter(shard -> shard.containsKey(targetMetric))
                    .flatMap(shard -> shard.get(targetMetric).keySet().stream())
                    .collect(Collectors.toSet());
            
            allTimePoints.forEach(timePoint -> {
                List<BigDecimal> values = shardResults.stream()
                        .filter(shard -> shard.containsKey(targetMetric))
                        .map(shard -> shard.get(targetMetric).get(timePoint))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                
                if (!values.isEmpty()) {
                    BigDecimal sum = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal avg = sum.divide(BigDecimal.valueOf(values.size()), 6, RoundingMode.HALF_UP);
                    targetData.put(timePoint, avg);
                }
            });
            
            if (!targetData.isEmpty()) {
                result.put(targetMetric, targetData);
            }
        });
        
        return result;
    }
    
    /**
     * 合并COUNT结果：各分片结果相加
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeCountResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行COUNT结果合并");
        // COUNT的合并逻辑与SUM相同
        return mergeSumResults(shardResults, metricMapping);
    }
    
    /**
     * 合并FIRST结果：取时间最早的值
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeFirstResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行FIRST结果合并");
        return mergeFirstLastResults(shardResults, metricMapping, true);
    }
    
    /**
     * 合并LAST结果：取时间最晚的值
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeLastResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行LAST结果合并");
        return mergeFirstLastResults(shardResults, metricMapping, false);
    }
    
    /**
     * 通用的FIRST/LAST合并逻辑
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeFirstLastResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping,
            boolean isFirst) {
        
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> result = new ConcurrentHashMap<>();
        
        metricMapping.values().forEach(targetMetric -> {
            // 收集所有分片中该指标的所有时间点和值
            Map<String, BigDecimal> allData = new HashMap<>();
            
            shardResults.stream()
                    .filter(shard -> shard.containsKey(targetMetric))
                    .forEach(shard -> allData.putAll(shard.get(targetMetric)));
            
            if (!allData.isEmpty()) {
                // 找到最早或最晚的时间点
                String extremeTime = allData.keySet().stream()
                        .sorted(isFirst ? String::compareTo : (a, b) -> b.compareTo(a))
                        .findFirst()
                        .orElse(null);
                
                if (extremeTime != null) {
                    ConcurrentHashMap<String, BigDecimal> targetData = new ConcurrentHashMap<>();
                    targetData.put(extremeTime, allData.get(extremeTime));
                    result.put(targetMetric, targetData);
                }
            }
        });
        
        return result;
    }
    
    /**
     * 默认合并策略：简单合并所有结果
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> mergeDefaultResults(
            List<ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>>> shardResults,
            Map<String, String> metricMapping) {
        
        log.debug("执行默认结果合并");
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> result = new ConcurrentHashMap<>();
        
        // 简单地合并所有分片的结果
        shardResults.forEach(shard -> {
            shard.forEach((metric, data) -> {
                result.computeIfAbsent(metric, k -> new ConcurrentHashMap<>()).putAll(data);
            });
        });
        
        return result;
    }
}
