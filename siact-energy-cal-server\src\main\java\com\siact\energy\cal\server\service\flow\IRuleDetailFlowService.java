package com.siact.energy.cal.server.service.flow;


import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;
import com.siact.energy.cal.server.entity.flow.RuleDetailEntity;

import java.util.List;
import java.util.Optional;

public interface IRuleDetailFlowService  extends IService<RuleDetailEntity> {
    RuleDetailEntity getRuleDetail(String id);

    Optional<RuleDetailEntity> getRuleDetailOption(String id);

}
