package com.siact.energy.cal.server.core.utils;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataIntervalQueryVo;
import com.siact.energy.cal.common.pojo.vo.energycal.DataPointQueryVo;
import com.siact.energy.cal.common.pojo.vo.energycal.ValTimes;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 结果组装器
 * 将计算结果组装成最终的返回格式
 */
@Service
@Slf4j
public class ResultAssembler {

    /**
     * 组装计算结果
     */
    public List<DataIntervalQueryVo> assembleResults(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            TimeQueryDTO queryDTO,
            List<TimeWindow> timeWindows) {
        
        try {
            // 并行组装结果
            return queryDTO.getDataCodes().parallelStream()
                .map(dataCode -> assembleIndicatorResult(dataCode, globalResults, timeWindows))
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("结果组装失败", e);
            throw new RuntimeException("结果组装失败", e);
        }
    }

    /**
     * 组装单个指标的结果
     */
    private DataIntervalQueryVo assembleIndicatorResult(
            String dataCode,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            List<TimeWindow> timeWindows) {
        
        DataIntervalQueryVo vo = new DataIntervalQueryVo();
        vo.setDataCode(dataCode);
        
        // 获取该指标的时间值映射
        ConcurrentHashMap<String, BigDecimal> timeValues = globalResults.get(dataCode);
        
        List<ValTimes> valTimesList = new ArrayList<>();
        
        // 按时间窗口顺序组装数据
        for (TimeWindow timeWindow : timeWindows) {
            ValTimes valTimes = new ValTimes();
            valTimes.setTimestamp(timeWindow.getAlignedTime());
            
            if (timeValues != null) {
                BigDecimal value = timeValues.get(timeWindow.getAlignedTime());
                valTimes.setPropVal(value != null ? value.toString() : null);
            } else {
                valTimes.setPropVal(null);
            }
            
            valTimesList.add(valTimes);
        }
        
        // 按时间排序
        valTimesList.sort(Comparator.comparing(ValTimes::getTimestamp));
        vo.setValTimes(valTimesList);
        
        return vo;
    }

    /**
     * 组装时间区间查询结果
     * 将计算结果转换为DataPointQueryVo格式，返回指定时间范围内的所有数据点
     */
    public List<DataPointQueryVo> assembleTimeIntervalResults(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            TimeQueryDTO queryDTO) {

        try {
            String startTime = queryDTO.getStartTime();
            String endTime = queryDTO.getEndTime();
            List<String> dataCodes = queryDTO.getDataCodes();

            log.debug("开始组装时间区间结果: 指标数量={}, 时间范围={} - {}",
                dataCodes.size(), startTime, endTime);

            // 根据数据量决定是否使用并行流处理
            if (dataCodes.size() > 100) {
                return processTimeIntervalWithParallelStream(globalResults, startTime, endTime, dataCodes);
            } else {
                return processTimeIntervalWithStream(globalResults, startTime, endTime, dataCodes);
            }

        } catch (Exception e) {
            log.error("时间区间结果组装失败", e);
            throw new RuntimeException("时间区间结果组装失败", e);
        }
    }

    /**
     * 使用并行流处理大量数据的时间区间查询
     */
    private List<DataPointQueryVo> processTimeIntervalWithParallelStream(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            String startTime,
            String endTime,
            List<String> dataCodes) {

        return dataCodes.parallelStream()
                .map(dataCode -> buildTimeIntervalDataPoint(globalResults, dataCode, startTime, endTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(() -> new ArrayList<>(dataCodes.size())));
    }

    /**
     * 使用普通流处理少量数据的时间区间查询
     */
    private List<DataPointQueryVo> processTimeIntervalWithStream(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            String startTime,
            String endTime,
            List<String> dataCodes) {

        return dataCodes.stream()
                .map(dataCode -> buildTimeIntervalDataPoint(globalResults, dataCode, startTime, endTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(() -> new ArrayList<>(dataCodes.size())));
    }

    /**
     * 构建单个数据点位的时间区间查询结果
     */
    private DataPointQueryVo buildTimeIntervalDataPoint(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            String dataCode,
            String startTime,
            String endTime) {

        try {
            DataPointQueryVo vo = new DataPointQueryVo();
            vo.setDataCode(dataCode);
            vo.setTimestamp(startTime);

            // 获取该指标在指定时间范围内的最近值
            BigDecimal propVal = findNearestValueInTimeRange(globalResults, dataCode, startTime, endTime);
            if (propVal != null) {
                vo.setPropVal(propVal.toString());
            }
            return vo;

        } catch (Exception e) {
            log.error("构建时间区间数据点失败，dataCode: {}", dataCode, e);
            return null;
        }
    }

    /**
     * 在指定时间范围内查找最近的值
     * 复用EnergyCalService中的findNearestValue逻辑
     */
    private BigDecimal findNearestValueInTimeRange(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            String dataCode,
            String startTime,
            String endTime) {

        ConcurrentHashMap<String, BigDecimal> timeValues = globalResults.get(dataCode);
        if (timeValues == null || timeValues.isEmpty()) {
            return null;
        }

        // 查找时间范围内最接近开始时间的值
        BigDecimal nearestValue = null;
        String nearestTime = null;

        for (Map.Entry<String, BigDecimal> entry : timeValues.entrySet()) {
            String timestamp = entry.getKey();

            // 检查时间是否在范围内
            if (timestamp.compareTo(startTime) >= 0 && timestamp.compareTo(endTime) <= 0) {
                if (nearestTime == null || timestamp.compareTo(nearestTime) < 0) {
                    nearestTime = timestamp;
                    nearestValue = entry.getValue();
                }
            }
        }

        return nearestValue;
    }

    /**
     * 组装时间断面查询结果
     * 将计算结果转换为DataPointQueryVo格式，返回指定时间点的数据
     */
    public List<DataPointQueryVo> assembleTimeSliceResults(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            List<String> dataCodes,
            String targetTime) {

        try {
            log.debug("开始组装时间断面结果: 指标数量={}, 目标时间点={}",
                dataCodes.size(), targetTime);

            // 根据数据量决定是否使用并行流处理
            if (dataCodes.size() > 100) {
                return processTimeSliceWithParallelStream(globalResults, dataCodes, targetTime);
            } else {
                return processTimeSliceWithStream(globalResults, dataCodes, targetTime);
            }

        } catch (Exception e) {
            log.error("时间断面结果组装失败", e);
            throw new RuntimeException("时间断面结果组装失败", e);
        }
    }

    /**
     * 使用并行流处理大量数据的时间断面查询
     */
    private List<DataPointQueryVo> processTimeSliceWithParallelStream(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            List<String> dataCodes,
            String targetTime) {

        return dataCodes.parallelStream()
                .map(dataCode -> buildTimeSliceDataPoint(globalResults, dataCode, targetTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(() -> new ArrayList<>(dataCodes.size())));
    }

    /**
     * 使用普通流处理少量数据的时间断面查询
     */
    private List<DataPointQueryVo> processTimeSliceWithStream(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            List<String> dataCodes,
            String targetTime) {

        return dataCodes.stream()
                .map(dataCode -> buildTimeSliceDataPoint(globalResults, dataCode, targetTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(() -> new ArrayList<>(dataCodes.size())));
    }

    /**
     * 构建单个数据点位的时间断面查询结果
     */
    private DataPointQueryVo buildTimeSliceDataPoint(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            String dataCode,
            String targetTime) {

        try {
            DataPointQueryVo vo = new DataPointQueryVo();
            vo.setDataCode(dataCode);
            vo.setTimestamp(targetTime);

            // 获取该指标在目标时间点的值
            ConcurrentHashMap<String, BigDecimal> timeValues = globalResults.get(dataCode);
            if (timeValues != null && !timeValues.isEmpty()) {
                BigDecimal propVal = timeValues.get(targetTime);
                if (propVal != null) {
                    vo.setPropVal(propVal.toString());
                }
            }

            return vo;

        } catch (Exception e) {
            log.error("构建时间断面数据点失败，dataCode: {}", dataCode, e);
            return null;
        }
    }
}
