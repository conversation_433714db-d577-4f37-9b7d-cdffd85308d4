package com.siact.energy.cal.server.core.pojo;

import com.siact.energy.cal.server.core.pojo.TimeWindow;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 计算结果封装类
 * 用于在服务层和控制器层之间传递计算结果和时间窗口信息
 */
public class CalculationResult {
    
    /**
     * 全局计算结果
     * 结构: 指标编码 -> (时间戳 -> 数值)
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults;
    
    /**
     * 时间窗口列表
     */
    private List<TimeWindow> timeWindows;
    
    /**
     * 计算耗时统计信息
     */
    private String performanceInfo;
    
    public CalculationResult() {
    }
    
    public CalculationResult(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults, 
                           List<TimeWindow> timeWindows, 
                           String performanceInfo) {
        this.globalResults = globalResults;
        this.timeWindows = timeWindows;
        this.performanceInfo = performanceInfo;
    }
    
    // Getters and Setters
    public ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> getGlobalResults() {
        return globalResults;
    }
    
    public void setGlobalResults(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {
        this.globalResults = globalResults;
    }
    
    public List<TimeWindow> getTimeWindows() {
        return timeWindows;
    }
    
    public void setTimeWindows(List<TimeWindow> timeWindows) {
        this.timeWindows = timeWindows;
    }
    
    public String getPerformanceInfo() {
        return performanceInfo;
    }
    
    public void setPerformanceInfo(String performanceInfo) {
        this.performanceInfo = performanceInfo;
    }
    
    @Override
    public String toString() {
        return String.format("CalculationResult{globalResults size=%d, timeWindows size=%d, performanceInfo='%s'}", 
                           globalResults != null ? globalResults.size() : 0,
                           timeWindows != null ? timeWindows.size() : 0,
                           performanceInfo);
    }
}
