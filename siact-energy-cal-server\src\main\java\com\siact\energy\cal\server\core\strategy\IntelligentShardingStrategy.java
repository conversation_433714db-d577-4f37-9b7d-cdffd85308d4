package com.siact.energy.cal.server.core.strategy;

import com.siact.energy.cal.server.common.config.CalculationProperties;
import com.siact.energy.cal.server.core.model.QueryContext;
import com.siact.energy.cal.server.core.model.QueryCost;
import com.siact.energy.cal.server.core.model.ShardingParams;
import com.siact.energy.cal.server.core.optimizer.AdaptiveOptimizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IntelligentShardingStrategy {

    @Autowired
    private CalculationProperties props;
    @Autowired
    private AdaptiveOptimizer adaptiveOptimizer;
    @Autowired
    private AdaptiveShardingStrategy adaptiveShardingStrategy;
    // 复杂度阈值常量
    private static final double HIGH_COMPLEXITY_THRESHOLD = 10.0;
    private static final double MEDIUM_COMPLEXITY_THRESHOLD = 5.0;
    
    // 分片大小常量
    private static final int MIN_TIME_SHARD_DAYS = 1;
    private static final int MAX_TIME_SHARD_DAYS = 30;
    private static final int MIN_METRIC_SHARD_SIZE = 20;
    private static final int MAX_METRIC_SHARD_SIZE = 100;
    
    // 并发控制常量
    private static final int MIN_CONCURRENCY = 4;
    private static final int MAX_CONCURRENCY = 32;

    /**
     * 🔥 基于数据驱动的智能分片（升级版）
     */
    public ShardingParams createOptimalPlan(QueryContext context) {
        // 检查是否启用智能分片
        if (!props.shouldUseIntelligentSharding()) {
            log.info("智能分片已禁用，使用传统分片策略");
            return createFallbackPlan(context);
        }

        log.info("🚀 启动智能分片计划: 指标数={}, 时间跨度={}天, 查询类型={}",
                context.getMetricCount(), context.getTimeSpanDays(),
                context.getQueryDTO().isEquallySpacedQuery() ? "等间隔" : "区间");

        // 🔥 优先使用数据驱动的自适应策略
        try {
            ShardingParams adaptiveParams = adaptiveShardingStrategy.createDataDrivenPlan(context);
            log.info("✅ 自适应分片计划生成成功: 策略={}, 时间分片={}天, 指标分片={}, 并发数={}",
                    adaptiveParams.getStrategy(), adaptiveParams.getTimeShardDays(),
                    adaptiveParams.getMetricShardSize(), adaptiveParams.getMaxConcurrency());
            return adaptiveParams;

        } catch (Exception e) {
            log.warn("⚠️ 自适应分片策略失败，降级到传统智能分片: {}", e.getMessage());

            // 降级到原有的成本模型分片
            QueryCost cost = estimateQueryCost(context);
            ShardingParams params = calculateOptimalSharding(cost, context);

            log.info("📋 传统智能分片计划生成完成: 策略={}, 时间分片={}天, 指标分片={}, 最大并发={}",
                    params.getStrategy(), params.getTimeShardDays(),
                    params.getMetricShardSize(), params.getMaxConcurrency());

            return params;
        }
    }

    /**
     * 创建降级方案（使用传统配置）
     */
    private ShardingParams createFallbackPlan(QueryContext context) {
        boolean needsTimeSharding = context.getTimeSpanDays() > props.getTimeShardThresholdDays();
        boolean needsMetricSharding = context.getMetricCount() > props.getMetricShardThresholdCount();

        ShardingParams.ShardingStrategy strategy;
        if (needsTimeSharding && needsMetricSharding) {
            strategy = ShardingParams.ShardingStrategy.BOTH;
        } else if (needsTimeSharding) {
            strategy = ShardingParams.ShardingStrategy.TIME_ONLY;
        } else if (needsMetricSharding) {
            strategy = ShardingParams.ShardingStrategy.METRIC_ONLY;
        } else {
            strategy = ShardingParams.ShardingStrategy.NO_SHARDING;
        }

        return ShardingParams.builder()
                .strategy(strategy)
                .timeShardDays(props.getTimeShardThresholdDays())
                .metricShardSize(props.getMetricShardSize())
                .needsTimeSharding(needsTimeSharding)
                .needsMetricSharding(needsMetricSharding)
                .maxConcurrency(props.getMaxConcurrentQueries())
                .build();
    }
    
    /**
     * 评估查询成本
     */
    private QueryCost estimateQueryCost(QueryContext context) {
        long timeSpan = context.getTimeSpanDays();
        int metricCount = context.getMetricCount();
        long expectedDataPoints = context.getExpectedDataPoints();
        
        // 计算各维度复杂度
        long timeComplexity = timeSpan * context.getInterval();
        int metricComplexity = metricCount;
        long dataVolumeComplexity = expectedDataPoints;
        
        return QueryCost.builder()
                .timeComplexity(timeComplexity)
                .metricComplexity(metricComplexity)
                .dataVolumeComplexity(dataVolumeComplexity)
                .build();
    }
    
    /**
     * 动态计算最优分片参数
     */
    private ShardingParams calculateOptimalSharding(QueryCost cost, QueryContext context) {
        double totalScore = cost.calculateTotalScore();
        
        // 根据复杂度选择分片策略
        ShardingParams.ShardingStrategy strategy = determineStrategy(cost, context);
        
        // 计算时间分片参数
        int timeShardDays = calculateOptimalTimeShardDays(cost, context);
        boolean needsTimeSharding = timeShardDays < context.getTimeSpanDays();
        
        // 计算指标分片参数
        int metricShardSize = calculateOptimalMetricShardSize(cost, context);
        boolean needsMetricSharding = metricShardSize < context.getMetricCount();
        
        // 计算最大并发数
        int maxConcurrency = calculateOptimalConcurrency(cost, context);
        
        return ShardingParams.builder()
                .strategy(strategy)
                .timeShardDays(timeShardDays)
                .metricShardSize(metricShardSize)
                .needsTimeSharding(needsTimeSharding)
                .needsMetricSharding(needsMetricSharding)
                .maxConcurrency(maxConcurrency)
                .build();
    }
    
    /**
     * 🔥 智能确定分片策略 - 基于数据驱动决策
     */
    private ShardingParams.ShardingStrategy determineStrategy(QueryCost cost, QueryContext context) {
        // 1. 计算预估数据量（核心指标）
        long estimatedDataPoints = estimateActualDataPoints(context);
        double dataIntensity = (double) estimatedDataPoints / (context.getTimeSpanDays() * context.getMetricCount());

        log.debug("智能分片决策: 预估数据点={}, 数据密度={}, 系统负载={}",
                estimatedDataPoints, dataIntensity, context.isSystemOverloaded());

        // 2. TDengine性能阈值（基于实际测试调优）
        long tdengineOptimalDataPoints = 50000;  // TDengine单次查询最优数据点数
        long tdengineMaxDataPoints = 200000;     // TDengine单次查询最大数据点数

        // 3. 智能决策逻辑
        if (estimatedDataPoints <= tdengineOptimalDataPoints && !context.isSystemOverloaded()) {
            log.debug("数据量较小，无需分片");
            return ShardingParams.ShardingStrategy.NO_SHARDING;
        }

        // 4. 分析分片收益比
        double timeShardingBenefit = calculateTimeShardingBenefit(context, dataIntensity);
        double metricShardingBenefit = calculateMetricShardingBenefit(context, dataIntensity);

        log.debug("分片收益分析: 时间分片收益={}, 指标分片收益={}", timeShardingBenefit, metricShardingBenefit);

        // 5. 基于收益比选择策略
        if (estimatedDataPoints > tdengineMaxDataPoints) {
            // 超大查询，必须双重分片
            return ShardingParams.ShardingStrategy.BOTH;
        } else if (timeShardingBenefit > 0.7 && metricShardingBenefit > 0.7) {
            // 双重分片都有显著收益
            return ShardingParams.ShardingStrategy.BOTH;
        } else if (timeShardingBenefit > metricShardingBenefit && timeShardingBenefit > 0.5) {
            // 时间分片收益更大
            return ShardingParams.ShardingStrategy.TIME_ONLY;
        } else if (metricShardingBenefit > 0.5) {
            // 指标分片收益更大
            return ShardingParams.ShardingStrategy.METRIC_ONLY;
        } else {
            // 自适应分片
            return ShardingParams.ShardingStrategy.ADAPTIVE;
        }
    }

    /**
     * 估算实际数据点数量
     */
    private long estimateActualDataPoints(QueryContext context) {
        // 基于查询类型和时间跨度估算
        if (context.getQueryDTO().isEquallySpacedQuery()) {
            // 等间隔查询：时间点数 × 指标数
            long timePoints = calculateTimePoints(context);
            return timePoints * context.getMetricCount();
        } else {
            // 区间查询：每个指标最多2个点（首末值）
            return context.getMetricCount() * 2L;
        }
    }

    /**
     * 计算时间点数量
     */
    private long calculateTimePoints(QueryContext context) {
        long timeSpanMinutes = context.getTimeSpanDays() * 24 * 60;
        int intervalMinutes = context.getQueryDTO().getInterval();

        // 根据时间单位转换
        String unit = context.getQueryDTO().getTsUnit();
        switch (unit.toLowerCase()) {
            case "h": intervalMinutes *= 60; break;
            case "d": intervalMinutes *= 24 * 60; break;
            case "s": intervalMinutes /= 60; break;
            // "m" 或其他默认为分钟
        }

        return Math.max(1, timeSpanMinutes / intervalMinutes);
    }

    /**
     * 计算时间分片收益
     */
    private double calculateTimeShardingBenefit(QueryContext context, double dataIntensity) {
        long timeSpan = context.getTimeSpanDays();

        // 时间分片收益因子
        double timeSpanFactor = Math.min(1.0, timeSpan / 30.0); // 30天以上收益最大
        double dataIntensityFactor = Math.min(1.0, dataIntensity / 1000.0); // 高密度数据收益大
        double systemLoadFactor = context.isSystemOverloaded() ? 1.5 : 1.0; // 系统过载时收益增加

        return timeSpanFactor * 0.4 + dataIntensityFactor * 0.4 + (systemLoadFactor - 1.0) * 0.2;
    }

    /**
     * 计算指标分片收益
     */
    private double calculateMetricShardingBenefit(QueryContext context, double dataIntensity) {
        int metricCount = context.getMetricCount();

        // 指标分片收益因子
        double metricCountFactor = Math.min(1.0, metricCount / 100.0); // 100个指标以上收益最大
        double connectionPressureFactor = Math.min(1.0, context.getActiveConnections() / 20.0); // 连接压力大时收益增加
        double memoryPressureFactor = context.getSystemMemoryUsage() > 80 ? 1.2 : 1.0; // 内存压力大时收益增加

        return metricCountFactor * 0.5 + connectionPressureFactor * 0.3 + (memoryPressureFactor - 1.0) * 0.2;
    }

    /**
     * 🔥 智能计算最优时间分片天数 - 基于数据密度和TDengine特性
     */
    private int calculateOptimalTimeShardDays(QueryCost cost, QueryContext context) {
        long timeSpan = context.getTimeSpanDays();
        long estimatedDataPoints = estimateActualDataPoints(context);

        // 1. 基于TDengine性能特征的基础分片大小
        int baseDays;
        if (context.getQueryDTO().isEquallySpacedQuery()) {
            // 等间隔查询：基于数据点密度
            long dailyDataPoints = estimatedDataPoints / Math.max(1, timeSpan);
            if (dailyDataPoints > 10000) {
                baseDays = 1; // 高密度数据，按天分片
            } else if (dailyDataPoints > 2000) {
                baseDays = 3; // 中密度数据，3天分片
            } else {
                baseDays = 7; // 低密度数据，周分片
            }
        } else {
            // 区间查询：数据量相对固定，主要考虑指标数量
            if (context.getMetricCount() > 200) {
                baseDays = 7;  // 大量指标，减少时间跨度
            } else if (context.getMetricCount() > 100) {
                baseDays = 15;
            } else {
                baseDays = 30; // 少量指标，可以较大时间跨度
            }
        }

        // 2. 系统负载调整
        if (context.isSystemOverloaded()) {
            baseDays = Math.max(1, baseDays / 2); // 系统过载时减半
        }

        // 3. 内存压力调整
        if (context.getSystemMemoryUsage() > 85) {
            baseDays = Math.max(1, baseDays / 2); // 内存压力大时减半
        }

        // 4. 连接池压力调整
        if (context.getActiveConnections() > 15) {
            baseDays = Math.max(1, baseDays * 2 / 3); // 连接池压力大时减少
        }

        // 5. 确保不超过总时间跨度
        int finalDays = Math.min(baseDays, (int) timeSpan);

        log.debug("智能时间分片计算: 总时间跨度={}天, 预估数据点={}, 基础分片={}天, 最终分片={}天",
                timeSpan, estimatedDataPoints, baseDays, finalDays);

        return Math.max(1, finalDays);
    }

    /**
     * 🔥 智能计算最优指标分片大小 - 基于TDengine IN子句性能特征
     */
    private int calculateOptimalMetricShardSize(QueryCost cost, QueryContext context) {
        int metricCount = context.getMetricCount();
        long timeSpan = context.getTimeSpanDays();

        // 1. TDengine IN子句性能基准（基于实际测试）
        int baseShardSize;
        if (context.getQueryDTO().isEquallySpacedQuery()) {
            // 等间隔查询：IN子句对性能影响更大
            if (timeSpan > 30) {
                baseShardSize = 10; // 长时间跨度，极小分片
            } else if (timeSpan > 7) {
                baseShardSize = 20; // 中等时间跨度，小分片
            } else if (timeSpan > 1) {
                baseShardSize = 30; // 短时间跨度，中等分片
            } else {
                baseShardSize = 50; // 极短时间跨度，可以较大分片
            }
        } else {
            // 区间查询：IN子句影响相对较小
            if (timeSpan > 30) {
                baseShardSize = 30;
            } else if (timeSpan > 7) {
                baseShardSize = 50;
            } else {
                baseShardSize = 80;
            }
        }

        // 2. 系统资源压力调整
        if (context.isSystemOverloaded()) {
            baseShardSize = Math.max(5, baseShardSize / 2); // 系统过载时减半
        }

        // 3. 内存使用率调整
        if (context.getSystemMemoryUsage() > 85) {
            baseShardSize = Math.max(5, baseShardSize * 2 / 3); // 内存压力大时减少
        }

        // 4. 连接池活跃连接数调整
        if (context.getActiveConnections() > 15) {
            baseShardSize = Math.max(5, baseShardSize * 3 / 4); // 连接池压力大时减少
        }

        // 5. CPU使用率调整
        if (context.getSystemCpuUsage() > 80) {
            baseShardSize = Math.max(5, baseShardSize * 3 / 4); // CPU压力大时减少
        }

        // 6. 确保不超过总指标数
        int finalShardSize = Math.min(baseShardSize, metricCount);

        log.debug("智能指标分片计算: 总指标数={}, 时间跨度={}天, 基础分片={}, 最终分片={}",
                metricCount, timeSpan, baseShardSize, finalShardSize);

        return Math.max(5, finalShardSize); // 最小5个指标
    }

    // 在 calculateOptimalConcurrency 方法中
    /**
     * 计算最优并发数（集成动态调优）
     */
    private int calculateOptimalConcurrency(QueryCost cost, QueryContext context) {
        // 获取动态调优参数
        AdaptiveOptimizer.DynamicParams dynamicParams = adaptiveOptimizer.getCurrentDynamicParams();

        // 基础并发数使用动态参数
        int baseConcurrency = dynamicParams.getConcurrency();

        // 根据复杂度调整
        if (cost.isHighComplexity() && props.getConcurrency().isReduceForHighComplexity()) {
            baseConcurrency = Math.max(props.getConcurrency().getMinConcurrency(), baseConcurrency / 2);
        } else if (cost.isLowComplexity() && props.getConcurrency().isIncreaseForLowComplexity()) {
            baseConcurrency = Math.min(props.getConcurrency().getMaxConcurrency(), baseConcurrency * 2);
        }

        // 根据系统负载调整
        if (context.isSystemOverloaded()) {
            baseConcurrency = Math.max(props.getConcurrency().getMinConcurrency(), baseConcurrency / 2);
        }

        // 根据活跃连接数调整
        if (context.getActiveConnections() > props.getMonitoring().getActiveConnectionThreshold()) {
            baseConcurrency = Math.max(props.getConcurrency().getMinConcurrency(), baseConcurrency / 2);
        }

        int finalConcurrency = Math.max(props.getConcurrency().getMinConcurrency(),
                Math.min(props.getConcurrency().getMaxConcurrency(), baseConcurrency));

        log.debug("并发数计算: 动态基础值={}, 最终值={}", dynamicParams.getConcurrency(), finalConcurrency);

        return finalConcurrency;
    }
}