package com.siact.energy.cal.server.common.datasource.db.core;

import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.utils.RedisUtil;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据库操作抽象类
 */
public abstract class AbstractDbOperator {
    protected final RedisUtil redisUtil;

    protected AbstractDbOperator(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }


    /**
     * 获取数据库连接
     */
    public abstract Connection getConnection(DataSourceVo dataSourceVo);
    
    /**
     * 执行基础查询
     */
    public abstract void executeBasicSql(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo);
            
    /**
     * 执行聚合查询
     */
    public abstract void executeAggQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping);
            
    /**
     * 执行差值查询
     */
    public abstract void executeDiffQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping);

    /**
     * 执行计数查询，并返回每个指标的点数。
     *
     * @param sql          待执行的计数SQL
     * @param dataSourceVo 数据源信息
     * @return             一个Map，Key是指标编码(devproperty)，Value是该指标的数据点总数。
     */
    public abstract Map<String, Long> executeCountQuery(String sql, DataSourceVo dataSourceVo);
    /**
     * 数据写入
     */
    public abstract void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                    List<String> dataCodes,
                                    DataSourceVo dataSourceVo);
                                   
    /**
     * 测试连接
     */
    public abstract boolean testConnection(DataSourceVo testDTO);
}