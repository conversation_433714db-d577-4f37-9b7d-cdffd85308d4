package com.siact.energy.cal.server.core.optimizer;

import com.siact.energy.cal.server.common.config.CalculationProperties;
import com.siact.energy.cal.server.core.monitor.QueryPerformanceMonitor;
import com.siact.energy.cal.server.core.monitor.SystemMonitorService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import lombok.Builder;

import java.util.concurrent.atomic.AtomicInteger;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
@Service
@Slf4j
public class AdaptiveOptimizer {
    
    @Autowired
    private CalculationProperties calculationProperties;
    @Autowired
    private QueryPerformanceMonitor performanceMonitor;
    @Autowired
    private SystemMonitorService systemMonitor;
    
    // 动态调优参数
    private final AtomicInteger dynamicConcurrency = new AtomicInteger(16);
    private final AtomicInteger dynamicMetricShardSize = new AtomicInteger(50);
    private final AtomicInteger dynamicTimeShardDays = new AtomicInteger(7);

    // 调优历史记录
    private OptimizationHistory lastOptimization = null;
    
    /**
     * 定期执行自适应调优
     */
    @Scheduled(fixedDelay = 300000) // 5分钟执行一次
    public void performAdaptiveOptimization() {
        if (!calculationProperties.getPerformance().isEnableAdaptiveResharding()) {
            return;
        }
        
        try {
            log.debug("开始执行自适应调优");
            
            // 收集当前性能数据
            QueryPerformanceMonitor.PerformanceStats stats = performanceMonitor.getPerformanceStats();
            double cpuUsage = systemMonitor.getCpuUsage();
            double memoryUsage = systemMonitor.getMemoryUsage();
            
            // 分析性能趋势
            PerformanceAnalysis analysis = analyzePerformance(stats, cpuUsage, memoryUsage);
            
            // 执行调优
            if (analysis.needsOptimization) {
                executeOptimization(analysis);
            }
            
            log.debug("自适应调优完成");
            
        } catch (Exception e) {
            log.error("自适应调优执行失败", e);
        }
    }
    
    /**
     * 分析性能数据
     */
    private PerformanceAnalysis analyzePerformance(
            QueryPerformanceMonitor.PerformanceStats stats, 
            double cpuUsage, 
            double memoryUsage) {
        
        PerformanceAnalysis analysis = new PerformanceAnalysis();
        
        // 分析查询性能
        if (stats.getTotalQueries() > 10) { // 有足够的样本数据
            double slowQueryRate = (double) stats.getSlowQueries() / stats.getTotalQueries();
            double failureRate = (double) stats.getFailedQueries() / stats.getTotalQueries();
            
            analysis.setSlowQueryRate(slowQueryRate);
            analysis.setFailureRate(failureRate);
            analysis.setAvgExecutionTime(stats.getAvgExecutionTime());
            
            // 判断是否需要优化
            if (slowQueryRate > 0.2) { // 慢查询率超过20%
                analysis.setNeedsOptimization(true);
                analysis.setOptimizationType(OptimizationType.REDUCE_LOAD);
                analysis.setReason("慢查询率过高: " + String.format("%.2f%%", slowQueryRate * 100));
            } else if (failureRate > 0.1) { // 失败率超过10%
                analysis.setNeedsOptimization(true);
                analysis.setOptimizationType(OptimizationType.IMPROVE_STABILITY);
                analysis.setReason("查询失败率过高: " + String.format("%.2f%%", failureRate * 100));
            } else if (cpuUsage > 85 || memoryUsage > 90) { // 系统资源紧张
                analysis.setNeedsOptimization(true);
                analysis.setOptimizationType(OptimizationType.REDUCE_RESOURCE_USAGE);
                analysis.setReason(String.format("系统资源使用率过高: CPU=%.1f%%, 内存=%.1f%%", cpuUsage, memoryUsage));
            } else if (slowQueryRate < 0.05 && cpuUsage < 50 && memoryUsage < 60) { // 性能良好，可以提升并发
                analysis.setNeedsOptimization(true);
                analysis.setOptimizationType(OptimizationType.INCREASE_THROUGHPUT);
                analysis.setReason("系统性能良好，可以提升吞吐量");
            }
        }
        
        analysis.setCpuUsage(cpuUsage);
        analysis.setMemoryUsage(memoryUsage);
        
        return analysis;
    }
    
    /**
     * 执行优化调整
     */
    private void executeOptimization(PerformanceAnalysis analysis) {
        log.info("执行自适应优化: 类型={}, 原因={}", analysis.getOptimizationType(), analysis.getReason());
        
        switch (analysis.getOptimizationType()) {
            case REDUCE_LOAD:
                reduceSystemLoad();
                break;
            case IMPROVE_STABILITY:
                improveStability();
                break;
            case REDUCE_RESOURCE_USAGE:
                reduceResourceUsage();
                break;
            case INCREASE_THROUGHPUT:
                increaseThroughput();
                break;
        }

        // 记录优化历史
        lastOptimization = OptimizationHistory.builder()
                .timestamp(System.currentTimeMillis())
                .type(analysis.getOptimizationType())
                .reason(analysis.getReason())
                .concurrencyBefore(dynamicConcurrency.get())
                .metricShardSizeBefore(dynamicMetricShardSize.get())
                .timeShardDaysBefore(dynamicTimeShardDays.get())
                .build();
    }
    
    /**
     * 减少系统负载
     */
    private void reduceSystemLoad() {
        // 减少并发数
        int currentConcurrency = dynamicConcurrency.get();
        int newConcurrency = Math.max(calculationProperties.getConcurrency().getMinConcurrency(), 
                                     currentConcurrency - 2);
        dynamicConcurrency.set(newConcurrency);
        
        // 减少分片大小
        int currentMetricSize = dynamicMetricShardSize.get();
        int newMetricSize = Math.max(calculationProperties.getSharding().getMinMetricShardSize(),
                                    currentMetricSize - 10);
        dynamicMetricShardSize.set(newMetricSize);
        
        log.info("减少系统负载: 并发数 {} -> {}, 指标分片大小 {} -> {}", 
                currentConcurrency, newConcurrency, currentMetricSize, newMetricSize);
    }
    
    /**
     * 提升稳定性
     */
    private void improveStability() {
        // 减少时间分片大小，提高查询稳定性
        int currentTimeShards = dynamicTimeShardDays.get();
        int newTimeShards = Math.max(calculationProperties.getSharding().getMinTimeShardDays(),
                                    currentTimeShards - 1);
        dynamicTimeShardDays.set(newTimeShards);
        
        log.info("提升查询稳定性: 时间分片大小 {} -> {} 天", currentTimeShards, newTimeShards);
    }
    
    /**
     * 减少资源使用
     */
    private void reduceResourceUsage() {
        // 综合减少各项参数
        reduceSystemLoad();
        improveStability();
        
        log.info("减少资源使用: 综合优化完成");
    }
    
    /**
     * 提升吞吐量
     */
    private void increaseThroughput() {
        // 适当增加并发数
        int currentConcurrency = dynamicConcurrency.get();
        int newConcurrency = Math.min(calculationProperties.getConcurrency().getMaxConcurrency(),
                                     currentConcurrency + 2);
        dynamicConcurrency.set(newConcurrency);
        
        // 适当增加分片大小
        int currentMetricSize = dynamicMetricShardSize.get();
        int newMetricSize = Math.min(calculationProperties.getSharding().getMaxMetricShardSize(),
                                    currentMetricSize + 10);
        dynamicMetricShardSize.set(newMetricSize);
        
        log.info("提升吞吐量: 并发数 {} -> {}, 指标分片大小 {} -> {}", 
                currentConcurrency, newConcurrency, currentMetricSize, newMetricSize);
    }

    public DynamicParams getCurrentDynamicParams() {
        return DynamicParams.builder()
                .concurrency(dynamicConcurrency.get())
                .metricShardSize(dynamicMetricShardSize.get())
                .timeShardDays(dynamicTimeShardDays.get())
                .build();
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceAnalysis {
        private boolean needsOptimization = false;
        private OptimizationType optimizationType;
        private String reason;
        private double slowQueryRate;
        private double failureRate;
        private long avgExecutionTime;
        private double cpuUsage;
        private double memoryUsage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptimizationHistory {
        private long timestamp;
        private OptimizationType type;
        private String reason;
        private int concurrencyBefore;
        private int metricShardSizeBefore;
        private int timeShardDaysBefore;
    }

    // 第238-244行修改为：
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicParams {
        private int concurrency;
        private int metricShardSize;
        private int timeShardDays;
    }
    
    public enum OptimizationType {
        REDUCE_LOAD,           // 减少负载
        IMPROVE_STABILITY,     // 提升稳定性
        REDUCE_RESOURCE_USAGE, // 减少资源使用
        INCREASE_THROUGHPUT    // 提升吞吐量
    }
}