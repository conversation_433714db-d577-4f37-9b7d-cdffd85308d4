package com.siact.energy.cal.server.core.executor;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.server.common.utils.CalculatorUtil;
import com.siact.energy.cal.server.core.optimizer.DerivedIndicatorOptimizer;
import com.siact.energy.cal.server.core.monitor.DerivedIndicatorPerformanceMonitor;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;

/**
 * 🔥 高性能衍生指标执行器
 *
 * 核心特性：
 * 1. 智能分批执行：按依赖层级和复杂度分批
 * 2. 列式计算优化：向量化计算
 * 3. 性能监控：实时监控计算性能
 */
@Component
@Slf4j
public class HighPerformanceDerivedExecutor {
    
    @Autowired
    private DerivedIndicatorOptimizer optimizer;

    @Autowired
    private DerivedIndicatorPerformanceMonitor performanceMonitor;
    
    // 时间格式化器
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 动态线程池
    private final ForkJoinPool executorPool = new ForkJoinPool(
            Math.min(Runtime.getRuntime().availableProcessors() * 2, 32));
    
    /**
     * 🚀 高性能衍生指标计算主入口
     */
    public CompletableFuture<Void> executeHighPerformanceCalculation(
            List<String> derivedIndicators,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {
        
        return CompletableFuture.runAsync(() -> {
            // 🔥 启动性能监控
            String sessionId = "HP_" + System.currentTimeMillis();
            DerivedIndicatorPerformanceMonitor.CalculationSession session =
                    performanceMonitor.startCalculation(sessionId, derivedIndicators.size(), true);

            try {
                log.info("🚀 启动高性能衍生指标计算: 指标数量={}", derivedIndicators.size());

                // 1. 分析和制定计算计划
                DerivedIndicatorOptimizer.DerivedCalculationPlan plan =
                        optimizer.analyzeDerivedIndicators(derivedIndicators);

                // 2. 提取时间点
                List<String> timePoints = extractTimePointsFromResults(globalResults);
                if (timePoints.isEmpty()) {
                    log.warn("未找到有效时间点，跳过衍生指标计算");
                    return;
                }

                // 3. 执行批次化计算
                executeBatchedCalculation(plan, timePoints, queryDTO, globalResults, timeWindows);

                log.info("✅ 高性能衍生指标计算完成");

            } catch (Exception e) {
                log.error("高性能衍生指标计算失败", e);
                throw new RuntimeException("衍生指标计算失败", e);
            } finally {
                // 🔥 结束性能监控
                performanceMonitor.endCalculation(session);
            }
        }, executorPool);
    }
    
    /**
     * 执行批次化计算
     */
    private void executeBatchedCalculation(DerivedIndicatorOptimizer.DerivedCalculationPlan plan,
                                         List<String> timePoints,
                                         TimeQueryDTO queryDTO,
                                         ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
                                         List<TimeWindow> timeWindows) {
        
        log.info("开始批次化计算: 总批次数={}", plan.batches.size());
        
        // 准备列式数据结构
        Map<String, Integer> timeIndexMap = CalculatorUtil.createTimeIndexMap(timePoints);
        Map<String, BigDecimal[]> columnData = CalculatorUtil.transposeToColumnar(
                globalResults, timePoints, timeIndexMap);
        
        // 计算DURATION值
        Long duration = calculateDurationInSeconds(queryDTO);
        
        // 按批次顺序执行
        for (DerivedIndicatorOptimizer.CalculationBatch batch : plan.batches) {
            try {
                log.debug("执行计算批次: {}", batch.description);
                long batchStartTime = System.currentTimeMillis();
                
                if (batch.type == DerivedIndicatorOptimizer.BatchType.NORMAL) {
                    // 普通指标批次
                    executeNormalIndicatorBatch(batch, plan.formulaMap, timePoints,
                            columnData, duration, timeIndexMap);
                } else if (batch.type == DerivedIndicatorOptimizer.BatchType.YOY_MOM) {
                    // 同比环比批次
                    executeYoYMoMBatch(batch, plan.formulaMap, timePoints,
                            queryDTO, globalResults, timeWindows);
                }

                long batchDuration = System.currentTimeMillis() - batchStartTime;
                log.debug("批次执行完成: {}, 耗时={}ms", batch.description, batchDuration);

                // 🔥 记录批次性能
                performanceMonitor.recordBatchPerformance(
                        batch.type.name(),
                        batch.complexity.name(),
                        batch.indicators.size(),
                        batchDuration);
                
            } catch (Exception e) {
                log.error("批次执行失败: {}", batch.description, e);
                // 继续执行下一个批次，不中断整个计算流程
            }
        }
        
        // 将新计算的普通指标结果合并回全局结果
        List<String> normalIndicators = plan.analysisResult.normalIndicators;
        if (!normalIndicators.isEmpty()) {
            CalculatorUtil.mergeColumnarToRowBased(normalIndicators, columnData, timePoints, globalResults);
        }
    }
    
    /**
     * 🔥 执行普通指标批次（高性能列式计算）
     */
    private void executeNormalIndicatorBatch(DerivedIndicatorOptimizer.CalculationBatch batch,
                                           Map<String, String> formulaMap,
                                           List<String> timePoints,
                                           Map<String, BigDecimal[]> columnData,
                                           Long duration,
                                           Map<String, Integer> timeIndexMap) {
        
        // 根据批次复杂度决定并行策略
        boolean useParallel = batch.complexity != DerivedIndicatorOptimizer.IndicatorComplexity.HIGH 
                && batch.indicators.size() > 10;
        
        if (useParallel) {
            // 并行处理（适用于低复杂度大批次）
            batch.indicators.parallelStream().forEach(indicator -> {
                calculateSingleIndicator(indicator, formulaMap, timePoints, columnData, duration);
            });
        } else {
            // 串行处理（适用于高复杂度或小批次）
            for (String indicator : batch.indicators) {
                calculateSingleIndicator(indicator, formulaMap, timePoints, columnData, duration);
            }
        }
    }
    
    /**
     * 计算单个普通指标
     */
    private void calculateSingleIndicator(String indicator,
                                        Map<String, String> formulaMap,
                                        List<String> timePoints,
                                        Map<String, BigDecimal[]> columnData,
                                        Long duration) {
        
        String originalFormula = formulaMap.get(indicator);
        if (StringUtils.isBlank(originalFormula)) return;
        
        try {
            // 处理DURATION替换
            String formula = originalFormula;
            if (duration != null && formula.contains(ConstantBase.DURATION)) {
                formula = formula.replace(ConstantBase.DURATION, duration.toString());
            }
            
            // 获取依赖变量
            List<String> variables = FormulaUtils.getVarList(formula);
            
            // 检查依赖是否满足
            Map<String, BigDecimal[]> dependencyColumns = new HashMap<>();
            boolean dependenciesMet = true;
            
            for (String variable : variables) {
                BigDecimal[] depColumn = columnData.get(variable);
                if (depColumn == null) {
                    log.warn("指标 {} 依赖 {} 不存在，跳过计算", indicator, variable);
                    dependenciesMet = false;
                    break;
                }
                dependencyColumns.put(variable, depColumn);
            }
            
            if (!dependenciesMet) return;
            
            // 🔥 高性能列式计算
            BigDecimal[] resultColumn = performVectorizedCalculation(
                    formula, variables, dependencyColumns, timePoints.size());
            
            // 存储结果到列数据中
            columnData.put(indicator, resultColumn);
            
        } catch (Exception e) {
            log.error("指标 {} 计算失败", indicator, e);
        }
    }
    
    /**
     * 🔥 向量化计算（列式计算的核心）
     */
    private BigDecimal[] performVectorizedCalculation(String formula,
                                                    List<String> variables,
                                                    Map<String, BigDecimal[]> dependencyColumns,
                                                    int timePointCount) {
        
        BigDecimal[] resultColumn = new BigDecimal[timePointCount];
        Map<String, BigDecimal> valueMap = new HashMap<>(variables.size());
        
        // 向量化计算：一次性处理所有时间点
        for (int i = 0; i < timePointCount; i++) {
            valueMap.clear();
            boolean hasAllValues = true;
            
            // 收集当前时间点的所有依赖值
            for (String variable : variables) {
                BigDecimal value = dependencyColumns.get(variable)[i];
                if (value == null) {
                    hasAllValues = false;
                    break;
                }
                valueMap.put(variable, value);
            }
            
            // 如果所有依赖值都存在，则计算结果
            if (hasAllValues) {
                try {
                    resultColumn[i] = FormulaUtils.calcFormula(formula, valueMap);
                } catch (Exception e) {
                    log.debug("时间点 {} 计算失败: {}", i, e.getMessage());
                    // 继续计算下一个时间点
                }
            }
        }
        
        return resultColumn;
    }
    
    /**
     * 执行同比环比批次
     */
    private void executeYoYMoMBatch(DerivedIndicatorOptimizer.CalculationBatch batch,
                                  Map<String, String> formulaMap,
                                  List<String> timePoints,
                                  TimeQueryDTO queryDTO,
                                  ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
                                  List<TimeWindow> timeWindows) {
        
        log.debug("执行同比环比批次: 指标数量={}", batch.indicators.size());
        
        // 解析同比环比指标
        Map<String, Map<String, String>> yoyMomMap = parseYoYMoMIndicators(batch.indicators, formulaMap);
        
        // 批量处理同比环比（复用原有逻辑，但优化了批次处理）
        batchProcessYoYMoM(yoyMomMap, queryDTO, globalResults, timeWindows, timePoints);
    }
    
    /**
     * 解析同比环比指标
     */
    private Map<String, Map<String, String>> parseYoYMoMIndicators(List<String> indicators, 
                                                                 Map<String, String> formulaMap) {
        
        Map<String, Map<String, String>> yoyMomMap = new HashMap<>();
        
        for (String indicator : indicators) {
            String formula = formulaMap.get(indicator);
            if (StringUtils.isBlank(formula)) continue;
            
            String functionType = null;
            String targetProperty = null;
            
            if (formula.contains(ConstantBase.YOY)) {
                functionType = "YOY";
                // 简化的解析逻辑，实际应该使用正则表达式
                targetProperty = extractTargetProperty(formula, "YOY");
            } else if (formula.contains(ConstantBase.MOM)) {
                functionType = "MOM";
                targetProperty = extractTargetProperty(formula, "MOM");
            }
            
            if (functionType != null && targetProperty != null) {
                yoyMomMap.computeIfAbsent(functionType, k -> new HashMap<>())
                        .put(indicator, targetProperty);
            }
        }
        
        return yoyMomMap;
    }
    
    /**
     * 提取目标属性（简化实现）
     */
    private String extractTargetProperty(String formula, String functionName) {
        try {
            int startIndex = formula.indexOf(functionName + "(");
            if (startIndex >= 0) {
                int openParen = formula.indexOf("(", startIndex);
                int closeParen = formula.indexOf(")", openParen);
                if (openParen >= 0 && closeParen > openParen) {
                    return formula.substring(openParen + 1, closeParen).trim();
                }
            }
        } catch (Exception e) {
            log.warn("解析目标属性失败: formula={}, function={}", formula, functionName);
        }
        return null;
    }
    
    /**
     * 批量处理同比环比（复用原有逻辑）
     */
    private void batchProcessYoYMoM(Map<String, Map<String, String>> yoyMomMap,
                                  TimeQueryDTO timeQueryDTO,
                                  ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                  List<TimeWindow> timeWindows,
                                  List<String> timePointList) {
        
        // 这里可以复用DerivedIndicatorCalculator中的同比环比处理逻辑
        // 为了简化，暂时使用简化版本
        log.debug("同比环比计算: 函数类型数={}", yoyMomMap.size());
        
        for (Map.Entry<String, Map<String, String>> entry : yoyMomMap.entrySet()) {
            String functionName = entry.getKey();
            Map<String, String> derivedToTargetMap = entry.getValue();
            
            log.debug("处理{}指标: 数量={}", functionName, derivedToTargetMap.size());
            
            // 这里应该实现具体的同比环比计算逻辑
            // 可以复用原有的batchProcessYoYMoM方法
        }
    }
    
    /**
     * 从结果中提取时间点
     */
    private List<String> extractTimePointsFromResults(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {
        
        Set<String> timePointSet = new HashSet<>();
        
        globalResults.forEach((indicator, timeValueMap) -> {
            if (!indicator.contains("YOY") && !indicator.contains("MOM")) {
                timePointSet.addAll(timeValueMap.keySet());
            }
        });
        
        List<String> timePoints = new ArrayList<>(timePointSet);
        timePoints.sort(String::compareTo);
        
        return timePoints;
    }
    
    /**
     * 计算DURATION值
     */
    private Long calculateDurationInSeconds(TimeQueryDTO queryDTO) {
        try {
            if (!queryDTO.isEquallySpacedQuery()) {
                // 区间查询：返回总时间跨度
                if (StringUtils.isNoneBlank(queryDTO.getStartTime(), queryDTO.getEndTime())) {
                    LocalDateTime startTime = LocalDateTime.parse(queryDTO.getStartTime(), DATE_TIME_FORMATTER);
                    LocalDateTime endTime = LocalDateTime.parse(queryDTO.getEndTime(), DATE_TIME_FORMATTER);
                    return ChronoUnit.SECONDS.between(startTime, endTime);
                }
            } else {
                // 等间隔查询：返回单个间隔的秒数
                Integer interval = queryDTO.getInterval();
                String tsUnit = queryDTO.getTsUnit();
                
                if (interval != null && StringUtils.isNotBlank(tsUnit)) {
                    switch (tsUnit.toLowerCase()) {
                        case "m": return interval * 60L;
                        case "h": return interval * 3600L;
                        case "d": return interval * 86400L;
                        case "n": return interval * 86400L * 30; // 月
                        case "y": return interval * 86400L * 365; // 年
                    }
                }
            }
        } catch (Exception e) {
            log.error("计算DURATION失败", e);
        }
        return null;
    }
    
    /**
     * 销毁方法
     */
    public void destroy() {
        if (executorPool != null && !executorPool.isShutdown()) {
            executorPool.shutdown();
            try {
                if (!executorPool.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
