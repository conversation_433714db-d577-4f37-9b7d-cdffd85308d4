package com.siact.energy.cal.server.core.monitor;

import com.siact.energy.cal.server.common.config.CalculationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

@Service
@Slf4j
public class QueryPerformanceMonitor {
    
    @Autowired
    private CalculationProperties calculationProperties;
    
    // 性能统计数据
    private final ConcurrentHashMap<String, QueryMetrics> metricsMap = new ConcurrentHashMap<>();
    private final LongAdder totalQueries = new LongAdder();
    private final LongAdder slowQueries = new LongAdder();
    private final LongAdder failedQueries = new LongAdder();
    private final AtomicLong totalExecutionTime = new AtomicLong(0);
    
    /**
     * 记录查询开始
     */
    public String startQuery(String operation, int indicatorCount, String timeRange) {
        String queryId = generateQueryId(operation);
        
        QueryMetrics metrics = QueryMetrics.builder()
                .queryId(queryId)
                .operation(operation)
                .indicatorCount(indicatorCount)
                .timeRange(timeRange)
                .startTime(System.currentTimeMillis())
                .status(QueryStatus.RUNNING)
                .build();
        
        metricsMap.put(queryId, metrics);
        totalQueries.increment();
        
        log.debug("查询开始: ID={}, 操作={}, 指标数={}, 时间范围={}", 
                queryId, operation, indicatorCount, timeRange);
        
        return queryId;
    }
    
    /**
     * 记录查询完成
     */
    public void completeQuery(String queryId, int resultSize) {
        QueryMetrics metrics = metricsMap.get(queryId);
        if (metrics != null) {
            long duration = System.currentTimeMillis() - metrics.getStartTime();
            metrics.setEndTime(System.currentTimeMillis());
            metrics.setDuration(duration);
            metrics.setResultSize(resultSize);
            metrics.setStatus(QueryStatus.COMPLETED);
            
            totalExecutionTime.addAndGet(duration);
            
            // 检查是否为慢查询
            if (calculationProperties.getPerformance().isEnableQueryMonitoring()) {
                long slowThreshold = calculationProperties.getPerformance().getSlowQueryThreshold();
                if (duration > slowThreshold) {
                    slowQueries.increment();
                    log.warn("慢查询检测: ID={}, 操作={}, 耗时={}ms, 指标数={}", 
                            queryId, metrics.getOperation(), duration, metrics.getIndicatorCount());
                }
            }
            
            log.debug("查询完成: ID={}, 耗时={}ms, 结果数={}", queryId, duration, resultSize);
        }
    }
    
    /**
     * 记录查询失败
     */
    public void failQuery(String queryId, String errorMessage) {
        QueryMetrics metrics = metricsMap.get(queryId);
        if (metrics != null) {
            long duration = System.currentTimeMillis() - metrics.getStartTime();
            metrics.setEndTime(System.currentTimeMillis());
            metrics.setDuration(duration);
            metrics.setStatus(QueryStatus.FAILED);
            metrics.setErrorMessage(errorMessage);
            
            failedQueries.increment();
            
            log.error("查询失败: ID={}, 操作={}, 耗时={}ms, 错误={}", 
                    queryId, metrics.getOperation(), duration, errorMessage);
        }
    }
    
    /**
     * 获取性能统计
     */
    public PerformanceStats getPerformanceStats() {
        long totalCount = totalQueries.sum();
        long avgExecutionTime = totalCount > 0 ? totalExecutionTime.get() / totalCount : 0;
        
        return PerformanceStats.builder()
                .totalQueries(totalCount)
                .slowQueries(slowQueries.sum())
                .failedQueries(failedQueries.sum())
                .avgExecutionTime(avgExecutionTime)
                .successRate(totalCount > 0 ? (double)(totalCount - failedQueries.sum()) / totalCount * 100 : 0)
                .build();
    }
    
    /**
     * 定期清理过期的监控数据
     */
    @Scheduled(fixedDelay = 300000) // 5分钟执行一次
    public void cleanupExpiredMetrics() {
        if (!calculationProperties.getPerformance().isEnableQueryMonitoring()) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        long expireTime = 30 * 60 * 1000; // 30分钟过期
        
        metricsMap.entrySet().removeIf(entry -> {
            QueryMetrics metrics = entry.getValue();
            return (currentTime - metrics.getStartTime()) > expireTime;
        });
        
        log.debug("清理过期监控数据完成，当前活跃查询数: {}", metricsMap.size());
    }
    
    /**
     * 定期输出性能报告
     */
    @Scheduled(fixedDelay = 60000) // 1分钟执行一次
    public void reportPerformance() {
        if (!calculationProperties.getPerformance().isEnableQueryMonitoring()) {
            return;
        }
        
        PerformanceStats stats = getPerformanceStats();
        if (stats.getTotalQueries() > 0) {
            log.info("查询性能报告: 总查询数={}, 慢查询数={}, 失败数={}, 平均耗时={}ms, 成功率={:.2f}%",
                    stats.getTotalQueries(), stats.getSlowQueries(), stats.getFailedQueries(),
                    stats.getAvgExecutionTime(), stats.getSuccessRate());
        }
    }
    
    private String generateQueryId(String operation) {
        return operation + "_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    @Data
    @lombok.Builder
    public static class QueryMetrics {
        private String queryId;
        private String operation;
        private int indicatorCount;
        private String timeRange;
        private long startTime;
        private long endTime;
        private long duration;
        private int resultSize;
        private QueryStatus status;
        private String errorMessage;
    }
    
    @Data
    @lombok.Builder
    public static class PerformanceStats {
        private long totalQueries;
        private long slowQueries;
        private long failedQueries;
        private long avgExecutionTime;
        private double successRate;
    }
    
    public enum QueryStatus {
        RUNNING, COMPLETED, FAILED
    }
}