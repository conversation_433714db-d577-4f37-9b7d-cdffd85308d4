package com.siact.energy.cal.server.entity.dataSource;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.siact.energy.cal.common.datasource.common.BaseEntity;

/**
 * 数据源表(DataSource)表实体类
 *
 * <AUTHOR>
 * @since 2024-05-20 13:36:03
 */
@Data
public class DataSource extends BaseEntity {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 数据源名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String databaseName;

    /**
     * 数据源类型
     */
    private Integer dbType;

    /**
     * 数据源ip
     */
    @TableField(condition = SqlCondition.LIKE)
    private String databaseIp;

    /**
     * 数据源端口
     */
    @TableField(condition = SqlCondition.LIKE)
    private String databasePort;

    /**
     * 数据库名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String db;

    /**
     * 超级表名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String tableName;

    /**
     * 状态（0-正常/1-中断），描述数据库的连接状态
     */
    private Integer status;

    /**
     * 用户名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String userName;

    /**
     * 密码
     */
    @TableField(condition = SqlCondition.LIKE)
    private String password;

    /**
     * 数据库连接串
     */
    @TableField(condition = SqlCondition.LIKE)
    private String jdbcUrl;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 数据源描述
     */
    private String description;

}

