package com.siact.energy.cal.server.common.datasource.db.core;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * SQL生成抽象类
 */
public abstract class AbstractSqlBuilder {
    
    /**
     * 生成基础查询SQL
     */
    public abstract String buildBasicQuerySql(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo);
    
    /**
     * 生成时间区间聚合SQL
     */
    public abstract String buildTimeRangeAggSql(String function,
                                               Map<String, String> propMapping,
                                                TimeQueryDTO queryDTO,
                                                String tableName);


    /**
     * 【修改】构建按时间间隔统计数据点数量的SQL
     * 这个查询需要能反映出 FILL(NULL) 的效果
     */
    public abstract String buildCountByIntervalSql(Set<String> sourceProps, TimeQueryDTO queryDTO, String tableName);
    /**
     * 生成等间隔采样聚合SQL
     */
    public abstract String buildIntervalAggSql(String function,
                                               Map<String, String> propMapping,
                                               TimeQueryDTO queryDTO,
                                               String tableName);

    /**
     * 生成时间截面采样SQL
     */
    public abstract String buildTimeSliceDataSql(String startTime,
                                                 String endTime,
                                                 List<String> dataCOdes,
                                               String tableName);
    /**
     * 生成差值计算SQL
     */
    public abstract String buildDiffSql(Map<String, String> propMapping,
                                      String tableName,
                                      String startTime,
                                      String endTime,
                                      String interval,
                                      String unit);

}