package com.siact.energy.cal.server.core.calculator;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.FormulaClass;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.CommonUtils;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.common.util.utils.HttpClientUtil;
import com.siact.energy.cal.server.common.cache.MultiLevelCacheTemplate;
import com.siact.energy.cal.server.common.config.CalculationProperties;
import com.siact.energy.cal.server.common.utils.CalculatorUtil;
import com.siact.energy.cal.server.core.monitor.QueryPerformanceMonitor;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.FormulaCacheService;
import com.siact.energy.cal.server.core.service.QueryPlanner;
import com.siact.energy.cal.server.service.energycal.DataBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 基础指标计算器 - 高性能重构版
 * <p>
 * 核心模型：结果复用 + 增量列式计算
 */
@Component
@Slf4j
public class BaseIndicatorCalculator {

    @Autowired
    private DataBaseService dataBaseService;
    @Autowired
    private MultiLevelCacheTemplate cacheTemplate;
    @Autowired
    private FormulaCacheService formulaCacheService;
    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    @Autowired
    private QueryPlanner queryPlanner;
    @Autowired
    private CalculationProperties calculationProperties;
    @Autowired
    private QueryPerformanceMonitor performanceMonitor;

    private final ForkJoinPool calculationPool = new ForkJoinPool(Math.min(Runtime.getRuntime().availableProcessors() * 2, 32));
    private final Cache<String, BigDecimal> staticPropertyCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 计算基础指标 - 主入口
     */
    public CompletableFuture<Void> calculateBaseIndicators(
            List<String> baseIndicators,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        return CompletableFuture.runAsync(() -> {
            StopWatch stopWatch = new StopWatch("基础指标计算(最终版)");
            try {
                // 1. 指标分类
                stopWatch.start("指标分类");
                Map<String, List<String>> indicatorTypes = classifyIndicators(baseIndicators);
                List<String> calculatedIndicators = indicatorTypes.get(ConstantBase.BASE_CAL);
                List<String> staticPropertyIndicators = indicatorTypes.get(ConstantBase.STAIC);
                stopWatch.stop();

                // 2. 一次性获取所有可用数据 (DB + 静态属性)
                stopWatch.start("获取全部可用数据");
                ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> allAvailableData =
                        queryRawData(baseIndicators, timeWindows, queryDTO, dataSourceVo);
                if (!CollectionUtils.isEmpty(staticPropertyIndicators)) {
                    processAndMergeStaticProperties(staticPropertyIndicators, timeWindows, allAvailableData);
                }
                stopWatch.stop();

                // 3. 执行核心计算 (增量列式计算)
                if (!CollectionUtils.isEmpty(calculatedIndicators)) {
                    stopWatch.start("核心增量计算");
                    performCalculations(calculatedIndicators, allAvailableData, timeWindows, dataSourceVo);
                    stopWatch.stop();
                }

                // 4. 将最终结果合并到全局结果集
                stopWatch.start("合并结果到全局");
                mergeResults(globalResults, allAvailableData);
                stopWatch.stop();

                log.info("基础指标计算完成: {}", stopWatch.prettyPrint());

            } catch (Exception e) {
                log.error("基础指标计算失败", e);
                throw new BizException("基础指标计算失败: " + e.getMessage());
            }
        }, calculationPool);
    }

    /**
     * 核心计算逻辑 - 增量列式计算
     * 此方法有副作用：会计算新值并合并回 allAvailableData 中。
     */

    private void performCalculations(
            List<String> calculatedIndicators,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> allAvailableData,
            List<TimeWindow> timeWindows,
            DataSourceVo dataSourceVo) {

        long startTime = System.currentTimeMillis();

        try {
            log.debug("开始执行基础指标计算: 计算指标数量={}, 时间窗口数量={}",
                    calculatedIndicators.size(), timeWindows.size());

            // 原有的计算逻辑保持不变...
            // 🔥 使用工具类
            List<String> timePoints = CalculatorUtil.extractTimePoints(allAvailableData, timeWindows);
            if (timePoints.isEmpty()) return;

            Map<String, Set<String>> missingDataPointsMap = findMissingDataPoints(calculatedIndicators, allAvailableData, timePoints);
            if (missingDataPointsMap.isEmpty()) {
                log.info("所有基础计算指标数据完整,无需计算。");
                return;
            }

            // 1. 准备工作：一次性获取所有依赖并排序
            List<String> indicatorsToCalculate = new ArrayList<>(missingDataPointsMap.keySet());
            List<String> allRelevantProps = formulaCacheService.getFormulaVarList(indicatorsToCalculate, ConstantBase.RULECOLID_COMMON);

            Map<String, String> fullFormulaMap = formulaCacheService.batchGetFormulas(allRelevantProps, ConstantBase.RULECOLID_COMMON);
            List<FormulaClass> fullFormulaList = fullFormulaMap.entrySet().stream()
                    .map(entry -> new FormulaClass(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toList());
            List<String> orderedIndicators = FormulaUtils.calculateOrder(fullFormulaList);

            // 2. 数据转置
            Map<String, Integer> timeIndexMap = CalculatorUtil.createTimeIndexMap(timePoints);
            Map<String, BigDecimal[]> columnData = CalculatorUtil.transposeToColumnar(allAvailableData, timePoints, timeIndexMap);

            // 3. 按拓扑序进行增量计算
            for (String indicatorToCalc : orderedIndicators) {
                if (!missingDataPointsMap.containsKey(indicatorToCalc)) continue;

                String formula = fullFormulaMap.get(indicatorToCalc);
                if (formula == null) continue;

                Set<String> missingTimePointsForIndicator = missingDataPointsMap.get(indicatorToCalc);
                List<String> varList = FormulaUtils.getVarList(formula);
                Map<String, BigDecimal[]> dependencyColumns = new HashMap<>();

                boolean dependenciesAvailable = true;
                for (String var : varList) {
                    BigDecimal[] dependencyCol = columnData.get(var);
                    if (dependencyCol == null) {
                        log.error("指标 {} 依赖的变量 {} 的数据列不存在！", indicatorToCalc, var);
                        dependenciesAvailable = false;
                        break;
                    }
                    dependencyColumns.put(var, dependencyCol);
                }
                if (!dependenciesAvailable) continue;

                BigDecimal[] resultColumn = columnData.computeIfAbsent(indicatorToCalc, k -> new BigDecimal[timePoints.size()]);
                Map<String, BigDecimal> tempValueMap = new HashMap<>(varList.size());

                for (String timePointToCalc : missingTimePointsForIndicator) {
                    Integer i = timeIndexMap.get(timePointToCalc);
                    if (i == null || resultColumn[i] != null) continue;

                    tempValueMap.clear();
                    boolean dataPointComplete = true;
                    for (String var : varList) {
                        BigDecimal value = dependencyColumns.get(var)[i];
                        if (value == null) {
                            dataPointComplete = false;
                            break;
                        }
                        tempValueMap.put(var, value);
                    }

                    if (dataPointComplete) {
                        try {
                            resultColumn[i] = FormulaUtils.calcFormula(formula, tempValueMap);
                        } catch (Exception e) {
                            log.error("基础指标 {} 在时间点 {} 的增量计算失败: {}", indicatorToCalc, timePointToCalc, e.getMessage());
                        }
                    }
                }
            }

            // 4. 数据反转，准备存储
            // 🔥 使用工具类
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> newlyCalculatedMap =
                    CalculatorUtil.transposeIncrementallyToRowBased(columnData, missingDataPointsMap, timeIndexMap);

            // 5. 异步存储新数据
            if (!newlyCalculatedMap.isEmpty()) {
                CompletableFuture.runAsync(() -> {
                    try {
                        dataBaseService.insertData(newlyCalculatedMap, new ArrayList<>(newlyCalculatedMap.keySet()), dataSourceVo);
                    } catch (Exception e) {
                        log.error("异步存储增量计算的基础指标数据失败: {}", e.getMessage(), e);
                    }
                }, calculationPool);
            }

            // 6. 将新计算的结果合并回 allAvailableData
            mergeResults(allAvailableData, newlyCalculatedMap);

        } finally {
            // 在方法结束处添加性能监控
            long duration = System.currentTimeMillis() - startTime;
            monitorQueryPerformance("基础指标计算", duration, calculatedIndicators.size(),
                    allAvailableData.size());

            log.info("基础指标计算完成: 计算指标数量={}, 总耗时={}ms", calculatedIndicators.size(), duration);
        }
    }

    private Map<String, List<String>> classifyIndicators(List<String> indicators) {
        Map<String, List<String>> result = new HashMap<>();
        result.put(ConstantBase.BASE_ORI, new ArrayList<>());
        result.put(ConstantBase.BASE_CAL, new ArrayList<>());
        result.put(ConstantBase.STAIC, new ArrayList<>());

        for (String indicator : indicators) {
            if (isStaticProperty(indicator)) {
                result.get(ConstantBase.STAIC).add(indicator);
            } else {
                String formula = formulaCacheService.getFormula(indicator, ConstantBase.RULECOLID_COMMON);
                if (StringUtils.isBlank(formula)) {
                    result.get(ConstantBase.BASE_ORI).add(indicator);
                } else {
                    result.get(ConstantBase.BASE_CAL).add(indicator);
                }
            }
        }
        return result;
    }

    private void processAndMergeStaticProperties(
            List<String> staticPropertyIndicators,
            List<TimeWindow> timeWindows,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> targetMap) {

        Map<String, BigDecimal> staticValues = cacheTemplate.getAll(
                staticPropertyCache,
                (dataCode) -> "staticProp:" + dataCode,
                staticPropertyIndicators,
                Duration.ofMinutes(30),
                (uncachedIndicators) -> {
                    try {
                        String url = dataTwinsUrl + "/prop/all/rt";
                        JSONArray array = new JSONArray();
                        uncachedIndicators.forEach(array::add);
                        String result = HttpClientUtil.postJson(url, array);
                        JSONArray resultArray = CommonUtils.returnResultHandler(result);
                        Map<String, BigDecimal> apiResultMap = new HashMap<>();
                        resultArray.forEach(data -> {
                            JSONObject jsonObject = (JSONObject) data;
                            String dataCode = jsonObject.getString("dataCode");
                            String propVal = jsonObject.getString("propVal");
                            if (StrUtil.isNotBlank(propVal)) {
                                try {
                                    apiResultMap.put(dataCode, new BigDecimal(propVal));
                                } catch (NumberFormatException e) {
                                    log.warn("静态属性值 {} 格式错误: {}", dataCode, propVal);
                                }
                            }
                        });
                        return apiResultMap;
                    } catch (Exception e) {
                        log.error("通过API获取静态属性失败", e);
                        return Collections.emptyMap();
                    }
                }
        );

        staticValues.forEach((dataCode, value) -> {
            if (value != null) {
                ConcurrentHashMap<String, BigDecimal> timeValueMap = targetMap.computeIfAbsent(dataCode, k -> new ConcurrentHashMap<>());
                for (TimeWindow timeWindow : timeWindows) {
                    // 确保使用对齐后的时间戳
                    if(timeWindow.getAlignedTime() != null) {
                        timeValueMap.put(timeWindow.getAlignedTime(), value);
                    }
                }
            }
        });
    }

    private boolean isStaticProperty(String indicator) {
        try {
            String propClassifyCode = getPropClassifyCode(indicator);
            return "1".equals(propClassifyCode) || (indicator != null && indicator.contains("GJM"));
        } catch (Exception e) {
            log.warn("判断静态属性失败: {}", indicator, e);
            return false;
        }
    }

    private String getPropClassifyCode(String insDataCode) {
        if (insDataCode == null || insDataCode.length() < 60) return null;
        try {
            return (insDataCode.indexOf("_GL") != -1) ?
                    insDataCode.substring(54, 55) :
                    insDataCode.substring(59, 60);
        } catch (Exception e) {
            log.warn("获取属性分类代码失败: {}", insDataCode, e);
            return null;
        }
    }

    /**
     * 查询原始数据 - 简化版，专注于数据查询
     */
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> queryRawData(
            List<String> indicators,
            List<TimeWindow> originalTimeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 开始性能监控
        String queryId = performanceMonitor.startQuery("基础指标查询", indicators.size(),
                queryDTO.getStartTime() + " -> " + queryDTO.getEndTime());

        try {
            log.debug("开始查询原始数据: 指标数量={}, 时间窗口数量={}",
                    indicators.size(), originalTimeWindows.size());

            // 委托给 QueryPlanner 处理分片逻辑
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap =
                    queryPlanner.executePlan(indicators, queryDTO, dataSourceVo);

            // 对查询结果执行时间对齐（保持原有逻辑）
            if (!queryDTO.isEquallySpacedQuery()) {
                alignTimeStampsForIntervalQuery(resultMap, originalTimeWindows);
            }

            // 记录查询成功
            performanceMonitor.completeQuery(queryId, resultMap.size());

            log.info("原始数据查询完成: 指标数量={}, 结果集大小={}", indicators.size(), resultMap.size());

            return resultMap;

        } catch (Exception e) {
            // 记录查询失败
            performanceMonitor.failQuery(queryId, e.getMessage());
            log.error("查询原始数据失败: 指标数量={}", indicators.size(), e);
            throw new RuntimeException("查询原始数据失败", e);
        }
    }

    private Map<String, Set<String>> findMissingDataPoints(
            List<String> calculatedIndicators,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> existingDataMap,
            List<String> timePoints) {

        if (CollectionUtils.isEmpty(calculatedIndicators)) return Collections.emptyMap();

        Map<String, Set<String>> missingDataPointsMap = new HashMap<>();
        for (String indicator : calculatedIndicators) {
            Set<String> missingTimePointsForIndicator = new HashSet<>();
            Map<String, BigDecimal> timeValueMap = existingDataMap.get(indicator);
            if (timeValueMap == null) {
                missingTimePointsForIndicator.addAll(timePoints);
            } else {
                for (String timePoint : timePoints) {
                    if (!timeValueMap.containsKey(timePoint) || timeValueMap.get(timePoint) == null) {
                        missingTimePointsForIndicator.add(timePoint);
                    }
                }
            }
            if (!missingTimePointsForIndicator.isEmpty()) {
                missingDataPointsMap.put(indicator, missingTimePointsForIndicator);
            }
        }
        return missingDataPointsMap;
    }

    private void mergeResults(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> localResults) {

        if (localResults == null) return;
        localResults.forEach((indicator, timeValues) ->
                globalResults.merge(indicator, timeValues, (existingValues, newValues) -> {
                    existingValues.putAll(newValues);
                    return existingValues;
                })
        );
    }

    // --- 时间区间查询的对齐方法 ---

    private void alignTimeStampsForIntervalQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            List<TimeWindow> timeWindows) {
        if (CollectionUtils.isEmpty(timeWindows) || resultMap.isEmpty()) return;
        try {
            TimeWindow targetWindow = timeWindows.get(0);
            resultMap.entrySet().parallelStream().forEach(entry -> {
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    alignIndicatorTimeStampsForInterval(entry.getValue(), targetWindow.getStartTime(), targetWindow.getEndTime());
                }
            });
        } catch (Exception e) {
            log.error("时间区间查询时间戳对齐失败", e);
        }
    }

    private void alignIndicatorTimeStampsForInterval(
            ConcurrentHashMap<String, BigDecimal> timeValueMap,
            String targetStartTime,
            String targetEndTime) {
        BigDecimal startValue = findClosestValueForInterval(timeValueMap, targetStartTime, true);
        BigDecimal endValue = findClosestValueForInterval(timeValueMap, targetEndTime, false);
        timeValueMap.clear();
        if (startValue != null) timeValueMap.put(targetStartTime, startValue);
        if (endValue != null && !targetStartTime.equals(targetEndTime)) timeValueMap.put(targetEndTime, endValue);
    }

    private BigDecimal findClosestValueForInterval(
            Map<String, BigDecimal> timeValueMap,
            String targetTime,
            boolean preferEarlier) {
        try {
            LocalDateTime targetDateTime = LocalDateTime.parse(targetTime, DATE_TIME_FORMATTER);
            return timeValueMap.entrySet().stream()
                    .min(Comparator.comparingLong((Map.Entry<String, BigDecimal> entry) -> {
                        try {
                            return Math.abs(ChronoUnit.SECONDS.between(targetDateTime, LocalDateTime.parse(entry.getKey(), DATE_TIME_FORMATTER)));
                        } catch (Exception e) {
                            return Long.MAX_VALUE;
                        }
                    }).thenComparing((e1, e2) -> { // 时间差相同时，根据 preferEarlier 决定
                        if(preferEarlier) return e1.getKey().compareTo(e2.getKey());
                        else return e2.getKey().compareTo(e1.getKey());
                    }))
                    .map(Map.Entry::getValue).orElse(null);
        } catch (Exception e) {
            log.error("为时间区间查找最近值失败: targetTime={}", targetTime, e);
            return null;
        }
    }


    /**
     * 增强的性能监控
     */
    private void monitorQueryPerformance(String operation, long duration, int indicatorCount, int resultSize) {
        // 记录性能指标
        log.debug("查询性能监控: 操作={}, 耗时={}ms, 指标数={}, 结果数={}",
                operation, duration, indicatorCount, resultSize);

        // 检查慢查询
        if (calculationProperties.getPerformance().isEnableQueryMonitoring()) {
            long slowThreshold = calculationProperties.getPerformance().getSlowQueryThreshold();
            if (duration > slowThreshold) {
                log.warn("检测到慢查询: 操作={}, 耗时={}ms (阈值={}ms), 指标数={}",
                        operation, duration, slowThreshold, indicatorCount);
            }
        }
    }


}