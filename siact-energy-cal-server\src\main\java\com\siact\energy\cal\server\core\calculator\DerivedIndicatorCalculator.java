package com.siact.energy.cal.server.core.calculator;

import com.siact.energy.cal.common.pojo.dto.energycal.FormulaClass;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeUnitDto;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.enums.TimeQueryType;
import com.siact.energy.cal.common.pojo.enums.TimeUnit;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.common.util.utils.RateUtils;
import com.siact.energy.cal.server.common.utils.CalculatorUtil;
import com.siact.energy.cal.server.core.pojo.CalculationResult;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.pojo.YoYMoMParseResult;
import com.siact.energy.cal.server.core.service.FormulaCacheService;
import com.siact.energy.cal.server.core.executor.HighPerformanceDerivedExecutor;
import com.siact.energy.cal.server.core.calculator.CalculatorManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 🔥 衍生指标计算器 - 精简优化版
 *
 * 核心功能：
 * 1. 智能计算模式选择（传统模式 vs 高性能模式）
 * 2. 普通指标计算（公式计算）
 * 3. 同比环比指标计算（YoY/MoM）
 * 4. 列式计算优化
 */
@Slf4j
@Service
public class DerivedIndicatorCalculator {

    @Autowired
    private FormulaCacheService formulaCacheService;

    @Autowired
    private HighPerformanceDerivedExecutor highPerformanceExecutor;

    @Autowired
    private ApplicationContext applicationContext;

    private CalculatorManager calculatorManager;

    @PostConstruct
    public void init() {
        this.calculatorManager = applicationContext.getBean(CalculatorManager.class);
    }

    // 同比环比函数匹配模式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(YOY|MOM)\\(([^)]+)\\)");

    // 时间格式化器
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 专用线程池
    private final ForkJoinPool derivedCalculationPool = new ForkJoinPool(
            Math.min(Runtime.getRuntime().availableProcessors() * 2, 32));

    /**
     * 🔥 计算衍生指标 - 主入口方法
     */
    public CompletableFuture<Void> calculateDerivedIndicators(
            List<String> derivedIndicators,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            log.info("开始衍生指标计算，指标数量: {}", derivedIndicators.size());

            try {
                // 智能选择计算策略
                if (shouldUseHighPerformanceMode(derivedIndicators, timeWindows, queryDTO)) {
                    log.info("启用高性能计算模式");
                    highPerformanceExecutor.executeHighPerformanceCalculation(
                            derivedIndicators, timeWindows, queryDTO, globalResults).join();
                } else {
                    log.info("使用传统计算模式");
                    calculate(globalResults, derivedIndicators, timeWindows, queryDTO);
                }

                long duration = System.currentTimeMillis() - startTime;
                log.info("衍生指标计算完成，耗时: {}ms", duration);

            } catch (Exception e) {
                log.error("衍生指标计算失败", e);
            }
        }, derivedCalculationPool);
    }

    /**
     * 判断是否使用高性能模式
     */
    private boolean shouldUseHighPerformanceMode(List<String> derivedIndicators,
                                               List<TimeWindow> timeWindows,
                                               TimeQueryDTO queryDTO) {

        // 高性能模式触发条件
        boolean hasLargeIndicatorCount = derivedIndicators.size() > 50; // 超过50个指标
        boolean hasLongTimeSpan = timeWindows.size() > 100; // 超过100个时间窗口
        boolean hasComplexQuery = queryDTO.isEquallySpacedQuery() &&
                                 queryDTO.getInterval() != null &&
                                 queryDTO.getInterval() <= 5; // 高频查询

        // 系统资源充足时优先使用高性能模式
        boolean hasGoodSystemResources = Runtime.getRuntime().availableProcessors() >= 4 &&
                                       Runtime.getRuntime().freeMemory() > 500 * 1024 * 1024; // 500MB可用内存

        return (hasLargeIndicatorCount || hasLongTimeSpan || hasComplexQuery) && hasGoodSystemResources;
    }

    /**
     * 传统计算模式 - 核心计算逻辑
     */
    private void calculate(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            List<String> propList,
            List<TimeWindow> timeWindows,
            TimeQueryDTO timeQueryDTO) {

        // 1. 提取时间戳
        List<String> timePointList = extractTimePointsFromResultMap(resultMap);
        if (timePointList.isEmpty()) {
            log.warn("没有找到有效时间戳，衍生指标计算跳过");
            return;
        }

        // 2. 批量获取公式并排序
        Map<String, String> formulaMap = formulaCacheService.batchGetFormulas(propList, ConstantBase.RULECOLID_COMMON);

        List<FormulaClass> formulaList = propList.stream()
                .filter(targetProp -> StringUtils.isNotBlank(formulaMap.get(targetProp)))
                .map(targetProp -> new FormulaClass(targetProp, formulaMap.get(targetProp)))
                .collect(Collectors.toList());

        List<String> orderFormulaDevProp = FormulaUtils.calculateOrder(formulaList);

        // 3. 分类处理指标
        Map<String, Map<String, String>> yoyMomMap = new HashMap<>();
        List<String> normalIndicators = new ArrayList<>();

        for (String propCode : orderFormulaDevProp) {
            if (propList.contains(propCode)) {
                String formula = formulaMap.get(propCode);
                if (StringUtils.isNotBlank(formula)) {
                    if (formula.contains(ConstantBase.YOY) || formula.contains(ConstantBase.MOM)) {
                        // 同比环比指标
                        YoYMoMParseResult parseResult = parseYoYMoMFormula(formula);
                        if (parseResult != null) {
                            yoyMomMap.computeIfAbsent(parseResult.getFunctionName(), k -> new HashMap<>())
                                    .put(propCode, parseResult.getTargetProperty());
                        }
                    } else {
                        // 普通指标
                        normalIndicators.add(propCode);
                    }
                }
            }
        }

        // 4. 处理普通指标
        if (!normalIndicators.isEmpty()) {
            processNormalIndicators(normalIndicators, timePointList, timeQueryDTO, resultMap, formulaMap);
        }

        // 5. 批量处理同比环比
        if (!yoyMomMap.isEmpty()) {
            batchProcessYoYMoM(yoyMomMap, timeQueryDTO, resultMap, timeWindows, timePointList);
        }

        log.debug("衍生指标计算完成: 普通指标={}, 同比环比指标={}",
                normalIndicators.size(), yoyMomMap.size());
    }

    /**
     * 处理普通指标
     */
    private void processNormalIndicators(List<String> normalIndicators,
                                         List<String> timePoints,
                                         TimeQueryDTO queryDTO,
                                         ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                         Map<String, String> formulaMap) {

        if (timePoints.isEmpty()) return;

        // 1. 准备工作：在循环外只做一次
        Long duration = calculateDurationInSeconds(queryDTO);
        Map<String, Integer> timeIndexMap = CalculatorUtil.createTimeIndexMap(timePoints);

        // 2. 数据转置: 将 resultMap (行式) 转换为 columnData (列式)
        // 这是列式计算的核心前提，它将所有需要用到的依赖数据按列组织好
        Map<String, BigDecimal[]> columnData = CalculatorUtil.transposeToColumnar(resultMap, timePoints, timeIndexMap);

        // 3. 按拓扑序，逐个指标进行列式计算
        for (String propCode : normalIndicators) {
            String originalFormula = formulaMap.get(propCode);
            // (理论上不应为空，因为列表是基于 formulaMap 生成的，做个安全检查)
            if (originalFormula == null) continue;

            String formula = originalFormula;
            if (duration != null && formula.contains(ConstantBase.DURATION)) {
                formula = formula.replace(ConstantBase.DURATION, duration.toString());
            }

            List<String> varList = FormulaUtils.getVarList(formula);

            // a. 准备输入列
            Map<String, BigDecimal[]> dependencyColumns = new HashMap<>();
            boolean dependenciesMet = true;
            for (String var : varList) {
                BigDecimal[] depCol = columnData.get(var);
                if (depCol == null) {
                    log.warn("衍生指标 {} 计算中断，因为其依赖 {} 的数据列不存在。", propCode, var);
                    dependenciesMet = false;
                    break;
                }
                dependencyColumns.put(var, depCol);
            }
            if (!dependenciesMet) continue;

            // b. 创建输出列并进行高效的列式计算
            BigDecimal[] resultColumn = new BigDecimal[timePoints.size()];
            Map<String, BigDecimal> tempValueMap = new HashMap<>(varList.size()); // 循环外创建，循环内复用

            for (int i = 0; i < timePoints.size(); i++) {
                tempValueMap.clear();
                boolean dataPointComplete = true;
                for (String var : varList) {
                    BigDecimal value = dependencyColumns.get(var)[i];
                    if (value == null) {
                        dataPointComplete = false;
                        break;
                    }
                    tempValueMap.put(var, value);
                }

                if (dataPointComplete) {
                    try {
                        resultColumn[i] = FormulaUtils.calcFormula(formula, tempValueMap);
                    } catch (Exception e) {
                        log.error("衍生指标 {} 在时间点 {} 计算失败: {}", propCode, timePoints.get(i), e.getMessage());
                    }
                }
            }

            // c. 🔥 将新计算出的结果列加入“列数据库”，供后续指标使用
            columnData.put(propCode, resultColumn);
        }

        // 4. 🔥 数据反转: 将所有新计算出的列，一次性地写回行式的 resultMap
        CalculatorUtil.mergeColumnarToRowBased(normalIndicators, columnData, timePoints, resultMap);
    }

    /**
     * 批量处理同比环比计算
     */
    private void batchProcessYoYMoM(Map<String, Map<String, String>> yoyMomMap,
                                    TimeQueryDTO timeQueryDTO,
                                    ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                    List<TimeWindow> timeWindows,
                                    List<String> timePointList) {
        try {
            log.debug("开始批量处理同比环比: 数量={}", yoyMomMap.size());

            // 1. 按函数类型分组收集目标指标
            Map<String, Set<String>> functionTargetMap = groupTargetsByFunction(yoyMomMap);

            // 2. 批量查询基期数据
            batchQueryBasePeriodDataForYoYMoM(functionTargetMap, timeQueryDTO, resultMap, timeWindows);

            // 3. 批量计算同比环比值
            batchCalculateYoYMoMForDerived(yoyMomMap, timePointList, timeQueryDTO, resultMap);

            log.debug("批量处理同比环比完成");

        } catch (Exception e) {
            log.error("批量同比环比计算失败", e);
        }
    }

    /**
     * 按函数类型分组收集目标指标
     */
    private Map<String, Set<String>> groupTargetsByFunction(Map<String, Map<String, String>> yoyMomMap) {
        Map<String, Set<String>> functionTargetMap = new HashMap<>();

        for (Map.Entry<String, Map<String, String>> entry : yoyMomMap.entrySet()) {
            String functionName = entry.getKey();
            Map<String, String> derivedToTargetMap = entry.getValue();

            // 收集所有目标指标（去重）
            Set<String> targetProperties = new HashSet<>(derivedToTargetMap.values());
            functionTargetMap.put(functionName, targetProperties);
        }

        log.debug("函数类型分组结果: {}", functionTargetMap);
        return functionTargetMap;
    }

    /**
     * 批量查询基期数据
     */
    private void batchQueryBasePeriodDataForYoYMoM(Map<String, Set<String>> functionTargetMap,
                                                   TimeQueryDTO timeQueryDTO,
                                                   ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                   List<TimeWindow> timeWindows) {

        for (Map.Entry<String, Set<String>> entry : functionTargetMap.entrySet()) {
            String functionName = entry.getKey();
            Set<String> targetProperties = entry.getValue();

            try {
                log.debug("开始查询{}基期数据: 指标数量={}", functionName, targetProperties.size());

                // 1. 计算基期时间范围
                TimeRange basePeriodRange = calculateBasePeriodRange(timeQueryDTO, functionName);
                if (basePeriodRange == null) {
                    log.warn("基期时间范围计算失败: functionName={}", functionName);
                    continue;
                }

                // 2. 生成时间映射关系
                Map<String, String> tsMap = generateTimeStampMapping(timeQueryDTO, functionName, timeWindows);

                // 3. 构建基期查询参数
                TimeQueryDTO basePeriodQuery = createBasePeriodQueryDTO(basePeriodRange, timeQueryDTO, new ArrayList<>(targetProperties));

                // 4. 调用CalculateService进行基期数据计算
                CalculatorManager calculateService = applicationContext.getBean(CalculatorManager.class);
                CompletableFuture<CalculationResult> calculationResultCompletableFuture;

                if(TimeQueryType.EQUALLY_SPACED.equals(basePeriodQuery.getQueryType())){
                    calculationResultCompletableFuture = calculateService.calculateEquallySpacedTimeData(basePeriodQuery);
                }else if(TimeQueryType.INTERVAL.equals(basePeriodQuery.getQueryType())){
                    calculationResultCompletableFuture = calculateService.calculateIntervalTimeData(basePeriodQuery);
                }else{
                    log.warn("不支持的查询类型: {}", basePeriodQuery.getQueryType());
                    continue;
                }

                CalculationResult calculationResult = calculationResultCompletableFuture.get(60, java.util.concurrent.TimeUnit.SECONDS);
                ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> baseResults = calculationResult.getGlobalResults();

                // 5. 存储基期数据到resultMap中，使用时间戳映射进行转换
                storeBasePeriodDataWithTimeMapping(baseResults, targetProperties, functionName, tsMap, resultMap);

                log.debug("{}基期数据查询完成: 指标数量={}", functionName, targetProperties.size());

            } catch (Exception e) {
                log.error("查询{}基期数据失败", functionName, e);
            }
        }
    }

    /**
     * 批量计算同比环比值
     */
    private void batchCalculateYoYMoMForDerived(Map<String, Map<String, String>> yoyMomMap,
                                                List<String> timePoints,
                                                TimeQueryDTO timeQueryDTO,
                                                ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap) {

        for (Map.Entry<String, Map<String, String>> functionEntry : yoyMomMap.entrySet()) {
            String functionName = functionEntry.getKey();
            Map<String, String> derivedToTargetMap = functionEntry.getValue();

            // 遍历每个衍生指标
            for (Map.Entry<String, String> derivedEntry : derivedToTargetMap.entrySet()) {
                String derivedIndicatorCode = derivedEntry.getKey();
                String targetProperty = derivedEntry.getValue();

                for (String currentTimestamp : timePoints) {
                    try {
                        // 1. 计算基期时间戳
                        String basePeriodTimestamp = calculateBasePeriodTimestamp(currentTimestamp, functionName, timeQueryDTO);
                        if (basePeriodTimestamp == null) {
                            continue;
                        }

                        // 2. 获取当期数据
                        BigDecimal currentValue = getPropTsValue(resultMap, targetProperty, currentTimestamp);

                        // 3. 获取基期数据
                        String basePeriodKey = targetProperty + functionName;
                        BigDecimal basePeriodValue = getPropTsValue(resultMap, basePeriodKey, basePeriodTimestamp);

                        // 4. 计算同比环比
                        BigDecimal result = calculateYoYMoMValue(currentValue, basePeriodValue, functionName);

                        // 5. 存储结果
                        if (result != null) {
                            resultMap.computeIfAbsent(derivedIndicatorCode, k -> new ConcurrentHashMap<>())
                                    .put(currentTimestamp, result);

                            log.debug("{}计算完成: 衍生指标={}, 目标指标={}, 当期值={}, 基期值={}, 结果={}%",
                                    functionName, derivedIndicatorCode, targetProperty, currentValue, basePeriodValue, result);
                        }

                    } catch (Exception e) {
                        log.error("计算{}值失败: 衍生指标={}, 目标指标={}, timestamp={}",
                                functionName, derivedIndicatorCode, targetProperty, currentTimestamp, e);
                    }
                }
            }
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 解析同比环比公式
     */
    private YoYMoMParseResult parseYoYMoMFormula(String formula) {
        if (StringUtils.isBlank(formula)) {
            return null;
        }

        Matcher matcher = FUNCTION_PATTERN.matcher(formula);
        if (matcher.find()) {
            String functionName = matcher.group(1);
            String targetProperty = matcher.group(2).trim();
            return new YoYMoMParseResult(functionName, targetProperty);
        }

        return null;
    }

    /**
     * 计算基期时间范围
     */
    private TimeRange calculateBasePeriodRange(TimeQueryDTO timeQueryDTO, String functionName) {
        try {
            TimeUnitDto timeUnitDto = new TimeUnitDto();
            timeUnitDto.setStartTime(timeQueryDTO.getStartTime());
            timeUnitDto.setEndTime(timeQueryDTO.getEndTime());
            timeUnitDto.setInterval(timeQueryDTO.getInterval() != null ? timeQueryDTO.getInterval() : 1);
            timeUnitDto.setTimeUnit(TimeUnit.fromString(timeQueryDTO.getTsUnit()));

            TimeUnitDto basePeriod = RateUtils.getBasePeriod(timeUnitDto, functionName);
            if (basePeriod != null) {
                return new TimeRange(basePeriod.getStartTime(), basePeriod.getEndTime());
            }
        } catch (Exception e) {
            log.error("计算基期时间范围失败: functionName={}", functionName, e);
        }
        return null;
    }

    /**
     * 生成时间戳映射关系
     */
    private Map<String, String> generateTimeStampMapping(TimeQueryDTO timeQueryDTO, String functionName, List<TimeWindow> timeWindows) {
        Map<String, String> tsMap = new HashMap<>();

        try {
            for (TimeWindow timeWindow : timeWindows) {
                TimeUnitDto timeUnitDtoSub = new TimeUnitDto();
                timeUnitDtoSub.setStartTime(timeWindow.getStartTime());
                timeUnitDtoSub.setEndTime(timeWindow.getEndTime());
                timeUnitDtoSub.setInterval(timeQueryDTO.getInterval() != null ? timeQueryDTO.getInterval() : 1);
                timeUnitDtoSub.setTimeUnit(TimeUnit.fromString(timeQueryDTO.getTsUnit()));

                TimeUnitDto basePeriodSubBase = RateUtils.getBasePeriod(timeUnitDtoSub, functionName);

                tsMap.put(basePeriodSubBase.getStartTime(), timeWindow.getAlignedTime());
            }

            log.debug("时间戳映射生成完成: functionName={}, 映射数量={}", functionName, tsMap.size());

        } catch (Exception e) {
            log.error("生成时间戳映射失败: functionName={}", functionName, e);
        }

        return tsMap;
    }

    /**
     * 创建基期查询参数
     */
    private TimeQueryDTO createBasePeriodQueryDTO(TimeRange basePeriodRange, TimeQueryDTO originalQuery, List<String> targetProperties) {
        TimeQueryDTO basePeriodQuery = new TimeQueryDTO();
        basePeriodQuery.setDataCodes(targetProperties);
        basePeriodQuery.setStartTime(basePeriodRange.getStartTime());
        basePeriodQuery.setEndTime(basePeriodRange.getEndTime());
        basePeriodQuery.setInterval(originalQuery.getInterval());
        basePeriodQuery.setTsUnit(originalQuery.getTsUnit());
        basePeriodQuery.setQueryType(originalQuery.getQueryType());
        return basePeriodQuery;
    }

    /**
     * 存储基期数据到resultMap中，使用时间戳映射进行转换
     */
    private void storeBasePeriodDataWithTimeMapping(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> basePeriodResults,
            Set<String> targetProperties,
            String functionName,
            Map<String, String> tsMap,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap) {

        for (String targetProperty : targetProperties) {
            ConcurrentHashMap<String, BigDecimal> basePeriodData = basePeriodResults.get(targetProperty);

            if (basePeriodData != null && !basePeriodData.isEmpty()) {
                String basePeriodKey = targetProperty + functionName;
                ConcurrentHashMap<String, BigDecimal> convertedTimeValueMap = new ConcurrentHashMap<>();

                for (Map.Entry<String, BigDecimal> entry : basePeriodData.entrySet()) {
                    String basePeriodTimestamp = entry.getKey();
                    BigDecimal value = entry.getValue();

                    String currentTimestamp = tsMap.get(basePeriodTimestamp);
                    if (currentTimestamp != null) {
                        convertedTimeValueMap.put(currentTimestamp, value);
                    } else {
                        convertedTimeValueMap.put(basePeriodTimestamp, value);
                        log.debug("未找到时间戳映射，使用原时间戳: {}", basePeriodTimestamp);
                    }
                }

                resultMap.put(basePeriodKey, convertedTimeValueMap);
                log.debug("基期数据存储成功: key={}, 原始数据量={}, 转换后数据量={}",
                        basePeriodKey, basePeriodData.size(), convertedTimeValueMap.size());
            } else {
                log.warn("未找到基期数据: targetProperty={}, functionName={}", targetProperty, functionName);
            }
        }
    }

    /**
     * 计算基期时间戳
     */
    private String calculateBasePeriodTimestamp(String currentTimestamp, String functionName, TimeQueryDTO timeQueryDTO) {
        // 这里直接返回当期时间戳，因为基期数据已经通过tsMap转换为当期时间戳存储
        return currentTimestamp;
    }

    /**
     * 计算同比环比值
     */
    private BigDecimal calculateYoYMoMValue(BigDecimal currentValue, BigDecimal basePeriodValue, String functionName) {
        if (currentValue == null || basePeriodValue == null || basePeriodValue.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        try {
            // 计算增长率：(当期值 - 基期值) / 基期值 * 100
            BigDecimal difference = currentValue.subtract(basePeriodValue);
            BigDecimal rate = difference.divide(basePeriodValue, 4, RoundingMode.HALF_UP);
            return rate.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("计算{}值失败: currentValue={}, basePeriodValue={}", functionName, currentValue, basePeriodValue, e);
            return null;
        }
    }

    /**
     * 获取指标时间戳对应的值
     */
    private BigDecimal getPropTsValue(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                      String prop, String timestamp) {
        ConcurrentHashMap<String, BigDecimal> propData = resultMap.get(prop);
        if (propData != null) {
            return propData.get(timestamp);
        }
        return null;
    }

    /**
     * 从resultMap中提取时间戳
     */
    private List<String> extractTimePointsFromResultMap(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap) {
        Set<String> timePointSet = new HashSet<>();

        resultMap.forEach((indicator, timeValueMap) -> {
            if (!indicator.contains("YOY") && !indicator.contains("MOM")) {
                timePointSet.addAll(timeValueMap.keySet());
            }
        });

        List<String> timePoints = new ArrayList<>(timePointSet);
        timePoints.sort(String::compareTo);

        return timePoints;
    }

    /**
     * 查找最近的值（用于区间查询）
     */
    private BigDecimal findNearestValue(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                        String prop, String targetTimestamp, String endTime) {
        ConcurrentHashMap<String, BigDecimal> propData = resultMap.get(prop);
        if (propData == null || propData.isEmpty()) {
            return null;
        }

        try {
            LocalDateTime target = LocalDateTime.parse(targetTimestamp, DATE_TIME_FORMATTER);

            return propData.entrySet().stream()
                    .min((e1, e2) -> {
                        LocalDateTime t1 = LocalDateTime.parse(e1.getKey(), DATE_TIME_FORMATTER);
                        LocalDateTime t2 = LocalDateTime.parse(e2.getKey(), DATE_TIME_FORMATTER);
                        long diff1 = Math.abs(java.time.temporal.ChronoUnit.SECONDS.between(target, t1));
                        long diff2 = Math.abs(java.time.temporal.ChronoUnit.SECONDS.between(target, t2));
                        return Long.compare(diff1, diff2);
                    })
                    .map(Map.Entry::getValue)
                    .orElse(null);
        } catch (Exception e) {
            log.error("查找最近值失败: prop={}, targetTimestamp={}", prop, targetTimestamp, e);
            return null;
        }
    }

    /**
     * 【最终版】根据查询模式计算 DURATION 的值，并统一返回秒数。
     *
     * @param queryDTO 查询DTO，包含查询模式、起止时间和步长信息。
     * @return 计算出的时长（单位：秒）。如果无法计算，则返回 null。
     */
    private Long calculateDurationInSeconds(TimeQueryDTO queryDTO) {
        try {
            // 模式一：时间区间查询 (isEquallySpacedQuery() is false)
            if (!queryDTO.isEquallySpacedQuery()) {
                // 检查起止时间是否存在
                if (StringUtils.isNoneBlank(queryDTO.getStartTime(), queryDTO.getEndTime())) {
                    LocalDateTime startTime = LocalDateTime.parse(queryDTO.getStartTime(), DATE_TIME_FORMATTER);
                    LocalDateTime endTime = LocalDateTime.parse(queryDTO.getEndTime(), DATE_TIME_FORMATTER);
                    // 返回整个时间区间的总秒数
                    return ChronoUnit.SECONDS.between(startTime, endTime);
                } else {
                    log.warn("时间区间查询模式下，起止时间为空，无法计算 DURATION。");
                    return null;
                }
            }

            // 模式二：等时间间隔查询 (isEquallySpacedQuery() is true)
            else {
                Integer interval = queryDTO.getInterval();
                String tsUnit = queryDTO.getTsUnit();

                // 检查步长和单位是否存在
                if (interval == null || StringUtils.isBlank(tsUnit)) {
                    log.warn("等时间间隔查询模式下，interval或tsUnit为空，无法计算 DURATION。");
                    return null;
                }

                // 将单个步长转换为秒数
                switch (tsUnit.toLowerCase()) {
                    case "m":
                        return interval * 60L;
                    case "h":
                        return interval * 3600L;
                    case "d":
                        return interval * 86400L;
                    case "n": // 月和年做近似处理
                        return interval * 86400L * 30; // 按30天计
                    case "y":
                        return interval * 86400L * 365; // 按365天计
                    default:
                        log.warn("不支持的tsUnit: [{}]，无法计算 DURATION。", tsUnit);
                        return null;
                }
            }
        } catch (Exception e) {
            log.error("计算 DURATION 失败", e);
            return null;
        }
    }

    // ==================== 内部类 ====================

    /**
     * 时间范围类
     */
    private static class TimeRange {
        private final String startTime;
        private final String endTime;

        public TimeRange(String startTime, String endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public String getStartTime() { return startTime; }
        public String getEndTime() { return endTime; }
    }

    /**
     * 销毁方法
     */
    public void destroy() {
        if (derivedCalculationPool != null && !derivedCalculationPool.isShutdown()) {
            derivedCalculationPool.shutdown();
            try {
                if (!derivedCalculationPool.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    derivedCalculationPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                derivedCalculationPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
