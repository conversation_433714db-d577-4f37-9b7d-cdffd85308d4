package com.siact.energy.cal.common.pojo.vo.ruleDetail;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * 指标详情表(RuleDetail) VO
 *
 * <AUTHOR>
 * @since 2024-05-21 10:07:00
 */
@ApiModel("指标详情表VO")
@Data
public class RuleDetailVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    private Long id;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", position = 2)
    private String ruleName;

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述", position = 3)
    private String ruleDes;

    /**
     * 指标类型（0-实例级规则，1-模型级规则）
     */
    @ApiModelProperty(value = "指标类型（0-实例级规则，1-模型级规则）", position = 4)
    private Integer ruleType;

    /**
     * 计算类型（0-数值计算，1-逻辑计算，2-正则表达式）
     */
    @ApiModelProperty(value = "计算类型（0-数值计算，1-逻辑计算，2-正则表达式）", position = 5)
    private Integer calType;

    /**
     * 节点（模型）编码
     */
    @ApiModelProperty(value = "节点（模型）编码", position = 6)
    private String devCode;

    /**
     * 节点（模型）名称
     */
    @ApiModelProperty(value = "节点（模型）名称", position = 7)
    private String devName;

    /**
     * 属性编码，模型编码
     */
    @ApiModelProperty(value = "属性编码，模型编码", position = 8)
    private String devProperty;

    /**
     * 属性（模型）名称
     */
    @ApiModelProperty(value = "属性（模型）名称", position = 9)
    private String propName;

    /**
     * 公式表达式
     */
    @ApiModelProperty(value = "公式表达式", position = 10)
    private List<String> ruleFormula;

    /**
     * 公式表达式
     */
    @ApiModelProperty(value = "公式表达式", position = 11)
    private List<String> ruleFormulaShow;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", position = 12)
    private Long projectId;

    /**
     * 所属规则集id
     */
    @ApiModelProperty(value = "所属规则集id", position = 13)
    private Long ruleColId;

    /**
     * 所属规则集
     */
    @ApiModelProperty(value = "所属指标集", position = 13)
    private String ruleColName;

    /**
     * 是否激活（0-激活,1-未激活）
     */
    @ApiModelProperty(value = "是否激活（0-激活,1-未激活）", position = 14)
    private Integer activeState;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", position = 15)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 16)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}

