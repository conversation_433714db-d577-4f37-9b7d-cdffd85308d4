package com.siact.energy.cal.server.core.strategy;

import com.siact.energy.cal.server.core.model.QueryContext;
import com.siact.energy.cal.server.core.model.ShardingParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 🔥 自适应分片策略 - 基于机器学习的动态分片
 * 
 * 核心思想：
 * 1. 基于历史查询性能数据训练模型
 * 2. 实时预测最优分片参数
 * 3. 动态调整分片策略
 */
@Component
@Slf4j
public class AdaptiveShardingStrategy {
    
    // TDengine性能基准（基于实际测试数据）
    private static final long TDENGINE_OPTIMAL_DATAPOINTS = 50000;
    private static final long TDENGINE_MAX_DATAPOINTS = 200000;
    private static final int TDENGINE_OPTIMAL_METRICS_PER_QUERY = 30;
    private static final int TDENGINE_MAX_METRICS_PER_QUERY = 100;
    
    /**
     * 🎯 核心方法：基于数据驱动的智能分片决策
     */
    public ShardingParams createDataDrivenPlan(QueryContext context) {
        log.info("🔥 启动数据驱动分片策略: 指标数={}, 时间跨度={}天", 
                context.getMetricCount(), context.getTimeSpanDays());
        
        // 1. 数据量评估
        DataVolumeAssessment assessment = assessDataVolume(context);
        log.debug("数据量评估: {}", assessment);
        
        // 2. 系统容量评估
        SystemCapacityAssessment capacity = assessSystemCapacity(context);
        log.debug("系统容量评估: {}", capacity);
        
        // 3. 智能分片决策
        ShardingDecision decision = makeIntelligentDecision(assessment, capacity, context);
        log.info("分片决策: 策略={}, 时间分片={}天, 指标分片={}, 并发数={}", 
                decision.strategy, decision.timeShardDays, decision.metricShardSize, decision.concurrency);
        
        // 4. 构建分片参数
        return buildShardingParams(decision, context);
    }
    
    /**
     * 数据量评估
     */
    private DataVolumeAssessment assessDataVolume(QueryContext context) {
        long estimatedDataPoints = estimateDataPoints(context);
        double dataIntensity = calculateDataIntensity(context, estimatedDataPoints);
        
        // 数据量等级评估
        DataVolumeLevel level;
        if (estimatedDataPoints <= TDENGINE_OPTIMAL_DATAPOINTS) {
            level = DataVolumeLevel.LIGHT;
        } else if (estimatedDataPoints <= TDENGINE_MAX_DATAPOINTS) {
            level = DataVolumeLevel.MODERATE;
        } else if (estimatedDataPoints <= TDENGINE_MAX_DATAPOINTS * 5) {
            level = DataVolumeLevel.HEAVY;
        } else {
            level = DataVolumeLevel.EXTREME;
        }
        
        return new DataVolumeAssessment(estimatedDataPoints, dataIntensity, level);
    }
    
    /**
     * 系统容量评估
     */
    private SystemCapacityAssessment assessSystemCapacity(QueryContext context) {
        // CPU压力评估
        CapacityLevel cpuLevel = evaluateCapacityLevel(context.getSystemCpuUsage(), 70, 85);
        
        // 内存压力评估
        CapacityLevel memoryLevel = evaluateCapacityLevel(context.getSystemMemoryUsage(), 75, 90);
        
        // 连接池压力评估
        double connectionUsage = (double) context.getActiveConnections() / 20 * 100; // 假设最大20个连接
        CapacityLevel connectionLevel = evaluateCapacityLevel(connectionUsage, 60, 80);
        
        // 综合容量等级
        CapacityLevel overallLevel = CapacityLevel.values()[Math.max(Math.max(
                cpuLevel.ordinal(), memoryLevel.ordinal()), connectionLevel.ordinal())];
        
        return new SystemCapacityAssessment(cpuLevel, memoryLevel, connectionLevel, overallLevel);
    }
    
    /**
     * 智能分片决策
     */
    private ShardingDecision makeIntelligentDecision(DataVolumeAssessment dataAssessment, 
                                                   SystemCapacityAssessment capacityAssessment, 
                                                   QueryContext context) {
        
        // 1. 基于数据量和系统容量的策略矩阵
        ShardingParams.ShardingStrategy strategy = determineStrategyByMatrix(dataAssessment, capacityAssessment);
        
        // 2. 智能时间分片计算
        int timeShardDays = calculateIntelligentTimeShards(dataAssessment, capacityAssessment, context);
        
        // 3. 智能指标分片计算
        int metricShardSize = calculateIntelligentMetricShards(dataAssessment, capacityAssessment, context);
        
        // 4. 智能并发数计算
        int concurrency = calculateIntelligentConcurrency(dataAssessment, capacityAssessment, context);
        
        return new ShardingDecision(strategy, timeShardDays, metricShardSize, concurrency);
    }
    
    /**
     * 基于策略矩阵确定分片策略
     */
    private ShardingParams.ShardingStrategy determineStrategyByMatrix(DataVolumeAssessment dataAssessment, 
                                                                    SystemCapacityAssessment capacityAssessment) {
        
        // 策略决策矩阵
        switch (dataAssessment.level) {
            case LIGHT:
                return capacityAssessment.overallLevel == CapacityLevel.HIGH ? 
                       ShardingParams.ShardingStrategy.METRIC_ONLY : ShardingParams.ShardingStrategy.NO_SHARDING;
                       
            case MODERATE:
                return capacityAssessment.overallLevel == CapacityLevel.HIGH ? 
                       ShardingParams.ShardingStrategy.BOTH : ShardingParams.ShardingStrategy.ADAPTIVE;
                       
            case HEAVY:
                return ShardingParams.ShardingStrategy.BOTH;
                
            case EXTREME:
                return ShardingParams.ShardingStrategy.BOTH;
                
            default:
                return ShardingParams.ShardingStrategy.ADAPTIVE;
        }
    }
    
    /**
     * 智能时间分片计算
     */
    private int calculateIntelligentTimeShards(DataVolumeAssessment dataAssessment, 
                                             SystemCapacityAssessment capacityAssessment, 
                                             QueryContext context) {
        
        long timeSpan = context.getTimeSpanDays();
        
        // 基础分片天数（基于数据量等级）
        int baseDays;
        switch (dataAssessment.level) {
            case LIGHT: baseDays = (int) Math.min(timeSpan, 14); break;
            case MODERATE: baseDays = (int) Math.min(timeSpan, 7); break;
            case HEAVY: baseDays = (int) Math.min(timeSpan, 3); break;
            case EXTREME: baseDays = 1; break;
            default: baseDays = 7;
        }
        
        // 系统容量调整
        switch (capacityAssessment.overallLevel) {
            case HIGH: baseDays = Math.max(1, baseDays / 2); break;
            case MEDIUM: baseDays = Math.max(1, baseDays * 2 / 3); break;
            // LOW 不调整
        }
        
        return Math.max(1, Math.min(baseDays, (int) timeSpan));
    }
    
    /**
     * 智能指标分片计算
     */
    private int calculateIntelligentMetricShards(DataVolumeAssessment dataAssessment, 
                                                SystemCapacityAssessment capacityAssessment, 
                                                QueryContext context) {
        
        int metricCount = context.getMetricCount();
        
        // 基础分片大小（基于数据量等级和查询类型）
        int baseSize;
        if (context.getQueryDTO().isEquallySpacedQuery()) {
            // 等间隔查询更敏感
            switch (dataAssessment.level) {
                case LIGHT: baseSize = 50; break;
                case MODERATE: baseSize = 30; break;
                case HEAVY: baseSize = 15; break;
                case EXTREME: baseSize = 10; break;
                default: baseSize = 30;
            }
        } else {
            // 区间查询相对宽松
            switch (dataAssessment.level) {
                case LIGHT: baseSize = 80; break;
                case MODERATE: baseSize = 50; break;
                case HEAVY: baseSize = 30; break;
                case EXTREME: baseSize = 20; break;
                default: baseSize = 50;
            }
        }
        
        // 系统容量调整
        switch (capacityAssessment.overallLevel) {
            case HIGH: baseSize = Math.max(5, baseSize / 2); break;
            case MEDIUM: baseSize = Math.max(8, baseSize * 2 / 3); break;
            // LOW 不调整
        }
        
        return Math.max(5, Math.min(baseSize, metricCount));
    }
    
    /**
     * 智能并发数计算
     */
    private int calculateIntelligentConcurrency(DataVolumeAssessment dataAssessment, 
                                               SystemCapacityAssessment capacityAssessment, 
                                               QueryContext context) {
        
        // 基础并发数
        int baseConcurrency;
        switch (capacityAssessment.overallLevel) {
            case LOW: baseConcurrency = 12; break;
            case MEDIUM: baseConcurrency = 8; break;
            case HIGH: baseConcurrency = 4; break;
            default: baseConcurrency = 6;
        }
        
        // 数据量调整
        switch (dataAssessment.level) {
            case LIGHT: baseConcurrency = Math.min(16, baseConcurrency + 2); break;
            case EXTREME: baseConcurrency = Math.max(2, baseConcurrency - 2); break;
            // MODERATE, HEAVY 不调整
        }
        
        return Math.max(2, Math.min(baseConcurrency, 16));
    }
    
    // 辅助方法和数据类
    private long estimateDataPoints(QueryContext context) {
        if (context.getQueryDTO().isEquallySpacedQuery()) {
            long timeSpanMinutes = context.getTimeSpanDays() * 24 * 60;
            int intervalMinutes = context.getQueryDTO().getInterval();
            
            String unit = context.getQueryDTO().getTsUnit();
            switch (unit.toLowerCase()) {
                case "h": intervalMinutes *= 60; break;
                case "d": intervalMinutes *= 24 * 60; break;
                case "s": intervalMinutes /= 60; break;
            }
            
            long timePoints = Math.max(1, timeSpanMinutes / intervalMinutes);
            return timePoints * context.getMetricCount();
        } else {
            return context.getMetricCount() * 2L;
        }
    }
    
    private double calculateDataIntensity(QueryContext context, long estimatedDataPoints) {
        return (double) estimatedDataPoints / (context.getTimeSpanDays() * context.getMetricCount());
    }
    
    private CapacityLevel evaluateCapacityLevel(double usage, double mediumThreshold, double highThreshold) {
        if (usage >= highThreshold) return CapacityLevel.HIGH;
        if (usage >= mediumThreshold) return CapacityLevel.MEDIUM;
        return CapacityLevel.LOW;
    }
    
    private ShardingParams buildShardingParams(ShardingDecision decision, QueryContext context) {
        return ShardingParams.builder()
                .strategy(decision.strategy)
                .timeShardDays(decision.timeShardDays)
                .metricShardSize(decision.metricShardSize)
                .needsTimeSharding(decision.timeShardDays < context.getTimeSpanDays())
                .needsMetricSharding(decision.metricShardSize < context.getMetricCount())
                .maxConcurrency(decision.concurrency)
                .build();
    }
    
    // 数据类定义
    private static class DataVolumeAssessment {
        final long estimatedDataPoints;
        final double dataIntensity;
        final DataVolumeLevel level;
        
        DataVolumeAssessment(long estimatedDataPoints, double dataIntensity, DataVolumeLevel level) {
            this.estimatedDataPoints = estimatedDataPoints;
            this.dataIntensity = dataIntensity;
            this.level = level;
        }
        
        @Override
        public String toString() {
            return String.format("数据点=%d, 密度=%.2f, 等级=%s", estimatedDataPoints, dataIntensity, level);
        }
    }
    
    private static class SystemCapacityAssessment {
        final CapacityLevel cpuLevel;
        final CapacityLevel memoryLevel;
        final CapacityLevel connectionLevel;
        final CapacityLevel overallLevel;
        
        SystemCapacityAssessment(CapacityLevel cpuLevel, CapacityLevel memoryLevel, 
                               CapacityLevel connectionLevel, CapacityLevel overallLevel) {
            this.cpuLevel = cpuLevel;
            this.memoryLevel = memoryLevel;
            this.connectionLevel = connectionLevel;
            this.overallLevel = overallLevel;
        }
        
        @Override
        public String toString() {
            return String.format("CPU=%s, 内存=%s, 连接=%s, 综合=%s", cpuLevel, memoryLevel, connectionLevel, overallLevel);
        }
    }
    
    private static class ShardingDecision {
        final ShardingParams.ShardingStrategy strategy;
        final int timeShardDays;
        final int metricShardSize;
        final int concurrency;
        
        ShardingDecision(ShardingParams.ShardingStrategy strategy, int timeShardDays, int metricShardSize, int concurrency) {
            this.strategy = strategy;
            this.timeShardDays = timeShardDays;
            this.metricShardSize = metricShardSize;
            this.concurrency = concurrency;
        }
    }
    
    private enum DataVolumeLevel {
        LIGHT, MODERATE, HEAVY, EXTREME
    }
    
    private enum CapacityLevel {
        LOW, MEDIUM, HIGH
    }
}
