package com.siact.energy.cal.server.core.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.siact.energy.cal.common.util.utils.FormulaUtils;
import com.siact.energy.cal.server.common.cache.MultiLevelCacheTemplate;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公式缓存服务
 * <p>
 * 核心策略：一次性批量加载所需公式，然后在内存中完成所有依赖解析。
 */
@Slf4j
@Service
public class FormulaCacheService {

    @Autowired
    private RuleDetailInstanceService ruleDetailInstanceService;

    // 注入通用的多级缓存模板
    @Autowired
    private MultiLevelCacheTemplate cacheTemplate;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // Redis缓存键前缀
    private static final String FORMULA_KEY_PREFIX = "formula:";
    private static final long CACHE_EXPIRE_HOURS = 24;
    // 特殊值，用于缓存不存在的公式，防止缓存穿透
    private static final String NULL_VALUE = "NULL_FORMULA";

    // 🔥 定义Caffeine缓存实例，仅用于存储，逻辑由模板处理
    private final Cache<String, String> formulaMemoryCache = Caffeine.newBuilder()
            .maximumSize(10000) // 最大缓存1万个公式
            .expireAfterWrite(1, TimeUnit.HOURS) // 写入1小时后过期
            .build();

    /**
     * 获取单个公式 - 带有三级缓存（内存 -> Redis -> DB）
     * 主要供系统其他零散调用使用。
     */
    public String getFormula(String devProperty, Long ruleColId) {
        if (StrUtil.isBlank(devProperty) || ruleColId == null) {
            return null;
        }
        // 定义如何从 devProperty 和 ruleColId 生成 Redis Key
        String redisKey = buildCacheKey(devProperty, ruleColId);

        // 🔥 使用模板一行代码搞定！
        return cacheTemplate.get(
                formulaMemoryCache,
                redisKey,
                devProperty, // sourceKey 就是 devProperty
                Duration.ofHours(24),
                (key) -> ruleDetailInstanceService.getFormula(key, ruleColId) // 定义SourceLoader
        );
    }

    /**
     * 批量获取公式 - 带有三级缓存（内存 -> Redis -> DB）
     * 这是性能的关键路径。
     */
    public Map<String, String> batchGetFormulas(Collection<String> devProperties, Long ruleColId) {
        if (CollUtil.isEmpty(devProperties) || ruleColId == null) {
            return Collections.emptyMap();
        }

        // 定义如何从 devProperty 映射到 Redis Key
        Function<String, String> keyMapper = (prop) -> buildCacheKey(prop, ruleColId);

        // 🔥 使用模板一行代码搞定！
        return cacheTemplate.getAll(
                formulaMemoryCache,
                keyMapper,
                devProperties,
                Duration.ofHours(24),
                (keys) -> ruleDetailInstanceService.batchGetFormulas(keys, ruleColId) // 定义BatchSourceLoader
        );
    }

    /**
     * 采用“一次性批量加载，内存迭代解析”的最高效模型。
     */
    public List<String> getFormulaVarList(List<String> targetPropList, Long ruleColId) {
        if (CollUtil.isEmpty(targetPropList)) {
            return Collections.emptyList();
        }

        log.info("🚀 开始高性能公式变量解析，输入指标数: {}", targetPropList.size());
        long startTime = System.nanoTime();

        Set<String> allDependentProps = new HashSet<>(targetPropList);
        Queue<String> propsToProcess = new LinkedList<>(new HashSet<>(targetPropList));
        Map<String, String> formulaRepo = new HashMap<>(); // 本次请求的公式全量仓库

        // 限制最大迭代次数，防止因意外的循环依赖导致死循环
        int maxIterations = 20;
        int currentIteration = 0;

        while (!propsToProcess.isEmpty() && currentIteration++ < maxIterations) {
            // 从队列中取出当前需要处理的所有指标
            List<String> currentBatch = new ArrayList<>(propsToProcess);
            propsToProcess.clear();

            // 批量获取本轮需要的所有公式，过滤掉已经查过的
            List<String> propsToFetch = currentBatch.stream()
                    .filter(p -> !formulaRepo.containsKey(p))
                    .collect(Collectors.toList());
            if (!propsToFetch.isEmpty()) {
                Map<String, String> fetchedFormulas = batchGetFormulas(propsToFetch, ruleColId);
                formulaRepo.putAll(fetchedFormulas);

                // 将未查到公式的指标也放入仓库，值为null，避免重复查询
                for (String prop : propsToFetch) {
                    formulaRepo.putIfAbsent(prop, null);
                }
            }

            // 在内存中处理依赖，并将新发现的依赖项加入队列
            for (String prop : currentBatch) {
                String formula = formulaRepo.get(prop);
                if (StrUtil.isNotBlank(formula)) {
                    List<String> variables = FormulaUtils.getVarList(formula);
                    for (String var : variables) {
                        // 如果是新发现的指标，则加入总集合和待处理队列
                        if (allDependentProps.add(var)) {
                            propsToProcess.offer(var);
                        }
                    }
                }
            }
        }

        if (currentIteration >= maxIterations && !propsToProcess.isEmpty()) {
            log.warn("公式依赖解析达到最大迭代次数 ({})，可能存在过深的依赖链或循环依赖。队列中仍有 {} 个指标未处理。", maxIterations, propsToProcess.size());
        }

        long duration = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startTime);
        log.info("✅ 高性能公式变量解析完成。输入: {}个, 最终依赖: {}个, 耗时: {}ms", targetPropList.size(), allDependentProps.size(), duration);

        // 注意：这里返回的是所有依赖的集合，并未排序。
        // 计算顺序的保障应由后续的计算引擎（如下次迭代的DAG模型）负责。
        return new ArrayList<>(allDependentProps);
    }



    /**
     * 构建统一的缓存键
     */
    private String buildCacheKey(String devProperty, Long ruleColId) {
        return FORMULA_KEY_PREFIX + ruleColId + ":" + devProperty;
    }
}