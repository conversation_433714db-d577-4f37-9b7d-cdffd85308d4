package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.DataSourceDto;
import com.siact.energy.cal.common.pojo.vo.energycal.DataIntervalQueryVo;
import com.siact.energy.cal.common.pojo.vo.energycal.ValTimes;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.MapToDbUtils;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.common.flow.init.TaoSnit;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.ApiRtsServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.ProcessServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-09
 * @Description: 数据输出算子
 * @Version: 1.0
 */
@Component("OutPutMQTTNode")
@Slf4j
public class OutPutMQTTComponent extends NodeComponent {

    @Resource
    IFlowRelationService flowRelationService;

    @Override
    public void process() {

        log.info("MQTT数据输出组件开始执行");
        //获取上下文
        CalculateContext calculateContext = this.getContextBean("calculateContext");
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置查询失败");
        }
        //获取数据源id
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        DataSourceDto mqttCon = BeanUtil.toBean(collect, DataSourceDto.class);
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = calculateContext.getResultMap();
        //将数据转化为json字符串
        List<DataIntervalQueryVo> entries = new ArrayList<>();
        for (ConcurrentHashMap.Entry<String, ConcurrentHashMap<String, BigDecimal>> outerEntry : resultMap.entrySet()) {
            DataIntervalQueryVo dataIntervalQueryVo = new DataIntervalQueryVo();
            dataIntervalQueryVo.setDataCode(outerEntry.getKey());
            ArrayList<ValTimes> tsValList = new ArrayList<>();
            for (Map.Entry<String, BigDecimal> innerEntry : outerEntry.getValue().entrySet()) {
                String timestamp = innerEntry.getKey();
                BigDecimal propVal = innerEntry.getValue();
                ValTimes valTimes = new ValTimes();
                valTimes.setTimestamp(timestamp);
                valTimes.setPropVal(propVal.toString());
                tsValList.add(valTimes);
            }
            dataIntervalQueryVo.setValTimes(tsValList);
            entries.add(dataIntervalQueryVo);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", entries);

        mqtt(mqttCon, JSON.toJSONString(jsonObject));
        log.info("MQTT数据输出组件执行完成");
    }

    private void mqtt(DataSourceDto mqttCon, String resultJson){
        String broker = mqttCon.getBroker(); // MQTT代理的地址
        String port = mqttCon.getPort();
        String brokerUrl = "tcp://"+broker+":"+port;
        int random = RandomUtil.randomInt(10, 10000);;
        String clientId = "mqtt_java"+random; // 客户端ID
        MemoryPersistence persistence = new MemoryPersistence(); // 设置持久化
        try {
            MqttClient sampleClient = new MqttClient(brokerUrl, clientId, persistence);
            MqttConnectOptions connOpts = new MqttConnectOptions(); // 设置连接选项
            connOpts.setCleanSession(true); // 设置会话清除标志
            log.info("Connecting to broker: {}", brokerUrl);
            connOpts.setUserName(mqttCon.getUserName());
            connOpts.setPassword(mqttCon.getPassword().toCharArray());
            sampleClient.connect(connOpts); // 连接到MQTT代理
            log.info("Connected");
            // 订阅MQTT FX的Topic
            String topic = mqttCon.getTopic(); // 订阅的主题
            int qos = 1;
            MqttMessage message = new MqttMessage(resultJson.getBytes());
            message.setQos(qos);
            sampleClient.publish(topic, message);
            Thread.sleep(5000); // 等待5秒钟

            // 断开连接
            sampleClient.disconnect();

        } catch (MqttException me) {
            log.info("reason " + me.getReasonCode());
            log.info("msg " + me.getMessage());
            log.info("loc " + me.getLocalizedMessage());
            log.info("cause " + me.getCause());
            log.info("excep " + me);
        } catch (Exception e) {
            new Exception("MQTT连接失败", e);
        }
    }
}
